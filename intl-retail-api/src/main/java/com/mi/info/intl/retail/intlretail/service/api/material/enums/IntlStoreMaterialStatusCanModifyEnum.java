package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.function.Supplier;

@Getter
@AllArgsConstructor
public enum IntlStoreMaterialStatusCanModifyEnum {
    //can not modify
    CAN_NOT_MODIFY(0, "can not modify"),
    //404 New product inspection tour-LDU  新品LDU巡检
    CAN_MODIFY(1, "can modify"),
    ;
    
    @EnumValue
    private final Integer code;
    
    private final String desc;
    
    
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static IntlStoreMaterialStatusCanModifyEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (IntlStoreMaterialStatusCanModifyEnum value : IntlStoreMaterialStatusCanModifyEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
