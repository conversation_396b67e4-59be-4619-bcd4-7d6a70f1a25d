package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 阵地巡检信息表_expire 枚举
 *
 * @author: chuang
 * @since: 2025/8/21
 */
@Getter
@AllArgsConstructor
public enum InspectionFlagCompletionEnum {
    NORMAL(0, "默认方式"),
    COVER_DONE(1, "处于物料覆盖弹窗 完成的");
    @EnumValue
    private final Integer code;
    private final String desc;
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static InspectionFlagCompletionEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InspectionFlagCompletionEnum value : InspectionFlagCompletionEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
