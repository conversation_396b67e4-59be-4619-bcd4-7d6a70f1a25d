package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionFlagCompletionEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.model.BasePageRequest;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDocClassDefine;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;


/**
 * 新物料巡检列表请求参数
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class MaterialInspectionReq extends BasePageRequest {
    
    /**
     * 巡检负责人
     */
    private String owner;
    
    /**
     * 阵地名称（支持模糊查询）
     */
    @ApiDocClassDefine(value = "position name 阵地名称（支持模糊查询）")
    private String positionName;
    
    /**
     * 巡检状态
     */
    private List<Integer> inspectionStatus;
    
    private String areaId;
    
    //本地时间戳
    private Long localTimestamp;
    
    
    private List<Integer> businessTypes;
    /**
    *business_code
    */
    String businessCode;
    
    Integer businessType;
    /**
    *阵地巡检信息表 任务状态
    */
    TaskStatusEnum taskStatusEnum;
    /**
    *门店物料状态表 状态
    */
    InspectionFlagCompletionEnum inspectionFlagCompletionEnum;
}
