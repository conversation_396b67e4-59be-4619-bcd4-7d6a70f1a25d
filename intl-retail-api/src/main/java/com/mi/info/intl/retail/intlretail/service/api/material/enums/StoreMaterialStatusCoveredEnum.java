package com.mi.info.intl.retail.intlretail.service.api.material.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Objects;
import java.util.function.Supplier;

@Getter
@AllArgsConstructor
public enum StoreMaterialStatusCoveredEnum implements I18nDesc {
    /**
    * 0:未覆盖
    */
    UNCOVERED(0, () -> T.tr("new.product.inspection.uncovered")),
    /**
    *1:已覆盖
    */
    COVERED(1, () -> T.tr("new.product.inspection.covered")),
    /**
    *2:未收到
    */
    UNRECEIVED(2, () -> T.tr("new.product.inspection.unreceived")),
    /**
    *3:已收到
    */
    RECEIVED(3, () -> T.tr("new.product.inspection.received"));
    
    @EnumValue
    private final Integer code;
    
    private final Supplier<String> i18nDesc;
    
    public static String getNameByCode(Integer code) {
        if (Objects.isNull(code)) {
            return null;
        }
        for (StoreMaterialStatusCoveredEnum value : StoreMaterialStatusCoveredEnum.values()) {
            if (value.getCode().equals(code)) {
                return value.i18nDesc.get();
            }
        }
        return null;
    }
}
