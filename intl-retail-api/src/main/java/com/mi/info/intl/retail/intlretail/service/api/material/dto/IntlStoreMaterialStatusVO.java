package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 返回的门店物料状态表VO
 *
 * @author: chuang
 * @since: 2025/8/14
 */
@Data
public class IntlStoreMaterialStatusVO implements Serializable {
    Long id;
    Integer code;
    String name;
    /**
    *0 未覆盖; 1 已覆盖;2 未收到;3 已收到
    */
    Integer covered;
    /**
    *0 不可修改 1 可修改
    */
    Integer canModify;
}