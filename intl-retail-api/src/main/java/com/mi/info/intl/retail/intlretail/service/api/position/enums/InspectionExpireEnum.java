package com.mi.info.intl.retail.intlretail.service.api.position.enums;

import com.baomidou.mybatisplus.annotation.EnumValue;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.IntlStoreMaterialStatusCanModifyEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 阵地巡检信息表_expire 枚举
 *
 * @author: chuang
 * @since: 2025/8/21
 */
@Getter
@AllArgsConstructor
public enum InspectionExpireEnum {
    NOT_EXPIRE(0, "未过期"),
    EXPIRE(1, "已过期");
    @EnumValue
    private final Integer code;
    private final String desc;
    /**
     * 根据编码获取枚举
     *
     * @param code 编码
     * @return 枚举
     */
    public static InspectionExpireEnum getByCode(Integer code) {
        if (code == null) {
            return null;
        }
        for (InspectionExpireEnum value : InspectionExpireEnum.values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
