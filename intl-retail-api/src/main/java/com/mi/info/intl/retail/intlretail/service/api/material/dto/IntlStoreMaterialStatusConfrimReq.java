package com.mi.info.intl.retail.intlretail.service.api.material.dto;

import com.mi.info.intl.retail.intlretail.service.api.annotation.EnumValidation;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.StoreMaterialStatusCoveredEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.io.Serializable;

/**
 * 门店物料状态表VO 确认list
 *
 * @author: chuang
 * @since: 2025/8/14
 */
@Data
public class IntlStoreMaterialStatusConfrimReq implements Serializable {
    
    
    @NotNull(message = "taskType is required")
    @EnumValidation(type = "code", enumClass = TaskTypeEnum.class, message = "Task type does not match")
    Integer taskType;
    
    String businessCode;
    @NotNull(message = "id is required")
    Long id;
    
    /**
     * 0 未覆盖; 1 已覆盖;2 未收到;3 已收到
     */
    @NotNull(message = "covered is required")
    @EnumValidation(type = "code", enumClass = StoreMaterialStatusCoveredEnum.class, message = "covered does not match")
    Integer covered;
    
    /**
     * 0 不可修改 1 可修改
     */
    Integer canModify;
}