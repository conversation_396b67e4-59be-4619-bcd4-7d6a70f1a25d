package com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.FieldStrategy;
import com.baomidou.mybatisplus.annotation.TableField;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.convert.NewProductStartTimestampToAreaTimeConverter;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.convert.NewProductStatusConverter;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.convert.NewProductTargetTypeConverter;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.io.Serializable;

/**
 * 新品目标返回
 *
 * @author: chuang
 * @since: 2025/8/1
 */
@Slf4j
@Data
@ExcelIgnoreUnannotated
public class NewProductTargetItem implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 品类
     */
//    @ExcelProperty("Category")
    private String category;

    String categoryName;
    /**
     * 系列
     */
//    @ExcelProperty("Series")
    private String series;
    
    /**
     * 产品线
     */
//    @ExcelProperty("Product Line")
    private String productLine;
    
    /**
     *产品线name
     */
//    @TableField(value = "product_line_name", updateStrategy = FieldStrategy.NOT_EMPTY)
    @ExcelProperty("Product Line")
        String productLineName;
    
    
    /**
     * 项目代码
     */
    @ExcelProperty("Project")
    private String project;



    /**
     * 区域
     */
 
    private String region;
    
    /**
     *区域长码
     */
    @ExcelProperty("Region")
    String regionName;


    /**
     * 国家
     */

    private String country;
    
    /**
     *国家长码
     */
    @ExcelProperty("Country")
    String countryName;
    /**
     * 目标类型 1 首销期 2 生命周期
     */
    @ExcelProperty(value = "Period", converter = NewProductTargetTypeConverter.class)
    private String targetType;
    /**
    *
     * 目标类型描述
    */
    String targetTypeDesc;
    /**
     * LDU计划数
     */
    @ExcelProperty("LDU Plan Number")
    private Integer lduPlanCount;

    /**
     * LDU覆盖门店数
     */
    @ExcelProperty("LDU Coverage Stores Target")
    private Integer lduStoreCoverage;

    /**
     * 价签覆盖门店目标数
     */
    @ExcelProperty("Inventory Coverage Stores Target")
    private Integer priceTagCoverageTarget;

    /**
     * Dummy覆盖门店数
     */
    @ExcelProperty("Dummy Coverage Stores Target")
    private Integer dummyStoreCoverage;

    /**
     * POSM覆盖门店数
     */
    @ExcelProperty("POSM Coverage Stores Target")
    private Integer posmStoreCoverage;

    /**
     * 销售激活门店数
     */
    @ExcelProperty("Sales Activations")
    private Integer salesActivation;

    /**
     * 零售可做功目标
     */
    @ExcelProperty("Retail SO Target")
    private Integer retailEffortTarget;

    /**
     * 状态 0 已下发 1 国家已修改
     */
    @ExcelProperty(value = "Status", converter = NewProductStatusConverter.class)
    private Integer status;
    /**
    *状态描述
    */
    String statusDesc;

    /**
     * 起始时间
     */
//    @ExcelProperty(value = "Start Time", converter = NewProductStartTimestampToAreaTimeConverter.class)
    private Long startTime;
    @ExcelProperty(value = "Start Time")
    String startTimeString;
    /**
     * 截止时间
     */
//    @ExcelProperty(value = "End Time", converter = NewProductStartTimestampToAreaTimeConverter.class)
    private Long endTime;
    @ExcelProperty(value = "End Time")
    String endTimeString;
    /**
    *允许修改的-起始时间
    */
    Long canModifyStartTime;
    /**
    *允许修改的-截止时间
    */
    Long canModifyEndTime;

    /**
     * 创建时间戳（秒）
     */
    private Long createdTime;

    /**
     * 创建人
     */
    private String createdBy;

    /**
     * 更新时间戳（秒）
     */
    private Long updatedTime;

    /**
     * 更新人
     */
    private String updatedBy;


}