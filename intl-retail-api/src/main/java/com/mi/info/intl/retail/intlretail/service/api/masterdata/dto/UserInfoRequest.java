package com.mi.info.intl.retail.intlretail.service.api.masterdata.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2025/8/21
 **/
@Data
@Accessors(chain = true)
public class UserInfoRequest implements Serializable {
    private static final long serialVersionUID = -6826346288002527413L;
    /**
     * 搜索关键字,适配前端
     */
    private Long keyWord;
}
