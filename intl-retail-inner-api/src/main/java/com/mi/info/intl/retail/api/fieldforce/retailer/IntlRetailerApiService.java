package com.mi.info.intl.retail.api.fieldforce.retailer;

import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.bean.BasePage;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 零售商内部接口
 *
 * <AUTHOR>
 * @date 2025/07/30
 */
public interface IntlRetailerApiService {

    /**
     * 按国家代码获取零售商列表
     *
     * @param country 国家代码
     * @return {@link List }<{@link IntlRetailerDTO }>
     */
    List<IntlRetailerDTO> getRetailerListByCountryCode(CountryDTO country);

    /**
     * 按国家代码获取零售商列表
     *
     * @param countryCode 国家代码
     * @return {@link List }<{@link IntlRetailerDTO }>
     */
    List<IntlRetailerDTO> getRetailerListByCountryCode(String countryCode);

    /**
     * 通过零售商代码获取零售商
     *
     * @param dto DTO
     * @return {@link Optional }<{@link IntlRetailerDTO }>
     */
    Optional<IntlRetailerDTO> getRetailerByRetailerCode(IntlPositionDTO dto);


    IntlRetailerDTO getRetailerByRetailerId(String retailerId);

    Map<String, IntlRetailerDTO> batchGetRetailerByRetailerIds(List<String> retailerIds);

    /**
     * 分页获取零售商列表
     *
     * @param req req
     * @return {@link List }<{@link IntlRetailerDTO }>
     */
    List<IntlRetailerDTO> getRetailerListByPage(BasePage req);

    /**
     * 获取所有增量零售商
     *
     * @return {@link List }<{@link IntlRetailerDTO }>
     */
    List<IntlRetailerDTO> getIncrementRetailer();

    /**
     * 增量
     */
    void updateIncrementRetailer();

    Map<String, IntlRetailerDTO> getRetailersByRetailerCodes(List<String> retailerCodes);


}
