package com.mi.info.intl.retail.api.front.dto;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class RmsPositionInfoRes implements Serializable {


    private static final long serialVersionUID = 8447111045719350849L;
    /**
     *
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 唯一标识
     */
    private String positionId;

    /**
     * 阵地代码
     */
    private String code;

    /**
     * 阵地名称
     */
    private String name;

    /**
     * 门店名称
     */
    private String storeId;

    /**
     * 门店名称标签
     */
    private String storeName;

    /**
     * 运营状态
     */
    private Integer state;

    /**
     * 运营状态标签
     */
    private String stateName;


    /**
     * 零售商代码
     */
    private String retailerId;

    /**
     * 零售商代码标签
     */
    private String retailerName;

    /**
     * 渠道类型
     */
    private Integer channelType;

    /**
     * 渠道类型标签
     */
    private String channelTypeName;

    /**
     * 阵地类型
     */
    private Integer type;

    /**
     * 阵地类型标签
     */
    private String typeName;

    /**
     * 门店等级
     */
    private Integer level;

    /**
     * 门店等级标签
     */
    private String levelName;


    /**
     * 地址
     */
    private String address;

    /**
     * 是否可用
     */
    private Integer stateCode;

    /**
     * 创建时间
     */
    private String createdOn;

}
