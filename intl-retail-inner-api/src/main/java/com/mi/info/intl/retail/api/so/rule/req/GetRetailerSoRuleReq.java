package com.mi.info.intl.retail.api.so.rule.req;

import java.io.Serializable;

import lombok.Getter;
import lombok.Setter;

/**
 * 获取零售商对应的销售规则请求参数
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Getter
@Setter
public class GetRetailerSoRuleReq implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 国家编码
     */
    private String countryCode;

    /**
     * 阵地编码
     */
    private String positionCode;

    /**
     * 类型, 1-so规则, 2-ldu规则, 后续可以扩展
     */
    private int type;

    /**
     * 用户id
     */
    private String userId;

    /**
     * miId
     */
    private String miId;

    /**
     * 用户职称ID
     */
    private String userTitle;

    /**
     * 店铺编码
     */
    private String storeCode;
}
