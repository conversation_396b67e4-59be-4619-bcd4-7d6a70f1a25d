package com.mi.info.intl.retail.api.front.store;

import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;

import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 */
public interface RmsStoreService {

    Optional<RmsStoreInfoDto> getStoreInfoByStoreCode(String storeCode);

    Optional<RmsPositionInfoRes> getPositionIfoByPositionCode(String positionCode);

    /**
     * 根据用户miId获取关联的门店ID列表
     *
     * @param miId 用户miId
     * @return 门店ID列表
     */
    List<Integer> getUserStoreIdsByMiId(Long miId);

    Map<String, RmsStoreInfoDto> getStoreInfoByStoreCodes(List<String> storeCodes);

    Map<String, RmsStoreInfoDto> batchGetStoreInfoByStoreCode(List<String> storeCodes);

    Map<String, RmsPositionInfoRes> batchGetPositionIfoByPositionCode(List<String> positionCodes);
}
