package com.mi.info.intl.retail.cooperation.task.inspection.impl;

import java.util.List;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.fieldforce.user.IntlRmsUserApiService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.RmsUserReadMapper;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.dto.IntlRmsUserDTO;
import com.mi.info.intl.retail.utils.redis.RedisKey;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import com.mi.info.intl.retail.utils.redis.RedisClient;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class IntlRmsUserServiceImpl
        extends ServiceImpl<RmsUserReadMapper, IntlRmsUser>
        implements IntlRmsUserService, IntlRmsUserApiService {

    @Autowired
    private RmsUserReadMapper rmsUserReadMapper;

    @Autowired
    private RedisClient redisClient;

    @Override
    public IntlRmsUserDto getIntlRmsUserByDomainName(String domainName) {
        if (StringUtils.isEmpty(domainName)) {
            return null;
        }
        try {
            IntlRmsUser intlRmsUser = rmsUserReadMapper.selectByDomainName(domainName);
            if (intlRmsUser == null) {
                return null;
            }
            return IntlRmsUserDto.builder()
                    .rmsUserid(intlRmsUser.getRmsUserid())
                    .code(intlRmsUser.getCode())
                    .domainName(intlRmsUser.getDomainName())
                    .englishName(intlRmsUser.getEnglishName())
                    .countryId(intlRmsUser.getCountryId())
                    .countryName(intlRmsUser.getCountryName())
                    .jobId(intlRmsUser.getJobId())
                    .jobName(intlRmsUser.getJobName())
                    .email(intlRmsUser.getEmail())
                    .mobile(intlRmsUser.getMobile())
                    .miId(intlRmsUser.getMiId())
                    .managerId(intlRmsUser.getManagerId())
                    .managerName(intlRmsUser.getManagerName())
                    .virtualMiId(intlRmsUser.getVirtualMiId())
                    .languageId(intlRmsUser.getLanguageId())
                    .languageName(intlRmsUser.getLanguageName())
                    .isDisabled(intlRmsUser.getIsDisabled())
                    .build();
        } catch (Exception e) {
            log.error("getIntlRmsUserByDomainName error: ", e);
        }
        return null;
    }

    @Override
    public List<IntlRmsUserDTO> getRmsUserByMiIds(List<Long> miIdList) {
        if (miIdList == null || miIdList.isEmpty()) {
            return null;
        }

        List<IntlRmsUserDTO> intlRmsUserDTOList = new java.util.ArrayList<>();
        List<IntlRmsUser> rmsUserList = rmsUserReadMapper
                .selectList(Wrappers.<IntlRmsUser>lambdaQuery().in(IntlRmsUser::getMiId, miIdList));

        if (rmsUserList.isEmpty()) {
            return intlRmsUserDTOList;
        }

        for (IntlRmsUser intlRmsUser : rmsUserList) {
            IntlRmsUserDTO rmsUserDTO = convertUser(intlRmsUser);
            intlRmsUserDTOList.add(rmsUserDTO);
        }
        return intlRmsUserDTOList;

    }

    @Override
    public IntlRmsUserDTO getRmsUserByUniqueName(String uniqueName) {
        RedisKey redisKey = RedisKeyEnum.RMS_USER_CACHE.get(uniqueName);
        IntlRmsUserDTO userCache = redisClient.getObj(redisKey, IntlRmsUserDTO.class);
        if (Objects.nonNull(userCache)) {
            return userCache;
        }
        IntlRmsUser intlRmsUser =
                this.getOne(Wrappers.<IntlRmsUser>lambdaQuery().eq(IntlRmsUser::getDomainName, uniqueName));
        IntlRmsUserDTO rmsUser = convertUser(intlRmsUser);
        redisClient.set(redisKey, JSON.toJSONString(rmsUser));
        return rmsUser;
    }

    private IntlRmsUserDTO convertUser(IntlRmsUser intlRmsUser) {
        return IntlRmsUserDTO.builder()
                .rmsUserid(intlRmsUser.getRmsUserid())
                .code(intlRmsUser.getCode())
                .domainName(intlRmsUser.getDomainName())
                .englishName(intlRmsUser.getEnglishName())
                .countryId(intlRmsUser.getCountryId())
                .countryName(intlRmsUser.getCountryName())
                .jobId(intlRmsUser.getJobId())
                .jobName(intlRmsUser.getJobName())
                .email(intlRmsUser.getEmail())
                .mobile(intlRmsUser.getMobile())
                .miId(intlRmsUser.getMiId())
                .managerId(intlRmsUser.getManagerId())
                .managerName(intlRmsUser.getManagerName())
                .virtualMiId(intlRmsUser.getVirtualMiId())
                .languageId(intlRmsUser.getLanguageId())
                .languageName(intlRmsUser.getLanguageName())
                .languageCode(intlRmsUser.getLanguageCode())
                .isDisabled(intlRmsUser.getIsDisabled())
                .build();
    }
}
