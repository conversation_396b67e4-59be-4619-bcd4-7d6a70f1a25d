# 通用任务触发能力

## 概述

通用任务触发能力是一个封装了任务调度功能的通用组件，位于 `intl-retail-cooperation/downloadCenter` 目录下。该能力提供了标准化的任务触发接口，支持不同业务模块的任务调度需求。

**重要更新**：所有任务调度相关的依赖已迁移到 `intl-retail-cooperation` 模块，调用方只需引入该模块即可使用所有功能。

## 架构设计

### 核心组件

1. **JobTriggerService** - 任务触发服务接口
2. **JobTriggerServiceImpl** - 任务触发服务实现
3. **JobTriggerFactory** - 任务触发工厂，支持不同业务模块配置
4. **JobConfig** - 通用任务配置类
5. **BusinessModuleEnum** - 业务模块枚举
6. **JobTriggerAutoConfiguration** - 自动配置类

### 数据模型

1. **JobTriggerRequest** - 任务触发请求DTO
2. **JobTriggerResponse** - 任务触发响应DTO

## 使用方法

### 1. 添加依赖

只需要在调用方的 `pom.xml` 中添加一个依赖：

```xml
<dependency>
    <groupId>com.mi.info.intl.retail</groupId>
    <artifactId>intl-retail-cooperation</artifactId>
    <version>${project.version}</version>
</dependency>
```

该依赖包含了所有必要的任务调度相关依赖：
- nr-job-api & nr-job-core
- hutool-all
- commons-lang3 & commons-collections4
- spring-boot-starter
- dubbo-spring-boot-starter
- mybatis-plus相关
- rocketmq-spring-boot-starter
- 其他工具类依赖

### 2. 基本使用

```java
@Resource
private JobTriggerService jobTriggerService;

// 构建任务请求
JobTriggerRequest request = JobTriggerRequest.builder()
    .jobKey("yourJobKey")
    .owner("owner")
    .taskParam("taskParam")
    .taskDesc("任务描述")
    .taskName("任务名称")
    .projectId(30L)
    .projectName("项目名称")
    .businessModule("ldu")
    .build();

// 触发任务
JobTriggerResponse response = jobTriggerService.triggerJob(request);
```

### 3. 使用工厂模式（推荐）

```java
@Resource
private JobTriggerFactory jobTriggerFactory;

// 构建任务请求（模块配置已自动注册）
JobTriggerRequest request = jobTriggerFactory.buildRequest(
    "ldu",           // 模块名称
    "jobKey",        // 任务Key
    "owner",         // 负责人
    "taskParam",     // 任务参数
    "任务描述",        // 任务描述
    "任务名称"         // 任务名称
);

// 触发任务
JobTriggerResponse response = jobTriggerService.triggerJob(request);
```

### 4. 业务模块示例

参考 `LduJobTriggerExample` 类，展示了如何在LDU模块中使用通用任务触发能力。

## 配置说明

### 配置文件

在 `application.yml` 中添加以下配置：

```yaml
job:
  addresses: "your-job-admin-addresses"
  accessToken: "your-access-token"
  appname: "your-app-name"
  port: 9991
  logPath: "/path/to/logs"
  logRetentionDays: 30
  dubboGroup: "your-dubbo-group"
  timeout: 3000
  check: false
  trigger:
    enabled: true  # 启用自动配置（默认true）
```

### 自动配置

系统会自动注册以下业务模块的配置：
- LDU模块 (projectId: 30)
- 销售模块 (projectId: 31)
- 考勤模块 (projectId: 32)
- 零售商模块 (projectId: 33)
- 合作模块 (projectId: 34)

如需自定义配置，可以通过以下方式：

```java
@PostConstruct
public void initCustomConfig() {
    JobTriggerFactory.BusinessModuleConfig config = 
        new JobTriggerFactory.BusinessModuleConfig(
            projectId,      // 项目ID
            projectName,    // 项目名称
            description     // 描述
        );
    jobTriggerFactory.registerModuleConfig("customModule", config);
}
```

## 迁移指南

### 从原有NrJobGateway迁移

1. **添加依赖**：只需添加 `intl-retail-cooperation` 依赖
2. **保持向后兼容**：使用 `NrJobGatewayAdapter` 适配器
3. **逐步迁移**：将原有代码逐步迁移到新的通用能力
4. **移除冗余依赖**：移除原有的任务调度相关依赖

### 迁移步骤

1. 在 `pom.xml` 中添加依赖：
```xml
<dependency>
    <groupId>com.mi.info.intl.retail</groupId>
    <artifactId>intl-retail-cooperation</artifactId>
    <version>${project.version}</version>
</dependency>
```

2. 移除原有的任务调度相关依赖（如nr-job-api、hutool等）
3. 使用新的JobTriggerService替换原有调用
4. 移除原有的NrJobGatewayImpl依赖

## 扩展说明

### 添加新的业务模块

1. 在 `BusinessModuleEnum` 中添加新的模块枚举
2. 在 `JobTriggerAutoConfiguration` 中添加自动注册逻辑
3. 实现具体的业务逻辑

### 自定义配置

可以通过继承 `JobConfig` 或使用 `JobTriggerFactory` 来支持更复杂的配置需求。

## 依赖说明

### 已包含的依赖

- **任务调度**：nr-job-api, nr-job-core
- **工具类**：hutool-all, commons-lang3, commons-collections4
- **Spring相关**：spring-boot-starter, spring-boot-configuration-processor
- **RPC**：dubbo-spring-boot-starter
- **数据库**：mybatis-plus相关
- **消息队列**：rocketmq-spring-boot-starter
- **其他工具**：orika-core, commons-util等

### 调用方无需再引入的依赖

- cn.hutool:hutool-all
- com.xiaomi.nr:nr-job-api
- com.xiaomi.nr:nr-job-core
- org.apache.commons:commons-lang3
- org.apache.commons:commons-collections4
- org.apache.rocketmq:rocketmq-spring-boot
- 其他任务调度相关的依赖

## 注意事项

1. **线程安全**：JobTriggerFactory 使用 ConcurrentHashMap 保证线程安全
2. **异常处理**：所有异常都会被捕获并返回标准错误响应
3. **日志记录**：所有关键操作都会记录详细日志
4. **参数校验**：会对关键参数进行校验
5. **自动配置**：模块配置会自动注册，无需手动配置

## 版本历史

- v1.0.0 (2024-12-19): 初始版本，提供基础的任务触发能力
- v1.1.0 (2024-12-19): 依赖迁移版本，将所有任务调度相关依赖迁移到cooperation模块
- 支持多业务模块配置
- 提供工厂模式和适配器模式
- 保持向后兼容性
- 简化调用方依赖管理 