package com.mi.info.intl.retail.cooperation.downloadcenter.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * 通用任务配置类
 * 支持不同业务模块的任务调度配置
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Configuration
@Data
public class JobProperties {

    @Value("${proretail.project.id:}")
    private int projectId;

    @Value("${proretail.project.name}")
    private String projectName;

    @Value("${job.admin.addresses}")
    private String addresses;


    @Value("${job.accessToken}")
    private String accessToken;

    @Value("${job.executor.appname}")
    private String appname;

    @Value("${job.executor.ip}")
    private String executorIp;

    @Value("${job.executor.port}")
    private int port;

    @Value("${job.executor.logpath}")
    private String logPath;

    @Value("${job.executor.logretentiondays}")
    private int logretentiondays;
} 