package com.mi.info.intl.retail.cooperation.task.dto.dto;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.mi.info.intl.retail.cooperation.task.infra.entity.InspectionAction;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/6/6
 **/
@Data
@ExcelIgnoreUnannotated
public class InspectionTaskConfDTO {

    /**
     * 巡检任务ID
     */
    private Integer id;

    /**
     * 是否新品大促期间
     */
    private Boolean isNewProductPromotion;

    /**
     * 区域
     */
    private String region;

    /**
     * 用户职位
     */
    private String userTitle;

    /**
     * 国家
     */
    private String country;

    /**
     * S级巡检频次
     */
    private String sStoreInspectionFrequency;

    /**
     * a级门店巡检频次,格式 x月x次
     */
    private String aStoreInspectionFrequency;

    /**
     * b级门店巡检频次,格式 x月x次
     */
    private String bStoreInspectionFrequency;

    /**
     * c级门店巡检频次,格式 x月x次
     */
    private String cStoreInspectionFrequency;

    /**
     * d级门店巡检频次,格式 x月x次
     */
    private String dStoreInspectionFrequency;

    /**
     * 有促阵地巡检动作
     */
    private List<InspectionAction> hasPromoterFrontInspectionAction;

    /**
     * 无促阵地巡检动作
     */
    private List<InspectionAction> noPromoterFrontInspectionAction;

    /**
     * 有促售点巡检动作
     */
    private List<InspectionAction> hasPromoterPosInspectionAction;

    /**
     * 无售点地巡检动作
     */
    private List<InspectionAction> noPromoterPosInspectionAction;

    /**
     * 阵地巡检时长分钟
     */
    private Integer frontInspectionTime;

    /**
     * POS巡检时长分钟
     */
    private Integer posInspectionTime;

    /**
     * 巡检任务创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 巡检任务更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 新品任务开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date promotionStartTime;

    /**
     * 新品任务结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date promotionEndTime;

    /**
     * 状态
     */
    private Boolean isDisabled;
}
