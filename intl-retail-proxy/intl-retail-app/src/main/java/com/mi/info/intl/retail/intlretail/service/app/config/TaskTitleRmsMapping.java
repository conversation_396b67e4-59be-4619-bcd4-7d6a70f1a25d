package com.mi.info.intl.retail.intlretail.service.app.config;

import com.alibaba.fastjson.JSON;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.alibaba.nacos.spring.context.annotation.config.NacosPropertySource;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.core.config.TaskTitleRmsMappingDTO;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;

@Component
@NacosPropertySource(dataId = "task_title_check_rms_mapping", autoRefreshed = true)
public class TaskTitleRmsMapping {
    
    @NacosValue(value = "${task.title.rms.mapping.id:}", autoRefreshed = true)
    private String taskTitleRmsMapping;
    
    public List<TaskTitleRmsMappingDTO> getTaskTitleRmsMappingList() {
        if (StringUtils.hasText(taskTitleRmsMapping)) {
            return JSON.parseArray(taskTitleRmsMapping, TaskTitleRmsMappingDTO.class);
        }
        return Lists.newArrayList();
    }
}
