package com.mi.info.intl.retail.intlretail.service.app.proxy.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Data
public class MenuStatusCacheDto implements Serializable {
    @JsonProperty("code")
    private int code;
    @JsonProperty("message")
    private String message;
    @JsonProperty("data")
    private List<MenuStatusList> data;

    @Data
    public static class MenuStatusList implements Serializable {
        @JsonProperty("MenuStoreJobVisible")
        private Boolean menuStoreJobVisible;

        @JsonProperty("MenuStoreJobFinish")
        private Boolean menuStoreJobFinish;

        @JsonProperty("ScreenName")
        private String screenName;

        @JsonProperty("Label")
        private String label;

        @JsonProperty("statusCount")
        private Integer statusCount;

        @JsonProperty("Type")
        private Integer type;

        @JsonProperty("Executor")
        private Integer executor;

        @JsonProperty("InStoreJob")
        private Boolean inStoreJob;

        @JsonProperty("ItemRequired")
        private Boolean itemRequired;
    }
}
