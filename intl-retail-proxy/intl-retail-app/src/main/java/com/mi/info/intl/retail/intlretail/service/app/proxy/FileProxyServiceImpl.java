package com.mi.info.intl.retail.intlretail.service.app.proxy;

import com.mi.info.intl.retail.core.exception.RmsApiException;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.proxy.FileProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.FileResultDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;

@Slf4j
@Service
public class FileProxyServiceImpl implements FileProxyService {

    @Autowired
    FdsService fdsService;

    @Value("${config-url.fds}")
    private String fdsUrl;
    
    @Value("${config-url.fdsCdn}")
    private String fdsCdn;

    static final String BEARER = "Bearer ";

    @Override
    public FileResultDto upload(String fileName, MultipartFile file, Long timestamp) {
        if (file == null || file.isEmpty()) {
            throw new RmsApiException(400, "file is not be empty");
        }
        FileResultDto resultDto = new FileResultDto();
        UUID uuid = UUID.randomUUID();
        final String localFileName = "/tmp/init-retail"
                + uuid + "-" + file.getOriginalFilename();
        final File localFile = FileUtils.getFile(localFileName);
        try {
            file.transferTo(localFile);
            if (!localFile.exists()) {
                throw new RmsApiException(400, "file upload fail");
            }
            fileName = timestamp + "_" + uuid + "_" + fileName;
            FdsUploadResult uploadResult = fdsService.upload(fileName, localFile, true);

            resultDto.setFileName(fileName);
            resultDto.setUri(uploadResult.getUrl());
            resultDto.setCdnUri(uploadResult.getCdnUrl());

        } catch (IOException e) {
            throw new RmsApiException(500, e.getMessage(), e);
        }
        return resultDto;
    }

    @Override
    public FileResultDto uploadFile(String fileName, File file, Long timestamp, UUID uuid) {
        if (file == null) {
            throw new RmsApiException(400, "file is not be empty");
        }
        FileResultDto resultDto = new FileResultDto();
        if (!file.exists()) {
            throw new RmsApiException(400, "file upload fail");
        }
        fileName = timestamp + "_" + uuid + "_" + fileName;
        FdsUploadResult uploadResult = fdsService.upload(fileName, file, true);
        resultDto.setFileName(fileName);
        resultDto.setUri(uploadResult.getUrl());
        resultDto.setCdnUri(uploadResult.getCdnUrl());
        return resultDto;
    }

    @Override
    public FileResultDto upload(String url, String userToken) {
        if (StringUtils.isBlank(url)) {
            throw new RmsApiException(400, "file url can not be empty");
        }

        if (url.startsWith(fdsUrl) || url.startsWith(fdsCdn)) {
            FileResultDto resultDto = new FileResultDto(getFileNameByUrl(url), url);
            resultDto.setCdnUri(url);
            return resultDto;
        }

        ResponseEntity<byte[]> response = fetchFileFromUrl(url, userToken);
        try {
            if (!response.getStatusCode().is2xxSuccessful() || response.getBody() == null) {
                throw new RmsApiException(500, "download" + url + "fail: " + response.getHeaders());
            }

            File file = createMultipartFile(response);
            String fileName = getFileNameFromRmsResponse(response);

            try {
                return this.uploadFile(fileName, file, System.currentTimeMillis(), UUID.randomUUID());
            } finally {
                if (file.exists()) {
                    if (!file.delete()) {
                        log.error("file delete error");
                    }
                }
            }
        } catch (IOException e) {
            throw new RmsApiException(500, e.getMessage(), e);
        }

    }

    @Override
    public ResponseEntity<ByteArrayResource> httpForFile(String url, String token, HttpMethod method) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", BEARER + token);

        HttpEntity<Object> entity = new HttpEntity<>(null, headers);
        ResponseEntity<byte[]> response = new RestTemplate().exchange(url, method, entity, byte[].class);

        if (response.getStatusCode().is2xxSuccessful() && response.getBody() != null) {
            byte[] fileBytes = response.getBody();
            if (Objects.isNull(fileBytes)) {
                throw new RmsApiException(500, "get url file is null:" + url);
            }
            String fileName = getFileNameFromResponse(response);
            ByteArrayResource resource = new ByteArrayResource(fileBytes);

            HttpHeaders responseHeaders = new HttpHeaders();
            responseHeaders.setContentType(MediaType.APPLICATION_OCTET_STREAM);
            responseHeaders.setContentDispositionFormData("attachment", fileName);

            return new ResponseEntity<>(resource, responseHeaders, HttpStatus.OK);
        }
        return null;
    }


    private String getFileNameFromResponse(ResponseEntity<byte[]> response) {
        return Optional.ofNullable(response.getHeaders().get("X-Ms-File-Name"))
                .filter(mimetype -> !mimetype.isEmpty())
                .map(mimetype -> mimetype.get(0))
                .orElse("unknown");
    }

    private String getFileTypeFromResponse(ResponseEntity<byte[]> response) {
        return Optional.ofNullable(response.getHeaders().get("Mimetype"))
                .filter(mimetype -> !mimetype.isEmpty())
                .map(mimetype -> mimetype.get(0))
                .orElse("unknown");
    }

    private String getFileExtension(String fileName) {
        int dotIndex = fileName.lastIndexOf('.');
        return (dotIndex == -1) ? "" : fileName.substring(dotIndex);
    }

    private ResponseEntity<byte[]> fetchFileFromUrl(String url, String userToken) {
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", BEARER + userToken);
        HttpEntity<Object> entity = new HttpEntity<>(null, headers);
        return new RestTemplate().exchange(url, HttpMethod.GET, entity, byte[].class);
    }

    private File createMultipartFile(ResponseEntity<byte[]> response) throws IOException {
        byte[] fileBytes = response.getBody();
        String fileName = getFileNameFromRmsResponse(response);
        UUID uuid = UUID.randomUUID();
        File file = new File("/tmp/init-retail" + uuid + "-" + fileName);
        FileUtils.writeByteArrayToFile(file, fileBytes);
        return file;
    }

    private String getFileNameFromRmsResponse(ResponseEntity<byte[]> response) {
        return Optional.ofNullable(response.getHeaders().get("X-Ms-File-Name"))
                .filter(mimetype -> !mimetype.isEmpty())
                .map(mimetype -> mimetype.get(0))
                .orElse("unknown");
    }

    private String getFileTypeFromRmsResponse(ResponseEntity<byte[]> response) {
        return Optional.ofNullable(response.getHeaders().get("Mimetype"))
                .filter(mimetype -> !mimetype.isEmpty())
                .map(mimetype -> mimetype.get(0))
                .orElse("unknown");
    }

    private String getFileNameByUrl(String fileName) {
        int slashIndex = fileName.lastIndexOf('/');
        if (slashIndex == -1) {
            return fileName; // 如果没有斜杠，返回整个字符串
        }
        return fileName.substring(slashIndex + 1); // 返回斜杠后的部分
    }

}
