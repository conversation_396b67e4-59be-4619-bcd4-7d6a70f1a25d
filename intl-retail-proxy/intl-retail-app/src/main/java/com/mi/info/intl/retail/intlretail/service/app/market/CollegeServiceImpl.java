package com.mi.info.intl.retail.intlretail.service.app.market;

import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.intlretail.service.api.market.CollegeService;
import com.xiaomi.nrme.market.api.rpc.service.RetailCollegeProvider;
import com.xiaomi.nrme.market.api.rpc.service.intl.IntlAppRpcService;
import com.xiaomi.nrme.market.api.rpc.service.intl.IntlArticleWebProvider;
import com.xiaomi.nrme.market.api.rpc.service.intl.IntlCategoryRpcService;
import com.xiaomi.nrme.market.api.vo.article.*;
import com.xiaomi.nrme.market.api.vo.article.intl.pojo.IntlArticleVO;
import com.xiaomi.nrme.market.api.vo.article.intl.req.ArticleSearchEsPageableReq;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlAppArticlePageableVO;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlArticleViewProgressReqVO;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlCollegeArticleReq;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlRetailCollegeLikeRequest;
import com.xiaomi.nrme.market.api.vo.article.intl.resp.IntlCollegeArticleResp;
import com.xiaomi.nrme.market.api.vo.article.intl.resp.IntlPageResp;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryAppFirstPageRespVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryAppSecondVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlCategoryPageableReqVO;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * - <AUTHOR>
 * - @date 2025/6/24
 * - @description:
 * -
 **/
@Service
@Slf4j
public class CollegeServiceImpl implements CollegeService {
    @DubboReference(group = "${college.dubbo.group:}", interfaceClass = IntlAppRpcService.class, check = false)
    private IntlAppRpcService appRpcService;

    @DubboReference(group = "${college.dubbo.group:}", interfaceClass = IntlArticleWebProvider.class, check = false)
    private IntlArticleWebProvider intlArticleWebProvider;

    @DubboReference(group = "${college.dubbo.group:}", interfaceClass = IntlCategoryRpcService.class, check = false)
    private IntlCategoryRpcService categoryRpcService;

    @DubboReference(group = "${college.dubbo.group:}", interfaceClass = RetailCollegeProvider.class, check = false)
    private RetailCollegeProvider retailCollegeProvider;

    @Override
    public List<IntlCategoryAppFirstPageRespVO> appFirstCategoryList(IntlCategoryPageableReqVO pageableReqVO) {
        log.info("appFirstCategoryList:{}", JSON.toJSON(pageableReqVO));
        Result<List<IntlCategoryAppFirstPageRespVO>> result = categoryRpcService.appFirstCategoryList(pageableReqVO);

        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get appFirstCategoryList error");
        }
        return result.getData();
    }

    @Override
    public List<IntlCategoryAppSecondVO> intlAppSecondCategoryList(IntlCategoryPageableReqVO pageableReqVO) {
        log.info("intlAppSecondCategoryList:{}", JSON.toJSON(pageableReqVO));
        Result<List<IntlCategoryAppSecondVO>> result = categoryRpcService.intlAppSecondCategoryList(pageableReqVO);

        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get intlAppSecondCategoryList error");
        }
        return result.getData();
    }

    @Override
    public IntlPageResp<IntlArticleVO> pageList(IntlAppArticlePageableVO articlePageableVO) {
        Result<IntlPageResp<IntlArticleVO>> result = appRpcService.pageList(articlePageableVO);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get article pageList error");
        }
        return result.getData();
    }

    @Override
    public Long toggleLike(IntlRetailCollegeLikeRequest retailCollegeLikeRequest) {
        Result<Long> result = appRpcService.toggleLike(retailCollegeLikeRequest);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("toggleLike error");
        }
        return result.getData();
    }

    @Override
    public Void syncArticleProgress(IntlArticleViewProgressReqVO progressReqVO) {
        Result<Void> result = appRpcService.syncArticleProgress(progressReqVO);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("syncArticleProgress error");
        }
        return result.getData();
    }

    @Override
    public PageResp<IntlArticleVO> fullTextExplore(ArticleSearchEsPageableReq articleSearchEsPageableReq) {
        Result<PageResp<IntlArticleVO>> result = intlArticleWebProvider.fullTextExplore(articleSearchEsPageableReq);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get fullTextExplore error");
        }
        return result.getData();
    }

    @Override
    public IntlCollegeArticleResp detail(IntlCollegeArticleReq intlCollegeArticleReq) {
        Result<IntlCollegeArticleResp> result = appRpcService.detail(intlCollegeArticleReq);
        if (result == null || result.getCode() != 0) {
            throw new RuntimeException("get article detail error");
        }
        return result.getData();
    }
}
