spring: 
  application: 
    name: intl-retail
    # 默认根据环境开启相应 profile 配置， 即本地默认开启 dev profile。详见 https://docs.mit.mi.com/mit-commons-java/mit-starter/env-profile
  profiles:
    include: rms, dubbo, mq
    active: dev
  servlet:
    multipart:
      max-file-size: 1024MB
      max-request-size: 1024MB

dubbo:
  application:
    name: intl-retail
  scan: 
    base-packages: com.mi.info.intl.retail.intlretail
  registry: 
    address: ${nacos.address}
  protocol: 
    id: dubbo
    name: dubbo
    port: -1
  provider: 
    retries: 1
    validation: false
    filter: errorcodeExceptionFilter, -exception
    version: ${user.name}
  consumer:
    retries: 3
    timeout: 5000
    version: ${user.name}
    check: false
server: 
  port: 10020

comb:
  x5:
    request-secrets:
      - app-id: xm_test
#        app-key@kc-sid: keycenter-test  # app-key原文: 18f34bde4bb73c10f8ba1b02e3cf3efd
        app-key: GDAfiYp0JfVuGHVDyICPBjypUOx/BLwoxxDPfdiZWqOpQveexQVCcwhZZY4AyOcEOloYEo+OHExnG0Gso0VnL9f7qkPz/xgQ8mZTe0l2S9OZKwJbSSID2RgUP4+lS5TRex8o3Jta4fgXX+A+RW4A
        allow-methods: findUserList

