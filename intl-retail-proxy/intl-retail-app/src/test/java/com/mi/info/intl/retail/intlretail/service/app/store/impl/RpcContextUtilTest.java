package com.mi.info.intl.retail.intlretail.service.app.store.impl;

import com.mi.info.intl.retail.utils.RpcContextUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * RpcContextUtil测试类
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
public class RpcContextUtilTest {

    @Test
    public void testGetCurrentAreaId() {
        // 测试获取当前地区ID的方法
        String areaId = RpcContextUtil.getCurrentAreaId();
        // 在没有RPC上下文的情况下应该返回null
        assertNull(areaId);
    }

    @Test
    public void testGetCurrentLanguage() {
        // 测试获取当前语言的方法
        String language = RpcContextUtil.getCurrentLanguage();
        // 在没有RPC上下文的情况下应该返回null
        assertNull(language);
    }

    @Test
    public void testIsRpcContextValid() {
        // 测试检查RPC上下文是否有效的方法
        boolean isValid = RpcContextUtil.isRpcContextValid();
        // 在没有RPC上下文的情况下应该返回false
        assertFalse(isValid);
    }

    @Test
    public void testGetAreaIdWithParameter() {
        // 测试原有的带参数方法
        String areaId = RpcContextUtil.getAreaId("mone-retail-area-for-global:ID");
        assertEquals("ID", areaId);
    }

    @Test
    public void testGetLanguageWithParameter() {
        // 测试原有的带参数方法
        String language = RpcContextUtil.getLanguage("mone-retail-language-for-global:en-US");
        assertEquals("en-US", language);
    }
} 