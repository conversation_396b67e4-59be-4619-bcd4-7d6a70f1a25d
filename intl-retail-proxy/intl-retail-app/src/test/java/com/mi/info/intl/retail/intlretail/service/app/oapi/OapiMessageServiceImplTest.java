package com.mi.info.intl.retail.intlretail.service.app.oapi;

import com.lark.oapi.Client;
import com.lark.oapi.core.response.RawResponse;
import com.lark.oapi.service.im.ImService;
import com.lark.oapi.service.im.v1.V1;
import com.lark.oapi.service.im.v1.model.CreateMessageReq;
import com.lark.oapi.service.im.v1.model.CreateMessageResp;
import com.lark.oapi.service.im.v1.model.ListChatReq;
import com.lark.oapi.service.im.v1.model.ListChatResp;
import com.lark.oapi.service.im.v1.resource.Chat;
import com.lark.oapi.service.im.v1.resource.Message;

import com.mi.info.intl.retail.intlretail.service.api.oapi.dto.MissingRuleAlertDTO;
import com.mi.info.intl.retail.intlretail.service.app.config.OapiNacosConfig;
import com.mi.info.intl.retail.intlretail.service.app.oapi.impl.OapiMessageServiceImpl;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.anyMap;
import static org.mockito.Mockito.anyString;
import static org.mockito.Mockito.doReturn;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OapiMessageServiceImplTest {

    @InjectMocks
    private OapiMessageServiceImpl oapiMessageService;

    @Mock
    private Client client;

    @Mock
    private OapiNacosConfig oapiNacosConfig;

    @Mock
    private ImService imService;

    @Mock
    private V1 v1;

    @Mock
    private Message messageService;

    @Mock
    private Chat chatService;

    @Mock
    private CreateMessageResp successResponse;

    @Mock
    private CreateMessageResp failureResponse;

    @Mock
    private ListChatResp listChatResp;

    @Mock
    private RawResponse rawResponse;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(oapiMessageService, "client", client);
        ReflectionTestUtils.setField(oapiMessageService, "oapiNacosConfig", oapiNacosConfig);

        // 构建调用链
        when(client.im()).thenReturn(imService);
        when(imService.v1()).thenReturn(v1);
        when(v1.message()).thenReturn(messageService);
        when(v1.chat()).thenReturn(chatService);
    }

    /**
     * TC01: 正常发送消息，客户端返回成功
     */
    @Test
    void sendInteractiveMessage_success() throws Exception {
        // 准备参数
        String receiveId = "chat123";
        String template = "Hello {name}";
        Map<String, Object> map = new HashMap<>();
        map.put("name", "Tom");

        // 模拟客户端返回成功
        when(successResponse.success()).thenReturn(true);
        when(messageService.create(any(CreateMessageReq.class))).thenReturn(successResponse);
        when(successResponse.success()).thenReturn(true);
        when(successResponse.getRawResponse()).thenReturn(rawResponse);
        when(rawResponse.getBody()).thenReturn("success".getBytes(StandardCharsets.UTF_8));

        boolean result = oapiMessageService.sendInteractiveMessage(receiveId, template, map);

        assertTrue(result);
        verify(messageService).create(any(CreateMessageReq.class));
    }

    /**
     * TC02: receiveId 为空
     */
    @Test
    void sendInteractiveMessage_receiveIdIsNull() {
        String receiveId = null;
        String template = "Hello {name}";
        Map<String, Object> map = new HashMap<>();
        map.put("name", "Tom");

        assertThrows(IllegalArgumentException.class, () -> {
            oapiMessageService.sendInteractiveMessage(receiveId, template, map);
        });
    }

    /**
     * TC03: template 为空
     */
    @Test
    void sendInteractiveMessage_templateIsNull() {
        String receiveId = "chat123";
        String template = null;
        Map<String, Object> map = new HashMap<>();
        map.put("name", "Tom");

        assertThrows(IllegalArgumentException.class, () -> {
            oapiMessageService.sendInteractiveMessage(receiveId, template, map);
        });
    }

    /**
     * TC04: 模板格式化失败（map 缺少 key）
     */
    @Test
    void sendInteractiveMessage_formatException() {
        String receiveId = "chat123";
        String template = "Hello {missingKey}";
        Map<String, Object> map = new HashMap<>(); // 故意不提供 missingKey

        boolean result = oapiMessageService.sendInteractiveMessage(receiveId, template, map);

        assertFalse(result);
    }

    /**
     * TC05: 客户端调用抛异常
     */
    @Test
    void sendInteractiveMessage_clientThrowsException() throws Exception {
        String receiveId = "chat123";
        String template = "Hello {name}";
        Map<String, Object> map = new HashMap<>();
        map.put("name", "Tom");

        when(messageService.create(any(CreateMessageReq.class))).thenThrow(new RuntimeException("网络异常"));

        boolean result = oapiMessageService.sendInteractiveMessage(receiveId, template, map);

        assertFalse(result);
    }

    /**
     * TC07: 正常搜索群列表
     */
    @Test
    void searchChat_success() throws Exception {
        String userIdType = "user_id";
        String pageToken = "page_token";

        when(chatService.list(any(ListChatReq.class))).thenReturn(listChatResp);

        ListChatResp result = oapiMessageService.searchChat(userIdType, pageToken);

        assertEquals(listChatResp, result);
        verify(chatService).list(any(ListChatReq.class));
    }

    /**
     * TC08: userIdType 为空
     */
    @Test
    void searchChat_userIdTypeIsNull() throws Exception {
        String userIdType = null;
        String pageToken = "page_token";

        when(chatService.list(any(ListChatReq.class))).thenReturn(listChatResp);

        ListChatResp result = oapiMessageService.searchChat(userIdType, pageToken);

        assertEquals(listChatResp, result);
        verify(chatService).list(any(ListChatReq.class));
    }

    /**
     * TC09: pageToken 为空
     */
    @Test
    void searchChat_pageTokenIsNull() throws Exception {
        String userIdType = "user_id";
        String pageToken = null;

        when(chatService.list(any(ListChatReq.class))).thenReturn(listChatResp);

        ListChatResp result = oapiMessageService.searchChat(userIdType, pageToken);

        assertEquals(listChatResp, result);
        verify(chatService).list(any(ListChatReq.class));
    }

    /**
     * TC10: 客户端调用异常
     */
    @Test
    void searchChat_clientThrowsException() throws Exception {
        String userIdType = "user_id";
        String pageToken = "page_token";

        when(chatService.list(any(ListChatReq.class))).thenThrow(new RuntimeException("网络异常"));

        ListChatResp result = oapiMessageService.searchChat(userIdType, pageToken);

        assertNotNull(result);
        assertTrue(result instanceof ListChatResp);
    }

    /**
     * TC11: 正常发送缺失规则提醒
     */
    @Test
    void sendMissingRuleAlertMessage_success() {
        String receiveId = "chat123";
        MissingRuleAlertDTO missingRuleAlertDTO = new MissingRuleAlertDTO();
        Map<String, Object> content = new HashMap<>();
        content.put("key", "value");
        missingRuleAlertDTO.setCountry("中国");
        List<MissingRuleAlertDTO.MissingRuleAlertLineDTO> lines = new ArrayList<>();
        MissingRuleAlertDTO.MissingRuleAlertLineDTO line = new MissingRuleAlertDTO.MissingRuleAlertLineDTO();
        line.setTaskEvent("task_event");
        line.setTaskTemplateId("task_template_id");
        line.setRule1("rule1");
        line.setRule2("rule2");
        lines.add(line);

        missingRuleAlertDTO.setLines(lines);

        String template =
                "{\"elements\":[{\"tag\":\"column_set\",\"horizontal_spacing\":\"8px\",\"horizontal_align\":\"left\"," +
                        "\"columns\":[{\"tag\":\"column\",\"width\":\"weighted\",\"elements\":[{\"tag\":\"markdown\"," +
                        "\"content\":\"{content}\",\"text_align\":\"left\",\"text_size\":\"normal_v2\"}]}]}]," +
                        "\"header\":{\"title\":{\"tag\":\"plain_text\",\"content\":\"{title}\"},\"template\":\"red\"}}";

        when(oapiNacosConfig.getMissingRuleAlertTemplate()).thenReturn(template);
        // 使用 spy 来验证内部方法调用
        OapiMessageServiceImpl spyService = Mockito.spy(oapiMessageService);
        doReturn(true).when(spyService).sendInteractiveMessage(anyString(), anyString(), anyMap());

        boolean result = spyService.sendMissingRuleAlertMessage(receiveId, missingRuleAlertDTO);

        assertTrue(result);
        verify(spyService).sendInteractiveMessage(eq(receiveId), eq(template), anyMap());
    }

    /**
     * TC12: missingRuleAlertDTO 为 null
     */
    @Test
    void sendMissingRuleAlertMessage_dtoIsNull() {
        String receiveId = "chat123";
        MissingRuleAlertDTO missingRuleAlertDTO = null;

        boolean result = oapiMessageService.sendMissingRuleAlertMessage(receiveId, missingRuleAlertDTO);

        assertFalse(result);
    }
}
