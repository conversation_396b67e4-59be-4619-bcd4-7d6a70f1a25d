package com.mi.info.intl.retail.intlretail.service.app.market;

import com.xiaomi.nrme.market.api.rpc.service.intl.IntlAppRpcService;
import com.xiaomi.nrme.market.api.vo.article.intl.req.IntlAppSpuListVO;
import com.xiaomi.nrme.market.api.vo.category.i18n.IntlPosCategorySpuVO;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

/**
 * CollegeServiceImpl单元测试类
 * 
 * <AUTHOR> Generated
 * @date 2025-01-27
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("CollegeServiceImpl单元测试")
public class CollegeServiceImplTest {

    @InjectMocks
    private CollegeServiceImpl collegeService;

    @Mock
    private IntlAppRpcService appRpcService;

    private IntlAppSpuListVO testRequest;
    private List<IntlPosCategorySpuVO> testResponse;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testRequest = new IntlAppSpuListVO();
        testRequest.setAreaId("ID");
        testRequest.setSpuName("测试商品");
        
        testResponse = new ArrayList<>();
        IntlPosCategorySpuVO spu1 = new IntlPosCategorySpuVO();
        spu1.setSpuId(1L);
        spu1.setSpuName("测试商品1");
        testResponse.add(spu1);
        
        IntlPosCategorySpuVO spu2 = new IntlPosCategorySpuVO();
        spu2.setSpuId(2L);
        spu2.setSpuName("测试商品2");
        testResponse.add(spu2);
    }

    @Test
    @DisplayName("根据SPU名称搜索商品 - 成功返回商品列表")
    void searchSpuByName_Success_ReturnsSpuList() {
        // 准备数据
        Result<List<IntlPosCategorySpuVO>> successResult = mock(Result.class);
        when(successResult.getCode()).thenReturn(0);
        when(successResult.getData()).thenReturn(testResponse);
        when(appRpcService.searchSpuByName(any(IntlAppSpuListVO.class))).thenReturn(successResult);

        // 执行测试
        List<IntlPosCategorySpuVO> result = collegeService.searchSpuByName(testRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("测试商品1", result.get(0).getSpuName());
        assertEquals("测试商品2", result.get(1).getSpuName());
        
        // 验证方法调用
        verify(appRpcService, times(1)).searchSpuByName(testRequest);
    }

    @Test
    @DisplayName("根据SPU名称搜索商品 - 返回空列表")
    void searchSpuByName_Success_ReturnsEmptyList() {
        // 准备数据
        Result<List<IntlPosCategorySpuVO>> successResult = mock(Result.class);
        when(successResult.getCode()).thenReturn(0);
        when(successResult.getData()).thenReturn(Collections.emptyList());
        when(appRpcService.searchSpuByName(any(IntlAppSpuListVO.class))).thenReturn(successResult);

        // 执行测试
        List<IntlPosCategorySpuVO> result = collegeService.searchSpuByName(testRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证方法调用
        verify(appRpcService, times(1)).searchSpuByName(testRequest);
    }

    @Test
    @DisplayName("根据SPU名称搜索商品 - RPC返回null时抛出异常")
    void searchSpuByName_RpcReturnsNull_ThrowsException() {
        // 准备数据
        when(appRpcService.searchSpuByName(any(IntlAppSpuListVO.class))).thenReturn(null);

        // 执行测试并验证异常
        RuntimeException exception = assertThrows(RuntimeException.class,
                () -> collegeService.searchSpuByName(testRequest));

        // 验证异常信息
        assertEquals("searchSpuByName error", exception.getMessage());
        
        // 验证方法调用
        verify(appRpcService, times(1)).searchSpuByName(testRequest);
    }


    @Test
    @DisplayName("根据SPU名称搜索商品 - 验证请求参数传递")
    void searchSpuByName_VerifyRequestParameterPassing() {
        // 准备数据
        IntlAppSpuListVO customRequest = new IntlAppSpuListVO();
        customRequest.setAreaId("SG");
        customRequest.setSpuName("小米手机");
        
        Result<List<IntlPosCategorySpuVO>> successResult = mock(Result.class);
        when(successResult.getCode()).thenReturn(0);
        when(successResult.getData()).thenReturn(testResponse);
        when(appRpcService.searchSpuByName(any(IntlAppSpuListVO.class))).thenReturn(successResult);

        // 执行测试
        List<IntlPosCategorySpuVO> result = collegeService.searchSpuByName(customRequest);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        
        // 验证方法调用时传递了正确的参数
        verify(appRpcService, times(1)).searchSpuByName(argThat(request -> 
            request != null && 
            "SG".equals(request.getAreaId()) && 
            "小米手机".equals(request.getSpuName())
        ));
    }

    @Test
    @DisplayName("根据SPU名称搜索商品 - 边界情况测试")
    void searchSpuByName_BoundaryConditions() {
        // 准备数据 - 测试空字符串和null值
        IntlAppSpuListVO boundaryRequest = new IntlAppSpuListVO();
        boundaryRequest.setAreaId("");
        boundaryRequest.setSpuName(null);
        
        Result<List<IntlPosCategorySpuVO>> successResult = mock(Result.class);
        when(successResult.getCode()).thenReturn(0);
        when(successResult.getData()).thenReturn(Collections.emptyList());
        when(appRpcService.searchSpuByName(any(IntlAppSpuListVO.class))).thenReturn(successResult);

        // 执行测试
        List<IntlPosCategorySpuVO> result = collegeService.searchSpuByName(boundaryRequest);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        
        // 验证方法调用
        verify(appRpcService, times(1)).searchSpuByName(boundaryRequest);
    }
} 