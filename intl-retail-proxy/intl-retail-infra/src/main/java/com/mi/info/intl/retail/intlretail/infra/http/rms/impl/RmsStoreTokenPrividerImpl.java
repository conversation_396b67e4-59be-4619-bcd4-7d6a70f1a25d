package com.mi.info.intl.retail.intlretail.infra.http.rms.impl;

import com.mi.info.intl.retail.core.exception.RmsApiException;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.domain.http.rms.RmsStoreTokenPrivider;
import com.mi.info.intl.retail.intlretail.infra.config.AppliUserTokenConfig;
import com.mi.info.intl.retail.intlretail.infra.http.rms.dto.RmsToken;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.util.ObjectUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.client.RestClientException;
import org.springframework.web.client.RestTemplate;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@Slf4j
public class RmsStoreTokenPrividerImpl implements RmsStoreTokenPrivider {
    @Autowired
    private AppliUserTokenConfig tokenConfig;

    @Autowired
    private RestTemplate restTemplate;

    private Map<String, List<RmsToken>> multiEnvTokenCacheMap = new ConcurrentHashMap<>();
    private Map<String, AtomicInteger> envTokenIndexMap = new ConcurrentHashMap<>();

    private final ExecutorService executorService = Executors.newFixedThreadPool(10);

    @PostConstruct
    public void init() {
        // 初始化时获取多个Token
        tokenConfig.getResources().entrySet().stream()
                .forEach(item -> {
                    List<RmsToken> tokenCache = Collections.synchronizedList(new ArrayList<>());

                    List<CompletableFuture<Void>> futures = tokenConfig.getClients().stream()
                            .map(clientInfo -> CompletableFuture.runAsync(() -> {
                                RmsToken token = doGetToken(item.getValue(), clientInfo);
                                tokenCache.add(token);
                            }, executorService))
                            .collect(Collectors.toList());

                    CompletableFuture.allOf(futures.toArray(new CompletableFuture[0]))
                            .thenAccept(ignored -> {
                                multiEnvTokenCacheMap.put(item.getKey(), tokenCache);
                                envTokenIndexMap.put(item.getKey(), new AtomicInteger(0));
                            })
                            .join();
                });
    }

    @Override
    public String getToken(String envName) {
        List<RmsToken> tokenCache = multiEnvTokenCacheMap.get(envName);
        AtomicInteger currentIndex = envTokenIndexMap.get(envName);
        int index = currentIndex.get();
        RmsToken token = tokenCache.get(index);
        if (token == null || !cacheValid(token)) {
            // 初始化一个RmsToken供使用
            token = doGetToken(tokenConfig.getResources().get(envName), tokenConfig.getClients().get(index));
            tokenCache.set(index, token);
        }
        currentIndex.incrementAndGet();
        if (currentIndex.get() >= tokenCache.size()) {
            currentIndex.set(0);
        }
        return token.getAccessToken();
    }

    private boolean cacheValid(RmsToken token) {
        try {
            if (ObjectUtils.isEmpty(token)) {
                return false;
            }
            String expiresOn = token.getExpiresOn();
            if (ObjectUtils.isEmpty(expiresOn)) {
                return false;
            }
            long end = Long.parseLong(expiresOn);
            log.info("end = {}", end);
            // 过期时间在一分钟内
            return end >= System.currentTimeMillis() / 1000 + 60;
        } catch (Exception e) {
            return false;
        }
    }

    private RmsToken doGetToken(String resourceUrl, AppliUserTokenConfig.ClientInfo clientInfo) {
        RmsToken rmsToken;

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        MultiValueMap<String, String> body = new LinkedMultiValueMap<>();
        body.add("resource", resourceUrl);
        body.add("grant_type", "client_credentials");
        body.add("client_id", clientInfo.getClientId());
        body.add("client_secret", clientInfo.getClientSecret());

        HttpEntity<MultiValueMap<String, String>> requestEntity = new HttpEntity<>(body, headers);
        try {
            String res = this.restTemplate.exchange(tokenConfig.getUrl(), HttpMethod.POST, requestEntity, String.class).getBody();
            rmsToken = JsonUtil.json2bean(res, RmsToken.class);
        } catch (RestClientException e) {
            throw new RmsApiException(400, e.getMessage(), e);
        }
        return rmsToken;
    }
}
