-- 门店等级规则表 DDL
-- 创建时间: 2025-08-05
-- 描述: 存储门店等级规则配置信息

CREATE TABLE `store_grade_rule` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `country_code` varchar(10) NOT NULL COMMENT '国家代码',
  `channel_type` varchar(50) DEFAULT NULL COMMENT '渠道类型',
  `method` varchar(20) DEFAULT NULL COMMENT '计算方法',
  `retailer_name` varchar(100) DEFAULT NULL COMMENT '零售商名称',
  `retailer_code` varchar(50) DEFAULT NULL COMMENT '零售商代码',
  `s_min_count` bigint(20) DEFAULT NULL COMMENT 'S级最小数量',
  `a_min_count` bigint(20) DEFAULT NULL COMMENT 'A级最小数量',
  `b_min_count` bigint(20) DEFAULT NULL COMMENT 'B级最小数量',
  `c_min_count` bigint(20) DEFAULT NULL COMMENT 'C级最小数量',
  `d_min_count` bigint(20) DEFAULT NULL COMMENT 'D级最小数量',
  `rules_status` int(11) DEFAULT NULL COMMENT '规则状态',
  `application_time` datetime DEFAULT NULL COMMENT '申请时间',
  `approved_time` datetime DEFAULT NULL COMMENT '审批时间',
  `last_calculation_time` datetime DEFAULT NULL COMMENT '最后计算时间',
  `approve_status` int(11) DEFAULT NULL COMMENT '审批状态',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `create_by` varchar(50) DEFAULT NULL COMMENT '创建人',
  `update_by` varchar(50) DEFAULT NULL COMMENT '更新人',
  `is_deleted` tinyint(1) NOT NULL DEFAULT '0' COMMENT '是否删除（0-未删除，1-已删除）',
  PRIMARY KEY (`id`),
  KEY `idx_country_channel` (`country_code`, `channel_type`, `is_deleted`) COMMENT '国家代码+渠道类型+删除标记复合索引'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='门店等级规则表';

-- 插入示例数据
INSERT INTO `store_grade_rule` (`country_code`, `channel_type`, `method`, `retailer_name`, `retailer_code`, `s_min_count`, `a_min_count`, `b_min_count`, `c_min_count`, `d_min_count`, `rules_status`, `approve_status`, `create_by`) VALUES
('CN', 'RETAILER', 'CURRENT', '小米零售', 'MI', 1000, 500, 200, 100, 50, 1, 1, 'system'),
('CN', 'RETAILER', 'RELATION', '小米零售', 'MI', 1000, 500, 200, 100, 50, 1, 1, 'system'),
('US', 'RETAILER', 'CURRENT', 'Xiaomi Retail', 'MI', 800, 400, 150, 80, 40, 1, 1, 'system'),
('US', 'WHOLESALE', 'CURRENT', 'Xiaomi Wholesale', 'MI', 500, 250, 100, 50, 25, 1, 1, 'system');