# ChannelWorkableService.applyChannelWorkableChanges

**接口说明**

批量应用岗位变更参数，根据传入的变更列表和历史岗位状态，计算并更新当前岗位的可用状态。

---

## 方法签名

```java
void applyChannelWorkableChanges(List<ChannelWorkableChangeParam> changes);
```

---

## 请求参数

- **changes**（List<ChannelWorkableChangeParam>）
  - 变更参数列表，每个元素代表一次岗位属性的变更。

### ChannelWorkableChangeParam 字段说明
| 字段名         | 类型                        | 说明                       |
| -------------- | --------------------------- | -------------------------- |
| changeType     | ChannelWorkableChangeType   | 变更类型（枚举，见下表）   |
| changeResult   | ChannelWorkableChangeResultType | 变更结果（枚举，见下表） |
| effectiveTime  | Long                       | 变更生效时间（时间戳，毫秒） |
| positionCode   | String                     | 岗位编码                   |

#### ChannelWorkableChangeType 枚举值
- HAS_PS
- HAS_SR
- POSITION_TYPE
- POSITION_STATUS

#### ChannelWorkableChangeResultType 枚举值
- TRUE
- FALSE
- POS
- OPEN
- CLOSE
- ...（可根据业务扩展）

#### 示例参数
```json
[
  {
    "changeType": "HAS_PS",
    "changeResult": "FALSE",
    "effectiveTime": 1111111,
    "positionCode": "POS001"
  },
  {
    "changeType": "POSITION_TYPE",
    "changeResult": "POS",
    "effectiveTime": 1111111,
    "positionCode": "POS001"
  },
  {
    "changeType": "POSITION_STATUS",
    "changeResult": "OPEN",
    "effectiveTime": 1111111,
    "positionCode": "POS001"
  }
]
```

---

## 返回值

- 无（void）。如有业务异常会抛出异常。

---

## 异常说明
- 如果所有变更应用后与历史状态无差异，会抛出检查型异常（CheckedException），提示“没有变化”。
- 其他业务异常会根据实现抛出。

---

## 备注
- 该接口通常用于批量岗位属性变更场景。
- 变更类型和字段可根据业务扩展。 