package com.mi.info.intl.retail.org.domain.repository;

import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.dto.RuleConfigWithPendingInspectionDTO;

import java.util.List;

/**
 * 规则配置Repository接口
 */
public interface RuleConfigRepository {

    /**
     * 保存规则配置
     * 
     * @param ruleConfigDomain 规则配置领域对象
     * @return 是否成功
     */
    boolean save(RuleConfigDomain ruleConfigDomain);

    /**
     * 更新规则配置
     * 
     * @param ruleConfigDomain 规则配置领域对象
     * @return 是否成功
     */
    boolean update(RuleConfigDomain ruleConfigDomain);

    /**
     * 根据ID查询规则配置
     * 
     * @param id 规则配置ID
     * @return 规则配置领域对象
     */
    RuleConfigDomain getById(Long id);

    /**
     * 根据规则编码查询规则配置
     * 
     * @param ruleCode 规则编码
     * @return 规则配置领域对象
     */
    RuleConfigDomain getByRuleCode(String ruleCode);

    List<RuleConfigDomain> getByRuleCodeList(List<String> ruleCodes);

    /**
     * 根据任务批次ID查询规则配置
     * 
     * @param taskBatchId 任务批次ID
     * @return 规则配置领域对象
     */
    RuleConfigDomain getByTaskBatchId(Long taskBatchId);

    /**
     * 根据国家、区域查询有效的规则配置
     *
     * @param country 国家
     * @param region 区域
     * @return 规则配置领域对象列表
     */
    List<RuleConfigDomain> findValidRuleConfigs(String country, String region);

    /**
     * 根据区域ID查询有效的规则配置
     *
     * @param areaId 区域ID
     * @return 规则配置领域对象列表
     */
    List<RuleConfigDomain> findValidRuleConfigsByAreaId(String areaId);
    
    /**
     * 根据国家查询有效的规则配置
     *
     * @param country 国家
     * @param taskType 任务类型
     * @return 规则配置领域对象列表
     */
    List<RuleConfigDomain> findValidRuleConfigsByCountry(String country, String taskType);

    /**
     * 根据国家列表查询活跃的规则配置
     *
     * @param countries 国家列表
     * @return 活跃的规则配置领域对象列表
     */
    List<RuleConfigDomain> findActiveRulesByCountries(List<String> countries);

    /**
     * 根据国家列表查询有效的规则配置和未下发的巡检记录
     *
     * @param countries 国家列表
     * @return 规则配置和未下发巡检记录数量DTO列表
     */
    List<RuleConfigWithPendingInspectionDTO> getActiveRulesWithPendingInspections(List<String> countries);


    List<RuleConfigDomain> getByTaskDefineIds(List<Long> taskDefineIds);
}