package com.mi.info.intl.retail.org.app.service.material;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import com.mi.info.intl.retail.core.exception.RetailRunTimeException;
import com.mi.info.intl.retail.core.utils.IntlRetailAssert;
import com.mi.info.intl.retail.intlretail.domain.common.utils.JsonUtil;
import com.mi.info.intl.retail.intlretail.service.api.material.NewProductInspectionService;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationInfoVO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordPageDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialModelRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.SubmitMaterialInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.TaskRemindDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.TaskRemindRequest;
import com.mi.info.intl.retail.intlretail.service.api.proxy.TaskCenterProxyService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCentterPushReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.app.excel.ExcelTemplate;
import com.mi.info.intl.retail.org.domain.material.service.NewInspectionConfigDomainService;
import com.mi.info.intl.retail.org.domain.material.service.NewProductInspectionDomainService;
import com.mi.info.intl.retail.org.domain.repository.IntlStoreSalesModelRepository;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Service
@DubboService(group = "${material.dubbo.group:}", interfaceClass = NewProductInspectionService.class)
@ApiModule(value = "新品物料巡检服务实现类", apiInterface = NewProductInspectionService.class)
public class NewProductInspectionServiceImpl implements NewProductInspectionService {

    @Autowired
    private NewInspectionConfigDomainService newInspectionConfigDomainService;

    @Resource
    private NewProductInspectionDomainService newProductInspectionDomainService;
    @Autowired
    private ExcelTemplate excelTemplate;

    @Resource
    JobTriggerHelper jobTriggerHelper;

    @Autowired
    private TaskCenterProxyService taskCenterProxyService;

    @Autowired
    private IntlStoreSalesModelRepository storeSalesModelRepository;

    @Override
    public CommonApiResponse<PageResponse<MaterialInspectionItem>> getMaterialInspectionList(
            MaterialInspectionReq request) {
        String areaId = request.getAreaId();
        request.setLocalTimestamp(IntlTimeUtil.getLocalTimestamp(areaId));
        Page<MaterialInspectionItem> resultPage = newProductInspectionDomainService.pageMaterialInspection(request);
        log.info("分页查询结果: total={}, pageNum={}, pageSize={}, records={}", resultPage.getTotal(),
                resultPage.getCurrent(), resultPage.getSize(), resultPage.getRecords());
        List<MaterialInspectionItem> records = resultPage.getRecords();
        PageResponse<MaterialInspectionItem> pageResponse = new PageResponse<>(resultPage.getTotal(),
                request.getPageNum(), request.getPageSize(), records);
        return new CommonApiResponse<>(pageResponse);
    }

    @Override
    public CommonApiResponse<MaterialInspectionDetailResponse> getMaterialInspectionDetail(
            MaterialInspectionDetailRequest request) {
        log.info("查询详情参数: owner={}, inspectionId={}", request.getOwner(), request.getMaterialInspectionId());
        return newProductInspectionDomainService.getMaterialInspectionDetail(request);
    }

    @Override
    public CommonApiResponse<String> submitMaterialInspection(SubmitMaterialInspectionRequest req) {
        try {
            log.info("submitMaterialInspection req:{}", JsonUtil.bean2json(req));
            return newProductInspectionDomainService.submitMaterialInspection(req);
        } catch (Exception e) {
            log.error("阵地巡检提交处理异常", e);
            return new CommonApiResponse<>(500, "系统异常: " + e.getMessage(), "");
        }
    }
    
    @Override
    public CommonApiResponse<List<MaterialInspectionOperationHistoryResponse>> getMaterialInspectionOperationHistory(
            MaterialInspectionOperationHistoryRequest req) {
        List<MaterialInspectionOperationHistoryResponse> responses = newProductInspectionDomainService.getMaterialInspectionOperationHistory(
                req.getMaterialInspectionId());
        return CommonApiResponse.success(responses);
    }

    @Override
    @ApiDoc(name = "巡检任务配置枚举项", value = "巡检任务配置枚举项", description = "/mtop/proretail-pc/intl/new/inspectionConfig/getEnumByType")
    public CommonApiResponse<List<Map<String, Object>>> getEnumByType(InspectionTaskConfigurationPageRequest dto) {
        log.info("查询详情参数: dto {}", dto == null ? "" : JSON.toJSONString(dto));
        List<Map<String, Object>> map = newInspectionConfigDomainService.getEnumByType(dto);
        return CommonApiResponse.success(map);
    }

    @Override
    @ApiDoc(name = "巡检任务配置分页查询", value = "巡检任务配置分页查询", description = "/mtop/proretail-pc/intl/new/inspectionConfig/page")
    public CommonApiResponse<PageResponse<InspectionTaskConfigurationPageResponse>> findInspectionTaskPage(
            InspectionTaskConfigurationPageRequest dto) {
        Page<InspectionTaskConfigurationPageResponse> page = new Page<>(dto.getPageNum(), dto.getPageSize());
        Page<InspectionTaskConfigurationPageResponse> data =
                newInspectionConfigDomainService.findInspectionTaskPage(page,
                        dto);
        PageResponse<InspectionTaskConfigurationPageResponse> pageResponse =
                new PageResponse<>(data.getTotal(), data.getCurrent(),
                        data.getSize(),
                        data.getRecords());
        return CommonApiResponse.success(pageResponse);
    }

    @Override
    @ApiDoc(name = "巡检任务配置详情", value = "巡检任务配置详情", description = "/mtop/proretail-pc/intl/new/inspectionConfig/info")
    public CommonApiResponse<InspectionTaskConfigurationInfoVO> getInspectionTaskInfo(
            InspectionTaskConfigurationRequest dto) {
        InspectionTaskConfigurationInfoVO inspectionTaskConfigurationInfoVO =
                newInspectionConfigDomainService.getInspectionTaskInfo(dto);
        return CommonApiResponse.success(inspectionTaskConfigurationInfoVO);
    }

    @Override
    @ApiDoc(name = "巡检任务配置保存", value = "巡检任务配置保存", description = "/mtop/proretail-pc/intl/new/inspectionConfig/save")
    public CommonApiResponse<InspectionTaskConfigurationInfoVO> saveInspectionTask(InspectionTaskConfigurationDTO dto) {
        log.info("保存详情参数 saveInspectionTask: dto {}", dto == null ? "" : JSON.toJSONString(dto));
        if (Objects.isNull(dto)) {
            return CommonApiResponse.success();
        }
        InspectionTaskConfigurationInfoVO inspectionTaskConfigurationInfoVO = null;
        try {
            inspectionTaskConfigurationInfoVO = newInspectionConfigDomainService.insertOrUpdate(dto);
        } catch (RetailRunTimeException ex) {
            log.warn("saveInspectionTask error.", ex);
            return CommonApiResponse.failure(500, ex.getMessage());
        } catch (Exception ex) {
            log.error("saveInspectionTask error.", ex);
            return CommonApiResponse.failure(500, "保存巡检配置异常");
        }
        return CommonApiResponse.success(inspectionTaskConfigurationInfoVO);
    }

    @Override
    @ApiDoc(name = "巡检任务配置提交", value = "巡检任务配置提交", description = "/mtop/proretail-pc/intl/new/inspectionConfig/submit")
    public CommonApiResponse submitInspectionTask(InspectionTaskConfigurationDTO dto) {
        log.info("提交详情参数 submitInspectionTask: dto {}", dto == null ? "" : JSON.toJSONString(dto));
        try {
            newInspectionConfigDomainService.submitInspectionTaskConfiguration(dto);
        } catch (RetailRunTimeException ex) {
            log.warn("submitInspectionTask error.", ex);
            return CommonApiResponse.failure(500, ex.getMessage());
        } catch (Exception ex) {
            log.error("submitInspectionTask error.", ex);
            return CommonApiResponse.failure(500, "启用巡检配置异常");
        }
        return CommonApiResponse.success();
    }

    @Override
    @ApiDoc(name = "巡检任务配置停用", value = "巡检任务配置停用", description = "/mtop/proretail-pc/intl/new/inspectionConfig/enable")
    public CommonApiResponse startOrStopInspectionTask(InspectionTaskConfigurationRequest dto) {
        log.info("巡检任务配置停用 startOrStopInspectionTask: dto {}", dto == null ? "" : JSON.toJSONString(dto));
        try {
            newInspectionConfigDomainService.startOrStopInspectionTask(dto);
        } catch (RetailRunTimeException ex) {
            log.warn("startOrStopInspectionTask error.", ex);
            return CommonApiResponse.failure(500, ex.getMessage());
        } catch (Exception ex) {
            log.error("startOrStopInspectionTask error.", ex);
            return CommonApiResponse.failure(500, "启停巡检配置异常");
        }
        return CommonApiResponse.success();
    }

    @Override
    @ApiDoc(name = "巡检任务配置导出", value = "巡检任务配置导出", description = "/mtop/proretail-pc/intl/new/inspectionConfig/export")
    public CommonApiResponse exportInspectionTask(InspectionTaskConfigurationPageRequest dto) {
        newInspectionConfigDomainService.exportInspectionTask(dto);
        return CommonApiResponse.success();
    }

    @Override
    @ApiDoc(name = "巡检任务配置-零售商导入", value = "巡检任务配置-零售商导入", description = "/mtop/proretail-pc/intl/new/inspectionConfig/upload")
    public CommonApiResponse<InspectionTaskConfigurationDTO> uploadAssignedStore(
            InspectionTaskConfigurationRequest dto) {
        InspectionTaskConfigurationDTO inspectionTaskConfigurationDTO =
                newInspectionConfigDomainService.uploadAssignedStore(dto);
        return CommonApiResponse.success(inspectionTaskConfigurationDTO);
    }

    @Override
    @ApiDoc(name = "巡检任务配置-所有国家区域", value = "巡检任务配置-所有国家区域", description =
            "/mtop/proretail-pc/intl/new" +
                    "/inspectionConfig/listArea")
    public CommonApiResponse<List<Area>> listArea() {
        return CommonApiResponse.success(Area.all());
    }

    @Override
    public MaterialSampleResponse getMaterialSample(MaterialSampleRequest req) {
        return newProductInspectionDomainService.getMaterialSample(req);
    }

    @Override
    @ApiDoc(description = "巡检任务下拉框列表", name = "巡检任务下拉框列表",
            value = "/mtop/proretail-pc/intl/newProduct/materialInspection/record/selector")
    public CommonApiResponse<MaterialInspectionRecordSelectorItem> getMaterialInspectionRecordSelectorItem() {
        MaterialInspectionRecordSelectorItem selectorItem = newProductInspectionDomainService.getSelectorItemList();
        return new CommonApiResponse<>(selectorItem);
    }

    @Override
    @ApiDoc(description = "巡检任务搜索&排序&分页 列表接口", name = "巡检任务搜索&排序&分页 列表接口",
            value = "/mtop/proretail-pc/intl/newProduct/materialInspection/record/list")
    public CommonApiResponse<PageResponse<MaterialInspectionRecordPageDTO>> getMaterialInspectionRecordPageItem(
            InspectionRecordPageRequest request) {
        log.info("material inspection record page request: {}", request);
        PageResponse<MaterialInspectionRecordPageDTO> pageItem =
                newProductInspectionDomainService.getMaterialInspectionRecordPageItem(request);
        return new CommonApiResponse<>(pageItem);
    }

    @Override
    @ApiDoc(description = "巡检任务列表导出", name = "巡检任务列表导出接口",
            value = "/mtop/proretail-pc/intl/newProduct/materialInspection/record/export")
    public CommonApiResponse<String> exportMaterialInspectionRecordPageItem(
            InspectionRecordPageRequest request) {
        log.info("material inspection record export request: {}", request);
        String url = excelTemplate.exporter(MaterialInspectionRecordPageDTO.class, request,
                        newProductInspectionDomainService::getMaterialInspectionRecordPageItem)
                .setFileName("Material_Inspection_List")
                .setTaskName("Material Inspection List Export")
                .doExport();

        return CommonApiResponse.success(url);
    }

    /**
     * 1. title：Task Warning
     * 2. 文案：任务事件，deadline：yyyy/mm/dd XX:XX（时分）,please complete it in time.
     * 3. 点击跳转导到task tab页
     *
     * @param request
     * @return
     */
    @Override
    public CommonApiResponse<List<TaskRemindDTO>> taskRemind(TaskRemindRequest request) {
        //获取未完成的任务
        List<TaskRemindDTO> taskRemindDTOList = new ArrayList<>();
        Long miId = request.getMiId();
        List<TaskRemindDTO> list = newProductInspectionDomainService.getNoCompletedList(miId);
        if (CollectionUtil.isEmpty(list)) {
            log.info("未完成任务为空");
            return CommonApiResponse.success(taskRemindDTOList);
        }
        Map<String, Long> countryMap = getCountryMap(list);
        for (TaskRemindDTO taskRemindDTO : list) {
            String country = taskRemindDTO.getCountry();
            if (StringUtils.isEmpty(country)) {
                continue;
            }
            Long timeStamp = countryMap.get(country);
            Long periodStartTimeStamp = taskRemindDTO.getPeriodStartTimeStamp();
            Long periodEndTimeStamp = taskRemindDTO.getPeriodEndTimeStamp();
            if (timeStamp == null || periodStartTimeStamp == null || periodEndTimeStamp == null) {
                continue;
            }
            //判断当前时间是否是否在任务时间段内
            boolean isInTime = periodStartTimeStamp <= timeStamp && timeStamp <= periodEndTimeStamp;
            if (!isInTime) {
                continue;
            }
            Long reminderTime = taskRemindDTO.getReminderTime();
            if (reminderTime == null) {
                continue;
            }
        }
        TaskCentterPushReq taskCentterPushReq = new TaskCentterPushReq();
        taskCenterProxyService.pushTask(taskCentterPushReq);
        return CommonApiResponse.success(taskRemindDTOList);
    }

    @Override
    public List<String> getStoreModelList(String positionCode) {
        if (StringUtils.isEmpty(positionCode)) {
            return Collections.emptyList();
        }
        List<String> salesModels = storeSalesModelRepository.getStoreSalesModelList(positionCode);
        if (!CollectionUtils.isEmpty(salesModels)) {
            return Collections.emptyList();
        }
        return newProductInspectionDomainService.getStoreModelList(positionCode);
    }

    @Override
    public void saveSalesStoreModels(MaterialModelRequest request) {
        IntlRetailAssert.notEmpty(request.getPositionCode(), "PositionCode can not be empty");
        IntlRetailAssert.notEmpty(request.getProject(), "Project can not be empty");
        List<String> storeSalesModelList = storeSalesModelRepository.getStoreSalesModelList(request.getPositionCode());
        IntlRetailAssert.isTrue(storeSalesModelList.isEmpty(), "Position already exists");
        storeSalesModelRepository.saveSalesStoreModels(request);
    }

    private Map<String, Long> getCountryMap(List<TaskRemindDTO> list) {
        List<String> countryList = list.stream().map(TaskRemindDTO::getCountry).filter(Objects::nonNull).distinct()
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(countryList)) {
            return Collections.emptyMap();
        }
        //
        return IntlTimeUtil.getCurrentTimeStampByCountryCodes(countryList);
    }

    @Override
    @ApiDoc(description = "巡检任务审核/驳回接口", name = "巡检任务审核/驳回接口",
            value = "/mtop/proretail-pc/intl/newProduct/materialInspection/record/verify")
    public CommonApiResponse<Void> verifyMaterialInspection(MaterialInspectionVerifyRequest request) {
        log.info("verify material inspection request: {}", request);
        try {
            newProductInspectionDomainService.verifyMaterialInspection(request);
        } catch (RetailRunTimeException ex) {
            log.warn("verify material inspection biz failed.", ex);
            return CommonApiResponse.failure(500, ex.getMessage());
        } catch (Exception ex) {
            log.error("verify material inspection sys failed.", ex);
            return CommonApiResponse.failure(500, "verify material inspection failed");
        }
        return CommonApiResponse.success();
    }
}
