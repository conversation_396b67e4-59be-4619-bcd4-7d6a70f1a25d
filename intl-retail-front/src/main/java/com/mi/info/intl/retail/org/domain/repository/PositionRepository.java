package com.mi.info.intl.retail.org.domain.repository;

import com.mi.info.intl.retail.org.domain.PositionDomain;

import java.util.List;

/**
 * 阵地信息仓储接口
 */
public interface PositionRepository {
    
    /**
     * 保存阵地信息
     *
     * @param positionDomain 阵地领域对象
     * @return 是否保存成功
     */
    boolean save(PositionDomain positionDomain);
    
    /**
     * 通过ID获取阵地信息
     *
     * @param id 阵地ID
     * @return 阵地领域对象
     */
    PositionDomain getById(Integer id);
    
    /**
     * 通过阵地ID获取阵地信息
     *
     * @param positionId 阵地ID
     * @return 阵地领域对象
     */
    PositionDomain getByPositionId(String positionId);

    /**
     * 通过阵地编码获取阵地信息
     *
     * @param positionCode 阵地编码
     * @return 阵地领域对象
     */
    PositionDomain getByPositionCode(String positionCode);

    List<PositionDomain> getByPositionCodeList(List<String> positionCodes);
    /**
     * 通过编码获取阵地信息
     *
     * @param code 编码
     * @return 阵地领域对象
     */
    PositionDomain getByCode(String code);
    
    /**
     * 更新阵地信息
     *
     * @param positionDomain 阵地领域对象
     * @return 是否更新成功
     */
    boolean update(PositionDomain positionDomain);
    
    /**
     * 通过零售商ID获取阵地列表
     *
     * @param retailerId 零售商ID
     * @return 阵地领域对象列表
     */
    List<PositionDomain> getByRetailerId(String retailerId);
    
    /**
     * 通过门店ID获取阵地列表
     *
     * @param storeId 门店ID
     * @return 阵地领域对象列表
     */
    List<PositionDomain> getByStoreId(String storeId);
    
    /**
     * 通过国家获取阵地列表
     *
     * @param country 国家
     * @return 阵地领域对象列表
     */
    List<PositionDomain> getByCountry(String country);
    
    /**
     * 通过城市获取阵地列表
     *
     * @param cityId 城市ID
     * @return 阵地领域对象列表
     */
    List<PositionDomain> getByCityId(String cityId);
    
    /**
     * 通过阵地类型获取阵地列表
     *
     * @param positionType 阵地类型
     * @return 阵地领域对象列表
     */
    List<PositionDomain> getByPositionType(String positionType);

    List<PositionDomain> getByCodes(List<String> codes);
}