package com.mi.info.intl.retail.org.domain.newproducttarget;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.*;
import com.mi.info.intl.retail.model.CommonApiResponse;

import javax.validation.Valid;
import java.util.List;

/**
 * 新品目标实现domain的目标包
 *
 * @author: chuang
 * @since: 2025/8/1
 */
public interface NewProductTargetDomainService {
    
    Page<NewProductTargetItem> listNewProductTarget(NewProductTargetReq req);
    
    NewProductTargetItem getNewProductTargetItemById(Long id);
    
    /**
     * 新增新品目标
     */
    CommonApiResponse<List<String>> addNewProductTarget(@Valid NewProductTargetAddReq req);
    
    CommonApiResponse<String> updateNewProductTarget(@Valid NewProductTargetUpdateReq req);
    
    
    CommonApiResponse<List<NewProdcutTargetMetaResp>> listMeta(@Valid NewProdcutTargetMetaReq req);
    
    /**
     * 导入新品目标
     */
    CommonApiResponse<String> importNewProdcutTargetMetaResp(@Valid NewProductTargetImportReq req);
    /**
    *级联查询项目
    */
    List<NewProductTargetProjectResp> listProject(NewProductTargetProjectReq req);
    /**
    *得到当前用户的组织基本信息
    */
    public GetUserInfoRespForAuth judgeUser();
}
