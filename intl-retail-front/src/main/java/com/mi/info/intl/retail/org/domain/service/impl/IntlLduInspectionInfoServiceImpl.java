package com.mi.info.intl.retail.org.domain.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.material.dto.LduInspectionRecordItem;
import com.mi.info.intl.retail.org.domain.dto.IntlLduInspectionInfoDTO;
import com.mi.info.intl.retail.org.domain.service.IntlLduInspectionInfoService;
import com.mi.info.intl.retail.org.infra.entity.IntlLduInspectionInfo;
import com.mi.info.intl.retail.org.infra.mapper.IntlLduInspectionInfoMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * LDU巡检明细数据 Service 实现类
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Service
public class IntlLduInspectionInfoServiceImpl implements IntlLduInspectionInfoService {

    @Resource
    private IntlLduInspectionInfoMapper intlLduInspectionInfoMapper;

    @Override
    public IntlLduInspectionInfoDTO getById(Long id) {
        IntlLduInspectionInfo entity = intlLduInspectionInfoMapper.selectById(id);
        return convertToDTO(entity);
    }

    @Override
    public List<IntlLduInspectionInfoDTO> getByInspectionRecordId(Long inspectionRecordId) {
        List<IntlLduInspectionInfo> entities = intlLduInspectionInfoMapper.selectByInspectionRecordId(inspectionRecordId);
        return entities.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<IntlLduInspectionInfoDTO> getByLduReportLogId(Long lduReportLogId) {
        List<IntlLduInspectionInfo> entities = intlLduInspectionInfoMapper.selectByLduReportLogId(lduReportLogId);
        return entities.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<IntlLduInspectionInfoDTO> getByGuid(String guid) {
        List<IntlLduInspectionInfo> entities = intlLduInspectionInfoMapper.selectByGuid(guid);
        return entities.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public boolean save(IntlLduInspectionInfoDTO dto) {
        IntlLduInspectionInfo entity = convertToEntity(dto);
        return intlLduInspectionInfoMapper.insert(entity) > 0;
    }

    @Override
    public boolean batchSave(List<IntlLduInspectionInfoDTO> dtoList) {
        List<IntlLduInspectionInfo> entities = dtoList.stream()
                .map(this::convertToEntity)
                .collect(Collectors.toList());
        return intlLduInspectionInfoMapper.batchInsert(entities) > 0;
    }

    @Override
    public boolean update(IntlLduInspectionInfoDTO dto) {
        IntlLduInspectionInfo entity = convertToEntity(dto);
        return intlLduInspectionInfoMapper.updateById(entity) > 0;
    }

    @Override
    public boolean deleteById(Long id) {
        return intlLduInspectionInfoMapper.deleteById(id) > 0;
    }

    @Override
    public List<IntlLduInspectionInfoDTO> queryByCondition(IntlLduInspectionInfoDTO dto) {
        IntlLduInspectionInfo entity = convertToEntity(dto);
        List<IntlLduInspectionInfo> entities = intlLduInspectionInfoMapper.selectByCondition(entity);
        return entities.stream().map(this::convertToDTO).collect(Collectors.toList());
    }

    @Override
    public List<LduInspectionRecordItem> getByInspectionRecordIdList(List<Long> inspectionRecordIds) {
        if (CollectionUtils.isEmpty(inspectionRecordIds)) {
            return Collections.emptyList();
        }
        return intlLduInspectionInfoMapper.selectByInspectionRecordIdList(inspectionRecordIds);
    }

    /**
     * 将实体对象转换为DTO
     */
    private IntlLduInspectionInfoDTO convertToDTO(IntlLduInspectionInfo entity) {
        if (entity == null) {
            return null;
        }
        IntlLduInspectionInfoDTO dto = new IntlLduInspectionInfoDTO();
        BeanUtils.copyProperties(entity, dto);
        return dto;
    }

    /**
     * 将DTO转换为实体对象
     */
    private IntlLduInspectionInfo convertToEntity(IntlLduInspectionInfoDTO dto) {
        if (dto == null) {
            return null;
        }
        IntlLduInspectionInfo entity = new IntlLduInspectionInfo();
        BeanUtils.copyProperties(dto, entity);
        return entity;
    }
}
