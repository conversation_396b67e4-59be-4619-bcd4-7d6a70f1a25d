package com.mi.info.intl.retail.management.rpc;

import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.maindataapi.api.StoreProvider;
import com.xiaomi.cnzone.maindataapi.model.OrgResponse;
import com.xiaomi.cnzone.maindataapi.model.enums.DomainEnum;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgParamEntity;
import com.xiaomi.cnzone.maindataapi.model.req.store.StoreListByOrgIdsRequest;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import com.xiaomi.cnzone.storems.common.exception.ErrorCodeEnums;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.List;

import static org.reflections.Reflections.log;

@Service
public class MainDataRpc {

    private static final Integer PAGE_SIZE = 100;
    private static final Integer PAGE_NUM = 1;

    @Reference(group = "${maindata.dubbo.group:}", version = "1.0", check = false,  timeout = 20000)
    private StoreProvider storeProvider;

    Integer success = 0;

    public OrgResponse selectStoreByOrgIds(List<String> orgIds, String[] fields) {
        StoreListByOrgIdsRequest request = new StoreListByOrgIdsRequest();
        request.setOrgId(orgIds);
        request.setFilter(DomainEnum.autoAllDomains);
        request.setPageIndex(PAGE_NUM);
        request.setPageSize(PAGE_SIZE);
        if (fields != null) {
            request.setFilter(fields);
        }
        Result<OrgResponse> result = storeProvider.selectStoreByOrgIds(request);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new BusinessException(result.getMessage());
    }

    public OrgResponse pageSelectStoreByOrgIds(List<String> orgIds, Integer pageNum, Integer pageSize) {
        StoreListByOrgIdsRequest request = new StoreListByOrgIdsRequest();
        request.setOrgId(orgIds);
        request.setFilter(DomainEnum.autoAllDomains);
        request.setPageIndex(pageNum);
        request.setPageSize(pageSize);
        Result<OrgResponse> result = storeProvider.selectStoreByOrgIds(request);
        if (result.getCode() == success) {
            return result.getData();
        }
        throw new BusinessException(result.getMessage());
    }

    public Boolean pushEditStoreBeta(OrgParamEntity orgParamEntity) {
        log.info("MainDataRpc-pushEditStoreBeta param{}", JacksonUtil.toStr(orgParamEntity));
        Result<String> result = storeProvider.editStoreBeta(orgParamEntity);
        log.info("result={}", JacksonUtil.toStr(result));
        // 处理返回数据
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "push mainData fail:" + result.getMessage());
        }
        return Boolean.TRUE;
    }

}

