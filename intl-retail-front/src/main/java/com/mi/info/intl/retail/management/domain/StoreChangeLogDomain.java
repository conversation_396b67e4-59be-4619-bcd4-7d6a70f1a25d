package com.mi.info.intl.retail.management.domain;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 门店变更日志领域对象
 */
@Data
public class StoreChangeLogDomain implements Serializable {
    private Integer id;
    private String storeName;
    private String storeCode;
    private Integer positionChangeId;
    private String rmsStoreCode;
    private Date effectiveTime;
    private String storeClass;
    private Integer workableType;
    private Integer hasPc;
    private Integer hasSr;
    private String storeStatus;
    private String changeReason;
    private Date recordCreateTime;
    private String storeGrade;
    private String storeType;
    private String storeChannelType;
    private Integer hasFront;
    private Integer hasPos;
    private Integer remainingXiaomiStore;
    private Integer effectiveStatus;
}
