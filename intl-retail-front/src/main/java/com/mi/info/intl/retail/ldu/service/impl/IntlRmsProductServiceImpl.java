package com.mi.info.intl.retail.ldu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlRmsProductMapper;
import com.mi.info.intl.retail.ldu.service.IntlRmsProductService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/11 17:46
 */
@Slf4j
@Service
public class IntlRmsProductServiceImpl extends ServiceImpl<IntlRmsProductMapper, IntlRmsProduct> implements IntlRmsProductService, ProductApiService {

    @Resource
    private IntlRmsProductMapper intlRmsProductMapper;


    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    @Override
    public void batchSaveOrUpdate(List<IntlRmsProduct> products) {
        if (CollectionUtils.isEmpty(products)) {
            log.warn("批量保存商品信息为空");
            return;
        }
        List<String> goodsIds = products.stream().map(IntlRmsProduct::getGoodsId).collect(Collectors.toList());
        List<IntlRmsProduct> newProducts = this.queryByGoodsIdList(goodsIds);
        Map<String, IntlRmsProduct> existGoodsMap = newProducts.stream().collect(Collectors.toMap(IntlRmsProduct::getGoodsId, Function.identity()));
        List<IntlRmsProduct> toAdd = Lists.newArrayList();
        List<IntlRmsProduct> toUpdate = Lists.newArrayList();
        for (IntlRmsProduct product : products) {
            if (existGoodsMap.containsKey(product.getGoodsId())) {
                IntlRmsProduct exitProduct = existGoodsMap.get(product.getGoodsId());
                Long id = exitProduct.getId();
                ComponentLocator.getConverter().convert(product, exitProduct);
                exitProduct.setId(id);
                toUpdate.add(exitProduct);
            } else {
                toAdd.add(product);
            }
        }
        if (CollectionUtils.isNotEmpty(toAdd)) {
            this.saveBatch(toAdd);
        }
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            this.updateBatchById(toUpdate);
        }
    }

    @Override
    public List<IntlRmsProduct> queryByGoodsIdList(List<String> goodsIds) {
        if (CollectionUtils.isEmpty(goodsIds)) {
            return Lists.newArrayList();
        }
        return intlRmsProductMapper.selectList(new QueryWrapper<IntlRmsProduct>().in("goods_id", goodsIds));
    }

    @Override
    public List<IntlRmsProduct> queryByCode69List(List<String> code69List) {
        if (CollectionUtils.isEmpty(code69List)) {
            return Lists.newArrayList();
        }
        return intlRmsProductMapper.selectList(new QueryWrapper<IntlRmsProduct>().in("code69", code69List));
    }

    @Override
    public List<IntlRmsProduct> queryBySkuList(List<String> skuList) {
        if (CollectionUtils.isEmpty(skuList)) {
            return Lists.newArrayList();
        }
        return intlRmsProductMapper.selectList(new QueryWrapper<IntlRmsProduct>().in("sku_id", skuList));
    }
    
    @Override
    public Map<Long, ProductInfoDTO> queryProductsByGoodIds(List<String> goodsIds) {
        log.info("查询产品信息, productIds: {}", goodsIds);

        Map<Long, ProductInfoDTO> resultMap = new HashMap<>();

        if (CollectionUtils.isEmpty(goodsIds)) {
            return resultMap;
        }

        try {

            List<IntlRmsProduct> products = intlRmsProductMapper.selectList(new QueryWrapper<IntlRmsProduct>().in("goods_id", goodsIds));

            for (IntlRmsProduct product : products) {
                resultMap.put(product.getId(), converter(product));
            }

            log.info("查询产品信息成功, 查询到{}条记录", resultMap.size());

        } catch (Exception e) {
            log.error("查询产品信息失败, productIds: {}", goodsIds, e);
        }

        return resultMap;
    }

    /**
     *
     * @param goodsIds
     * @return key goods_id
     */
    @Override
    public Map<String, ProductInfoDTO> batchGetByGoodIds(List<String> goodsIds) {

        if (CollectionUtils.isEmpty(goodsIds)) {
            return Collections.emptyMap();
        }
        List<IntlRmsProduct> products = intlRmsProductMapper.selectList(new QueryWrapper<IntlRmsProduct>().in("goods_id", goodsIds));
        if (CollectionUtils.isEmpty(products)) {
            return Collections.emptyMap();
        }
        Map<String, ProductInfoDTO> map = new HashMap<>();
        for (IntlRmsProduct product : products) {
            map.put(product.getGoodsId(), converter(product));
        }
        return map;
    }

    public static ProductInfoDTO converter(IntlRmsProduct product) {
        if (product == null) {
            return null;
        }
        ProductInfoDTO productInfo = new ProductInfoDTO();
        productInfo.setId(product.getId().intValue());
        productInfo.setGoodsId(product.getGoodsId());
        productInfo.setName(product.getEnglishName());
        productInfo.setProductLineEn(product.getProductLineEn());
        productInfo.setSkuName(product.getSkuName());
        productInfo.setIsSn(product.getIsSn());
        productInfo.setSpuId(product.getSpuId());
        productInfo.setSpuName(product.getSpuName());
        productInfo.setSpuNameEn(product.getSpuNameEn());
        productInfo.setCode69(product.getCode69());
        productInfo.setProductLineCode(product.getProductLineCode());
        return productInfo;
    }


}
