package com.mi.info.intl.retail.org.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.core.utils.CollUtils;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.TaskRemindDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.BoolEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionEnableEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionExpireEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.org.app.service.material.convert.NewProductInspectionConvert;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.infra.entity.InspectionRecord;
import com.mi.info.intl.retail.org.infra.mapper.InspectionRecordMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.InspectionRecordReadMapper;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Repository
public class NewProductInspectionRepositoryImpl implements NewProductInspectionRepository {
    
    @Resource
    private InspectionRecordReadMapper inspectionRecordReadMapper;
    
    @Resource
    private InspectionRecordMapper inspectionRecordMapper;
    
    //business_type 字符串 列表
    private static final Integer[] BUSINESS_TYPE_ARRAY = new Integer[] {401, 402, 403, 404};
    
    @Override
    public Page<MaterialInspectionItem> pageMaterialInspection(Page<MaterialInspectionItem> page,
            MaterialInspectionReq request) {
        log.info("分页查询新品物料巡检信息: request={}, pageNum={}, pageSize={}", request, page.getCurrent(),
                page.getSize());
        return inspectionRecordReadMapper.pageMaterialInspection(page, request);
    }
    
    @Override
    public boolean existsUnCompletedTaskByOwner(String inspectionOwner) {
        LambdaQueryWrapper<InspectionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(InspectionRecord::getInspectionOwner, inspectionOwner)
                // 只查询业务类型为401,402,403,404 的数据
                .in(InspectionRecord::getBusinessType, BUSINESS_TYPE_ARRAY).select(InspectionRecord::getId);
        Page<InspectionRecord> page = new Page<>(1, 1);
        page.setSearchCount(false);
        inspectionRecordReadMapper.selectPage(page, queryWrapper);
        return page.getSize() > 0;
    }
    
    @Override
    public Page<InspectionRecordPageItem> getMaterialInspectionRecordPageItem(InspectionRecordPageRequest request) {
        IPage<InspectionRecordPageItem> page = new Page<>(request.getPageNum(), request.getPageSize());
        return inspectionRecordReadMapper.pageMaterialInspectionRecordItem(page, request);
    }
    
    @Override
    public MaterialInspectionDomain getById(Long id) {
        InspectionRecord entity = inspectionRecordReadMapper.selectOne(
                Wrappers.lambdaQuery(InspectionRecord.class).eq(InspectionRecord::getId, id), false);
        return NewProductInspectionConvert.INSTANCE.entityToDomain(entity);
    }
    
    @Override
    public int updateVerifyStatus(MaterialInspectionDomain record) {
        InspectionRecord entity = NewProductInspectionConvert.INSTANCE.domainToEntity(record);
        return inspectionRecordMapper.updateVerifyStatus(entity, I18nDesc.safeGetCode(record.getPreInspectionStatus()));
    }
    
    @Override
    public void updateInspectionRecordToExpired(List<Long> taskInstanceIds) {
        if (CollectionUtils.isEmpty(taskInstanceIds)) {
            return;
        }
        LambdaUpdateWrapper<InspectionRecord> updateWrapper = Wrappers.<InspectionRecord>lambdaUpdate()
                .in(InspectionRecord::getTaskInstanceId, taskInstanceIds)
                .set(InspectionRecord::getExpire, BoolEnum.YES.getCode())
                .set(InspectionRecord::getUpdatedTime, System.currentTimeMillis());
        inspectionRecordMapper.update(updateWrapper);
    }
    
    @Override
    public List<TaskRemindDTO> getNoCompletedList(Long miId) {
        return inspectionRecordReadMapper.getNoCompletedList(miId);
    }
    
    @Override
    public void batchSave(List<MaterialInspectionDomain> records) {
        List<InspectionRecord> entities = CollUtils.mapping(records,
                NewProductInspectionConvert.INSTANCE::domainToEntity);
        Lists.partition(entities, 200).forEach(inspectionRecordMapper::batchInsert);
    }
    
    @Override
    public List<Long> getUnexistsTaskInstanceIds(List<Long> taskInstanceIds) {
        if (CollectionUtils.isEmpty(taskInstanceIds)) {
            return Collections.emptyList();
        }
        List<InspectionRecord> inspectionRecords = inspectionRecordMapper.selectList(
                Wrappers.<InspectionRecord>lambdaQuery().in(InspectionRecord::getTaskInstanceId, taskInstanceIds));
        Set<Long> exists = CollUtils.mappingToSet(inspectionRecords, InspectionRecord::getTaskInstanceId);
        return taskInstanceIds.stream().filter(e -> !exists.contains(e)).collect(Collectors.toList());
    }
    
    @Override
    public List<String> getStoreModelList(String positionCode) {
        if (StringUtils.isEmpty(positionCode)) {
            return Collections.emptyList();
        }
        List<String> projects = inspectionRecordReadMapper.getStoreModelList(positionCode);
        return projects.stream().filter(StringUtils::isNotEmpty).map(p -> JsonUtil.jsonArr2beanList(p, String.class))
                .flatMap(List::stream).distinct().collect(Collectors.toList());
    }
    
    @Override
    public List<MaterialInspectionDomain> getListForIntlStoreMaterialStatus(MaterialInspectionReq req) {
        LambdaQueryWrapper<InspectionRecord> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(StringUtils.isNotEmpty(req.getBusinessCode()), InspectionRecord::getBusinessCode,
                req.getBusinessCode());
        queryWrapper.eq(req.getBusinessType() != null, InspectionRecord::getBusinessType, req.getBusinessType());
        //        queryWrapper.eq(InspectionRecord::getExpire, InspectionExpireEnum.NOT_EXPIRE.getCode());
        //未过期 根据当前时间判断 截止时间要大于当前时间
        queryWrapper.ge(InspectionRecord::getDeadlineStamp, System.currentTimeMillis());
        queryWrapper.eq(InspectionRecord::getEnable, InspectionEnableEnum.ENABLE.getCode());
        queryWrapper.eq(req.getTaskStatusEnum() != null, InspectionRecord::getTaskStatus, req.getTaskStatusEnum());
        queryWrapper.eq(req.getInspectionFlagCompletionEnum() != null, InspectionRecord::getFlagCompletion,
                req.getInspectionFlagCompletionEnum());
        queryWrapper.orderByDesc(InspectionRecord::getCreatedTime);
        queryWrapper.last("limit 1");
        List<InspectionRecord> inspectionRecords = inspectionRecordMapper.selectList(queryWrapper);
        log.info("NewProductInspectionRepositoryImpl#getListForIntlStoreMaterialStatus inspectionRecords:{}",
                RetailJsonUtil.toJson(inspectionRecords));
        return NewProductInspectionConvert.INSTANCE.entityToDomainList(inspectionRecords);
    }
}
