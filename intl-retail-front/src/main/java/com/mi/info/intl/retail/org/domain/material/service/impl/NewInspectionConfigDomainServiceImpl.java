package com.mi.info.intl.retail.org.domain.material.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.text.StrPool;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.read.listener.PageReadListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import com.mi.info.intl.retail.core.exception.RetailRunTimeException;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationInfoVO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.BrainPlatformOperateTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.CycleTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.InspectionConfigTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.InspectionRuleStautsEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.PosmMaterialEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TargetTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.YESNOEnum;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectResp;
import com.mi.info.intl.retail.org.app.service.material.convert.NewProductInspectionConvert;
import com.mi.info.intl.retail.org.domain.material.config.NewInspectionConfig;
import com.mi.info.intl.retail.org.domain.material.service.IntlInspectionRuleRelationService;
import com.mi.info.intl.retail.org.domain.material.service.NewInspectionConfigDomainService;
import com.mi.info.intl.retail.org.domain.service.RuleConfigService;
import com.mi.info.intl.retail.org.domain.util.ContextUtil;
import com.mi.info.intl.retail.org.domain.util.RpcUtil;
import com.mi.info.intl.retail.org.infra.entity.IntlInspectionRuleRelation;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.RuleConfig;
import com.mi.info.intl.retail.org.infra.mapper.IntlNewProductTargetMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.TaskDefinitionReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.TaskDefinitionReq.PushPeriod.PushCycleRule;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.TaskDefinitionReq.PushPeriod.PushOnceRule;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.TaskDefinitionStatusOutReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.admin.AddTaskDefinitionResp;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformOuterProvider;
import com.xiaomi.cnzone.maindataapi.model.dto.common.ConfigKV2;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import com.xiaomi.cnzone.storems.api.model.req.store.CommonConfigReq;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.io.IOUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Validator;

@Slf4j
@Service
public class NewInspectionConfigDomainServiceImpl implements NewInspectionConfigDomainService {

    @Resource
    private RuleConfigService ruleConfigService;

    @Resource
    private StringRedisTemplate stringRedisTemplate;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @DubboReference(group = "${center.dubbo.group:}", check = false, timeout = 20000)
    private BrainPlatformOuterProvider brainPlatformOuterProvider;
    @DubboReference(group = "${maindata.dubbo.group:}", version = "1.0", check = false, interfaceClass = StoreRelateProvider.class)
    private StoreRelateProvider storeRelateProvider;

    @Resource
    private NewInspectionConfig newInspectionConfig;

    @Resource
    private FdsService fdsService;

    @Resource
    private JobTriggerHelper jobTriggerHelper;

    @Resource
    private IntlNewProductTargetMapper intlNewProductTargetMapper;

    @Resource
    private Validator validator;

    @Resource
    private NrJobService nrJobService;

    @Resource
    private IntlInspectionRuleRelationService inspectionRuleRelationService;

    private static final String RETAIL_TENANT_ID = "2";
    private static final String CHANNEL_RETAIL_TAG = "CHANNEL_RETAIL";
    private static final String INTERNATIONAL_CHANNEL = "INTERNATIONAL_CHANNEL";
    private static final String INTERNATIONAL_CHANNEL_RETAIL = "INTERNATIONAL_CHANNEL_RETAIL";
    private static final String SHOP_BUSINESS_CENTER = "shopBusinessCenter";
    private static final String CHANNEL_RETAIL = "channelRetail";
    private static final String INSPECTION_FILE_NAME = "巡检任务配置.xlsx";
    private static final String INSPECTION_FILE_NAME_PREFIX = "material_inspection";
    private static final String RULE = "RULE";
    private static final String PATTERN_00_00 = "00:00";
    private static final String HH_MM = "HH:mm";

    @Override
    public List<Map<String, Object>> getEnumByType(InspectionTaskConfigurationPageRequest dto) {
        List<Map<String, Object>> map;
        InspectionConfigTypeEnum inspectionConfigTypeEnum =
                Optional.ofNullable(InspectionConfigTypeEnum.valueOfCode(dto.getType()))
                        .orElse(InspectionConfigTypeEnum.TASK_TYPE);
        switch (inspectionConfigTypeEnum) {
            case TASK_TYPE:
                map = TaskTypeEnum.getNode();
                break;
            case CYCLE_TYPE:
                map = CycleTypeEnum.getNode();
                break;
            case POSM_MATERIAL:
                map = PosmMaterialEnum.getNode();
                break;
            case TARGET_TYPE:
                map = TargetTypeEnum.getNode();
                break;
            case RULE_STAUTS:
                map = InspectionRuleStautsEnum.getNode();
                break;
            case STORE:
                String language = RequestContextInfo.getLanguage();
                CommonConfigReq req = new CommonConfigReq();
                req.setBusinessScene(CHANNEL_RETAIL);
                req.setLanguage(language);
                CommonConfigDTO2 rpcResult = RpcUtil.getRpcResult(storeRelateProvider.get3CCommonConfig(req));
                map = ContextUtil.getNode(rpcResult.getStoreLevels(), ConfigKV2::getValue, ConfigKV2::getValue);
                break;
            default:
                throw new RetailRunTimeException("Invalid type");
        }
        return map;
    }

    @Override
    public Page<InspectionTaskConfigurationPageResponse> findInspectionTaskPage(
            Page<InspectionTaskConfigurationPageResponse> page, InspectionTaskConfigurationPageRequest dto) {
        Page<InspectionTaskConfigurationPageResponse> data = ruleConfigService.find4Page(page, dto);
        List<InspectionTaskConfigurationPageResponse> records = data.getRecords();
        Map<String, IntlRmsCountryTimezone> countryTimezoneMap = getCountryTimezoneMap(records);
        for (InspectionTaskConfigurationPageResponse inspectionTaskConfiguration : records) {
            inspectionTaskConfiguration.setTaskTypeDesc(
                    TaskTypeEnum.getDescByCode(inspectionTaskConfiguration.getTaskType()));
            inspectionTaskConfiguration.setCycleTypeDesc(
                    CycleTypeEnum.getFormatNameByCode(inspectionTaskConfiguration.getCycleType(),
                            inspectionTaskConfiguration.getCustomCycleDays()));
            IntlRmsCountryTimezone intlRmsCountryTimezone =
                    countryTimezoneMap.get(inspectionTaskConfiguration.getCountry());
            inspectionTaskConfiguration.setRuleStatusDesc(
                    InspectionRuleStautsEnum.getNameByCode(inspectionTaskConfiguration.getRuleStatus()));
            inspectionTaskConfiguration.setAllowPhotoFromGalleryDesc(
                    YESNOEnum.getNameByCode(inspectionTaskConfiguration.getAllowPhotoFromGallery()));
            if (Objects.nonNull(intlRmsCountryTimezone)) {
                inspectionTaskConfiguration.setRegionDesc(intlRmsCountryTimezone.getArea());
                inspectionTaskConfiguration.setCountryDesc(intlRmsCountryTimezone.getCountryName());
            }
            inspectionTaskConfiguration.setCreationTimeDesc(
                    timeDefaultConvert(inspectionTaskConfiguration.getCreationTime()));
            inspectionTaskConfiguration.setModificationTimeDesc(
                    timeDefaultConvert(inspectionTaskConfiguration.getModificationTime()));

            inspectionTaskConfiguration.setStartTimeDesc(
                    IntlTimeUtil.parseTimestampToAreaTime(inspectionTaskConfiguration.getCountry(),
                            inspectionTaskConfiguration.getStartTime(),
                            DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            inspectionTaskConfiguration.setEndTimeDesc(
                    IntlTimeUtil.parseTimestampToAreaTime(inspectionTaskConfiguration.getCountry(),
                            inspectionTaskConfiguration.getEndTime(),
                            DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
            if (inspectionTaskConfiguration.getProject() != null &&
                    inspectionTaskConfiguration.getProject().length > 0) {
                inspectionTaskConfiguration.setProjectDesc(
                        CharSequenceUtil.join(StrPool.COMMA, inspectionTaskConfiguration.getProject()));
            }

        }
        return data;
    }

    @Override
    public InspectionTaskConfigurationInfoVO getInspectionTaskInfo(InspectionTaskConfigurationRequest dto) {
        RuleConfig ruleConfig = ruleConfigService.getById(dto.getId());
        checkRuleConfigNull(ruleConfig, dto.getId());
        InspectionTaskConfigurationInfoVO inspectionTaskConfigurationInfoVO =
                NewProductInspectionConvert.INSTANCE.ruleConfigToInspectionTaskConfigurationInfoVO(ruleConfig);
        inspectionTaskConfigurationInfoVO.setTaskTypeDesc(
                TaskTypeEnum.getDescByCode(inspectionTaskConfigurationInfoVO.getTaskType()));
        inspectionTaskConfigurationInfoVO.setCycleTypeDesc(
                CycleTypeEnum.getFormatNameByCode(inspectionTaskConfigurationInfoVO.getCycleType(),
                        inspectionTaskConfigurationInfoVO.getCustomCycleDays()));
        inspectionTaskConfigurationInfoVO.setRuleStatusDesc(
                InspectionRuleStautsEnum.getNameByCode(inspectionTaskConfigurationInfoVO.getRuleStatus()));
        inspectionTaskConfigurationInfoVO.setTargetTypeDesc(
                TargetTypeEnum.getNameByCode(inspectionTaskConfigurationInfoVO.getTargetType()));

        Map<String, IntlRmsCountryTimezone> countryTimezoneMapByCountry =
                getCountryTimezoneMapByCountry(Lists.newArrayList(inspectionTaskConfigurationInfoVO.getCountry()));
        IntlRmsCountryTimezone intlRmsCountryTimezone =
                countryTimezoneMapByCountry.get(inspectionTaskConfigurationInfoVO.getCountry());
        if (intlRmsCountryTimezone != null) {
            inspectionTaskConfigurationInfoVO.setRegionDesc(intlRmsCountryTimezone.getArea());
            inspectionTaskConfigurationInfoVO.setCountryDesc(intlRmsCountryTimezone.getCountryName());
        }

        // 时间字段格式化
        inspectionTaskConfigurationInfoVO.setStartTimeDesc(
                IntlTimeUtil.parseTimestampToAreaTime(ruleConfig.getCountry(), ruleConfig.getStartTime(),
                        DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        inspectionTaskConfigurationInfoVO.setEndTimeDesc(
                IntlTimeUtil.parseTimestampToAreaTime(ruleConfig.getCountry(), ruleConfig.getEndTime(),
                        DateTimeFormatter.ofPattern(DatePattern.NORM_DATE_PATTERN)));
        inspectionTaskConfigurationInfoVO.setSuggestedTimeRangeStartDesc(
                IntlTimeUtil.parseTimestampToAreaTime(ruleConfig.getCountry(),
                        ruleConfig.getSuggestedTimeRangeStart(), DateTimeFormatter.ofPattern(HH_MM)));
        inspectionTaskConfigurationInfoVO.setSuggestedTimeRangeEndDesc(
                IntlTimeUtil.parseTimestampToAreaTime(ruleConfig.getCountry(), ruleConfig.getSuggestedTimeRangeEnd(),
                        DateTimeFormatter.ofPattern(HH_MM)));

        inspectionTaskConfigurationInfoVO.setCreationTimeDesc(timeDefaultConvert(ruleConfig.getCreationTime()));
        inspectionTaskConfigurationInfoVO.setModificationTimeDesc(timeDefaultConvert(ruleConfig.getModificationTime()));
        if (CollUtil.isNotEmpty(inspectionTaskConfigurationInfoVO.getProject())) {
            NewProductTargetProjectReq req = new NewProductTargetProjectReq();
            req.setProjectList(Lists.newArrayList(ruleConfig.getProject()));
            List<NewProductTargetProjectResp> newProductTargetProjectRespList =
                    intlNewProductTargetMapper.listByProject(req);
            inspectionTaskConfigurationInfoVO.setNewProductTargetProjectRespList(newProductTargetProjectRespList);
        }
        inspectionTaskConfigurationInfoVO.setPosmMaterialsDesc(
                PosmMaterialEnum.getI18nDescList(inspectionTaskConfigurationInfoVO.getPosmMaterials()));
        return inspectionTaskConfigurationInfoVO;
    }

    @Override
    @Transactional
    public InspectionTaskConfigurationInfoVO insertOrUpdate(InspectionTaskConfigurationDTO dto) {
        Long id = dto.getId();
        InspectionTaskConfigurationInfoVO inspectionTaskConfigurationInfoVO;
        if (Objects.isNull(id)) {
            log.info("巡检任务配置 新增");
            inspectionTaskConfigurationInfoVO = insertInspectionTaskConfiguration(dto);
        } else {
            log.info("巡检任务配置 修改 id {}", dto.getId());
            inspectionTaskConfigurationInfoVO = updateInspectionTaskConfiguration(dto);
        }
        if (BrainPlatformOperateTypeEnum.ENABLE.getCode().equals(dto.getEnableFlag())) {
            log.info("巡检任务配置 启动 id {}", dto.getId());
            InspectionTaskConfigurationDTO submitParam = new InspectionTaskConfigurationDTO();
            submitParam.setId(inspectionTaskConfigurationInfoVO.getId());
            this.submitInspectionTaskConfiguration(submitParam);
        }
        return inspectionTaskConfigurationInfoVO;
    }

    @Override
    public InspectionTaskConfigurationInfoVO insertInspectionTaskConfiguration(InspectionTaskConfigurationDTO dto) {
        validateRequiredFields(dto);
        checkStartTimeAndEndTime(dto);
        validateDaysScope(dto);
        setAssignedStore(dto);
        String newRuleCode = genTaskCode(dto.getRuleCode());
        if (CharSequenceUtil.isNotBlank(newRuleCode)) {
            log.info("insertInspectionTaskConfiguration {}", newRuleCode);
            dto.setRuleCode(newRuleCode);
        }
        resetFields(dto);
        RuleConfig ruleConfig = NewProductInspectionConvert.INSTANCE.inspectionTaskConfigurationDTOToRuleConfig(dto);
        ruleConfig.setRuleStatus(InspectionRuleStautsEnum.DRAFT.getCode());
        setCreatorOrModifier(ruleConfig, true);
        setRegion(ruleConfig);
        ruleConfig.setRuleName("");
        ruleConfigService.save(ruleConfig);

        // 新增ruleId和project关联表
        String[] project = ruleConfig.getProject();
        if (null != project && project.length > 0) {
            List<IntlInspectionRuleRelation> inspectionRuleRelations = new ArrayList<>();
            for (String projectCode : project) {
                IntlInspectionRuleRelation inspectionRuleRelation = new IntlInspectionRuleRelation();
                inspectionRuleRelation.setRuleId(ruleConfig.getId());
                inspectionRuleRelation.setProject(projectCode);
                inspectionRuleRelation.setCountry(ruleConfig.getCountry());
                inspectionRuleRelation.setCreateTime(System.currentTimeMillis());
                inspectionRuleRelation.setUpdateTime(System.currentTimeMillis());
                inspectionRuleRelations.add(inspectionRuleRelation);
            }
            inspectionRuleRelationService.batchInsert(inspectionRuleRelations);
        }
        InspectionTaskConfigurationRequest inspectionTaskConfigurationRequest =
                new InspectionTaskConfigurationRequest();
        inspectionTaskConfigurationRequest.setId(ruleConfig.getId());
        return getInspectionTaskInfo(inspectionTaskConfigurationRequest);
    }

    @Override
    public InspectionTaskConfigurationInfoVO updateInspectionTaskConfiguration(InspectionTaskConfigurationDTO dto) {
        RuleConfig oldRuleConfig = ruleConfigService.getById(dto.getId());
        checkRuleConfigNull(oldRuleConfig, dto.getId());
        log.info("updateInspectionTaskConfiguration ruleStatus {} id {}", oldRuleConfig.getRuleStatus(),
                oldRuleConfig.getId());
        checkRuleConfigIsDraftForSave(oldRuleConfig);
        validateRequiredFields(dto);
        checkStartTimeAndEndTime(dto);
        validateDaysScope(dto);
        setAssignedStore(dto);
        resetFields(dto);
        RuleConfig ruleConfig = NewProductInspectionConvert.INSTANCE.inspectionTaskConfigurationDTOToRuleConfig(dto);
        ruleConfig.setId(oldRuleConfig.getId());
        setCreatorOrModifier(ruleConfig, false);
        setRegion(ruleConfig);
        ruleConfigService.update(ruleConfig,
                new LambdaQueryWrapper<RuleConfig>().eq(RuleConfig::getId, ruleConfig.getId())
                        .eq(RuleConfig::getRuleStatus, InspectionRuleStautsEnum.DRAFT.getCode()));
        // 新增ruleId和project关联表
        batchUpdateRuleProjectRel(ruleConfig);
        InspectionTaskConfigurationRequest inspectionTaskConfigurationRequest =
                new InspectionTaskConfigurationRequest();
        inspectionTaskConfigurationRequest.setId(ruleConfig.getId());
        return getInspectionTaskInfo(inspectionTaskConfigurationRequest);
    }

    @Override
    @Transactional
    public void submitInspectionTaskConfiguration(InspectionTaskConfigurationDTO dto) {
        RuleConfig oldRuleConfig = ruleConfigService.getById(dto.getId());
        checkRuleConfigNull(oldRuleConfig, dto.getId());
        log.info("submitInspectionTaskConfiguration ruleStatus {} id {}", oldRuleConfig.getRuleStatus(),
                oldRuleConfig.getId());
        InspectionTaskConfigurationDTO inspectionTaskConfigurationDTO =
                NewProductInspectionConvert.INSTANCE.ruleConfigToInspectionTaskConfigurationDTO(oldRuleConfig);
        validateRequiredFields(inspectionTaskConfigurationDTO);
        checkRuleConfigIsDraftForActive(oldRuleConfig);
        checkMultiActiveRule(oldRuleConfig);
        //调用大脑接口
        AddTaskDefinitionResp data = createInspectionTaskByBrainPlatform(oldRuleConfig);
        if (Objects.isNull(data) || Objects.isNull(data.getId())) {
            //调用大脑接口失败, 返回实体data或ID为空
            throw new RetailRunTimeException(CharSequenceUtil.format(
                    "Failed to call brain platform API: The returned entity data or ID is null."));
        }

        oldRuleConfig.setTaskDefId(data.getId());
        oldRuleConfig.setRuleStatus(InspectionRuleStautsEnum.ACTIVE.getCode());
        setCreatorOrModifier(oldRuleConfig, false);
        ruleConfigService.update(oldRuleConfig,
                new LambdaQueryWrapper<RuleConfig>().eq(RuleConfig::getId, oldRuleConfig.getId())
                        .eq(RuleConfig::getRuleStatus, InspectionRuleStautsEnum.DRAFT.getCode()));

    }

    private void batchUpdateRuleProjectRel(RuleConfig oldRuleConfig) {
        String[] project = oldRuleConfig.getProject();

        List<IntlInspectionRuleRelation> inspectionRuleRelations =
                inspectionRuleRelationService.selectByRuleId(oldRuleConfig.getId());

        if (ObjUtil.isNotNull(inspectionRuleRelations)) {
            for (int i = 0; i < inspectionRuleRelations.size(); i++) {
                inspectionRuleRelations.get(i).setProject(project[i]);
                inspectionRuleRelations.get(i).setUpdateTime(System.currentTimeMillis());
            }
            inspectionRuleRelationService.batchUpdate(inspectionRuleRelations);
        }
    }

    @Override
    @Transactional
    public void startOrStopInspectionTask(InspectionTaskConfigurationRequest dto) {
        validatorHandler(validator.validate(dto, InspectionTaskConfigurationRequest.EnableTaskFlag.class));

        RuleConfig oldRuleConfig = ruleConfigService.getById(dto.getId());
        checkRuleConfigNull(oldRuleConfig, dto.getId());
        log.info("startOrStopInspectionTask ruleStatus {} id {} enableTaskFlag {}", oldRuleConfig.getRuleStatus(),
                oldRuleConfig.getId(), dto.getEnableTaskFlag());
        String enableTaskFlag = dto.getEnableTaskFlag();
        log.info("执行巡检任务启停操作，任务ID: {}, 当前状态: {}, 操作类型: {}",
                oldRuleConfig.getId(), oldRuleConfig.getRuleStatus(), enableTaskFlag);

        if (BrainPlatformOperateTypeEnum.DISABLE.getCode().equals(enableTaskFlag)) {
            handleDisableTask(oldRuleConfig);
        } else if (BrainPlatformOperateTypeEnum.ENABLE.getCode().equals(enableTaskFlag)) {
            handleEnableTask(oldRuleConfig);
        } else {
            //巡检任务操作不支持
            throw new RetailRunTimeException("Unsupported inspection task operation.");
        }
    }

    @Override
    public void exportInspectionTask(InspectionTaskConfigurationPageRequest dto) {
        File file = null;
        FdsUploadResult upload = null;
        try {
            file = File.createTempFile("InspectionTaskConfiguration", ExcelTypeEnum.XLSX.getValue());
            Page<InspectionTaskConfigurationPageResponse> inspectionTaskPage;
            Page<InspectionTaskConfigurationPageResponse> page = new Page<>(1, 1000);
            try (ExcelWriter excelWriter = EasyExcelFactory.write(file, InspectionTaskConfigurationPageResponse.class)
                    .build()) {
                WriteSheet writeSheet = EasyExcelFactory.writerSheet("Sheet").build();
                do {
                    inspectionTaskPage = findInspectionTaskPage(page, dto);
                    excelWriter.write(inspectionTaskPage.getRecords(), writeSheet);
                    page.setCurrent(page.getCurrent() + 1);
                } while (page.hasNext());
            }
            String objectName = genFilePath();
            upload = fdsService.upload(objectName, file, true);
        } catch (Exception e) {
            log.error("导出异常 {}", e.getMessage(), e);
        }
        if (Objects.nonNull(upload)) {
            CommonResponse<String> commonResponse =
                    jobTriggerHelper.triggerCommonExportJob(ContextUtil.getMiID().toString(), upload.getUrl());
            if (0 != commonResponse.getCode()) {
                throw new RetailRunTimeException(commonResponse.getMessage());
            }
        } else {
            throw new RetailRunTimeException("Upload failed.");
        }
    }

    @Override
    public InspectionTaskConfigurationDTO uploadAssignedStore(InspectionTaskConfigurationRequest dto) {
        InspectionTaskConfigurationDTO inspectionTaskConfigurationDTO = new InspectionTaskConfigurationDTO();
        List<String> assignedStore = new ArrayList<>();
        String filePath = dto.getFilePath();
        if (CharSequenceUtil.isBlank(filePath)) {
            return inspectionTaskConfigurationDTO;
        }
        try {
            log.info("uploadAssignedStore 零售门店上传 filePath {}", filePath);
            File file = uploadFile(filePath);
            FdsUploadResult upload = fdsService.upload(FileUtil.getName(filePath), file, false);
            EasyExcelFactory.read(file, new PageReadListener<LinkedHashMap<Integer, String>>(
                    data -> {
                        if (CollUtil.isEmpty(data)) {
                            return;
                        }
                        for (LinkedHashMap<Integer, String> item : data) {
                            if (item == null || CharSequenceUtil.isBlank(item.get(0))) {
                                continue;
                            }
                            assignedStore.add(item.get(0));
                        }
                    }
            )).sheet().doRead();
            log.info("uploadAssignedStore 处理后 filePath {} assignedStore {}", upload.getUrl(), assignedStore);
            inspectionTaskConfigurationDTO.setFilePath(upload.getUrl());
            inspectionTaskConfigurationDTO.setAssignedStore(assignedStore);
        } catch (Exception e) {
            log.error("获得导入文件异常 {}", e.getMessage(), e);
            throw new RetailRunTimeException("Failed to retrieve the imported file.");
        }
        return inspectionTaskConfigurationDTO;
    }

    private File uploadFile(String filePath) {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(filePath).build();
        Response response = null;
        File tempFile = FileUtil.createTempFile("newInspection", ExcelTypeEnum.XLSX.getValue(), true);
        try {
            response = client.newCall(request).execute();
            if (!response.isSuccessful()) {
                //获得导入文件失败
                throw new RetailRunTimeException("Failed to retrieve the imported file.");
            }
            if (response.body() != null) {
                try (FileOutputStream fos = new FileOutputStream(tempFile);
                     InputStream inputStream = response.body().byteStream()) {
                    IOUtils.copyLarge(inputStream, fos);
                }
            }
        } catch (IOException e) {
            log.error("获得导入文件异常 {}", e.getMessage(), e);
            //获得导入文件异常
            throw new RetailRunTimeException("Failed to retrieve the imported file.");
        }
        return tempFile;
    }

    /**
     * 设置门店编码
     * 文件路径
     */
    private void setAssignedStore(InspectionTaskConfigurationDTO dto) {
        InspectionTaskConfigurationRequest uploadParam = new InspectionTaskConfigurationRequest();
        uploadParam.setFilePath(dto.getFilePath());
        InspectionTaskConfigurationDTO inspectionTaskConfigurationDTO = uploadAssignedStore(uploadParam);

        dto.setAssignedStore(inspectionTaskConfigurationDTO.getAssignedStore());
        dto.setFilePath(inspectionTaskConfigurationDTO.getFilePath());
    }

    private String genFilePath() {
        return INSPECTION_FILE_NAME_PREFIX + "/" + DateTimeFormatter.ofPattern(
                DatePattern.NORM_DATE_PATTERN).format(LocalDateTime.now()) + "/" + UUID.randomUUID() + "/" +
                INSPECTION_FILE_NAME;
    }

    private void validateRequiredFields(InspectionTaskConfigurationDTO dto) {
        validatorHandler(validator.validate(dto));

        // 任务场景为LDU时，needInspection可以为空
        if (Objects.isNull(dto.getNeedInspection()) &&
                TaskTypeEnum.LDU.getCode().compareTo(dto.getTaskType()) != 0) {
            //是否巡检不能为空
            throw new RetailRunTimeException("The inspection flag cannot be empty.");
        }
        // 任务周期为NONE时，循环数不能为空
        List<Integer> cycleTypeList =
                Lists.newArrayList(CycleTypeEnum.WEEKLY.getCode(), CycleTypeEnum.MONTHLY.getCode(),
                        CycleTypeEnum.CUSTOM.getCode());
        if (cycleTypeList.contains(dto.getCycleType()) && Objects.isNull(dto.getCustomCycleDays())) {
            //自定义天数不能为空
            throw new RetailRunTimeException("The custom cycle days cannot be empty.");
        }

    }

    private void validateDaysScope(InspectionTaskConfigurationDTO dto) {
        Integer cycleType = dto.getCycleType();

        validateNoneCycleDaysRange(cycleType, dto);

        validateWeeklyCycleDaysRange(cycleType, dto);

        validateMonthlyCycleDaysRange(cycleType, dto);

        validateCustomyCycleDaysRange(cycleType, dto);
    }

    private void validateNoneCycleDaysRange(Integer cycleType, InspectionTaskConfigurationDTO dto) {
        if (CycleTypeEnum.NONE.getCode().compareTo(cycleType) == 0) {
            Long startTime = dto.getStartTime();
            Long endTime = dto.getEndTime();
            if (startTime == null || endTime == null) {
                throw new RetailRunTimeException("Start time and end time cannot be null for NONE cycle type.");
            }
            long betweenDay =
                    DateUtil.betweenDay(DateUtil.date(endTime), DateUtil.date(startTime), true);
            log.info("validateCustomCycleDaysScope betweenDay {}, reminderDays {}", betweenDay, dto.getReminderDays());
            if (dto.getReminderDays() == null || dto.getReminderDays() >= betweenDay) {
                //待办提醒天数小于结束日期-开始日期
                throw new RetailRunTimeException(
                        "The reminder days must be less than the difference between the end date and start date.");
            }
        }
    }

    private void validateWeeklyCycleDaysRange(Integer cycleType, InspectionTaskConfigurationDTO dto) {
        Integer customCycleDays = dto.getCustomCycleDays();
        if (CycleTypeEnum.WEEKLY.getCode().compareTo(cycleType) == 0 &&
                (customCycleDays == null || customCycleDays < 1 || customCycleDays > 7)) {
            //Task Cycle为周，自定义规则只能填1~7
            throw new RetailRunTimeException("For a weekly task cycle, the custom rule must be between 1 and 7.");
        }
    }

    private void validateMonthlyCycleDaysRange(Integer cycleType, InspectionTaskConfigurationDTO dto) {
        Integer customCycleDays = dto.getCustomCycleDays();
        if (CycleTypeEnum.MONTHLY.getCode().compareTo(cycleType) == 0 &&
                (customCycleDays == null || customCycleDays < 1 || customCycleDays > 28)) {
            //Task Cycle为月，自定义规则只能填1~28
            throw new RetailRunTimeException("For a monthly task cycle, the custom rule must be between 1 and 28.");
        }
    }

    private void validateCustomyCycleDaysRange(Integer cycleType, InspectionTaskConfigurationDTO dto) {
        Integer customCycleDays = dto.getCustomCycleDays();
        Integer reminderDays = dto.getReminderDays();
        if (CycleTypeEnum.CUSTOM.getCode().compareTo(cycleType) == 0 &&
                (reminderDays == null || customCycleDays == null || customCycleDays <= reminderDays)) {
            //待办提醒天数必须小于自定义天数
            throw new RetailRunTimeException("The reminder days must be less than the custom cycle days.");
        }
    }

    private void resetFields(InspectionTaskConfigurationDTO dto) {
        if (TaskTypeEnum.LDU.getCode().compareTo(dto.getTaskType()) == 0) {
            log.info("LDU 类型重置 needInspection  storeType assignedStorre");
            dto.setNeedInspection(YESNOEnum.NO.getCode());
            dto.setStoreType(null);
            dto.setAssignedStore(null);
        }
    }

    /**
     * 生成任务编码
     */
    private String genTaskCode(String taskCode) {
        if (CharSequenceUtil.isNotBlank(taskCode)) {
            return null;
        }
        String newTaskCode = RULE + DateUtil.format(new Date(), DatePattern.PURE_DATE_PATTERN);
        Long increment;
        if (stringRedisTemplate.hasKey(newTaskCode)) {
            increment = stringRedisTemplate.opsForValue().increment(newTaskCode);
        } else {
            increment = stringRedisTemplate.opsForValue().increment(newTaskCode);
            stringRedisTemplate.expire(newTaskCode, 1, TimeUnit.DAYS);
        }
        String seqPart = String.format("%04d", increment);
        return newTaskCode + seqPart;
    }

    private Map<String, IntlRmsCountryTimezone> getCountryTimezoneMap(
            List<InspectionTaskConfigurationPageResponse> records) {
        Map<String, IntlRmsCountryTimezone> countryTimezoneMap = new HashMap<>();
        List<String> countryList = records.stream().filter(Objects::nonNull)
                .map(InspectionTaskConfigurationPageResponse::getCountry)
                .filter(CharSequenceUtil::isNotBlank).collect(Collectors.toList());
        if (CollUtil.isEmpty(countryList)) {
            return countryTimezoneMap;
        }
        return getCountryTimezoneMapByCountry(countryList);
    }

    /**
     * 获得国家区域
     */
    private Map<String, IntlRmsCountryTimezone> getCountryTimezoneMapByCountry(List<String> countryList) {
        Map<String, IntlRmsCountryTimezone> countryTimezoneMap = new HashMap<>();
        List<IntlRmsCountryTimezone> intlRmsCountryTimezones =
                intlRmsCountryTimezoneMapper.selectList(Wrappers.lambdaQuery(IntlRmsCountryTimezone.class)
                        .in(IntlRmsCountryTimezone::getCountryCode, countryList));
        if (CollUtil.isEmpty(intlRmsCountryTimezones)) {
            return countryTimezoneMap;
        }
        countryTimezoneMap =
                intlRmsCountryTimezones.stream().collect(Collectors.toMap(IntlRmsCountryTimezone::getCountryCode,
                        Function.identity(), (v1, v2) -> v1));
        return countryTimezoneMap;
    }

    /**
     * 设置创建者或修改者
     */
    private void setCreatorOrModifier(RuleConfig ruleConfig, boolean flag) {
        String userName = ContextUtil.getUserName();
        if (flag) {
            ruleConfig.setCreationTime(new Date().getTime());
            ruleConfig.setCreator(userName);
        }
        ruleConfig.setModificationTime(new Date().getTime());
        ruleConfig.setModifier(userName);
    }

    private void setRegion(RuleConfig ruleConfig) {
        log.info("根据国家{}设置区域", ruleConfig.getCountry());
        Map<String, IntlRmsCountryTimezone> countryTimezoneMapByCountry =
                getCountryTimezoneMapByCountry(Lists.newArrayList(ruleConfig.getCountry()));
        IntlRmsCountryTimezone intlRmsCountryTimezone = countryTimezoneMapByCountry.get(ruleConfig.getCountry());
        if (intlRmsCountryTimezone == null) {
            //国家对应的区域编码不存在
            throw new RetailRunTimeException("The region code for the specified country does not exist.");
        }
        ruleConfig.setRegion(intlRmsCountryTimezone.getAreaCode());
    }

    /**
     * 国家、taskType、启动状态不能重复
     */
    private void checkMultiActiveRule(RuleConfig oldRuleConfig) {
        LambdaQueryWrapper<RuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(RuleConfig::getRuleStatus, InspectionRuleStautsEnum.ACTIVE.getCode());
        queryWrapper.eq(RuleConfig::getTaskType, oldRuleConfig.getTaskType());
        queryWrapper.eq(RuleConfig::getCountry, oldRuleConfig.getCountry());
        queryWrapper.ne(RuleConfig::getId, oldRuleConfig.getId());
        log.info("checkMultiActiveRule taskType {} country {} id {}", oldRuleConfig.getTaskType(),
                oldRuleConfig.getCountry(), oldRuleConfig.getId());
        List<RuleConfig> list = ruleConfigService.list(queryWrapper);
        if (CollUtil.isNotEmpty(list)) {
            //国家[{}] + 任务类型[{}]已存在启动任务，不允许启动
            throw new RetailRunTimeException(CharSequenceUtil.format(
                    "A running task already exists for country [{}] and task type [{}], so it cannot be started.",
                    oldRuleConfig.getCountry(), TaskTypeEnum.getDescByCode(oldRuleConfig.getTaskType())));
        }
    }

    /**
     * 启停任务-调用大脑接口
     */
    private boolean callToggleTaskDefinition(RuleConfig oldRuleConfig, String enableTaskFlag) {
        log.info("callToggleTaskDefinition call");
        TaskDefinitionStatusOutReq taskDefinitionStatusOutReq = new TaskDefinitionStatusOutReq();
        taskDefinitionStatusOutReq.setTaskDefinitionId(oldRuleConfig.getTaskDefId());
        taskDefinitionStatusOutReq.setOperateType(enableTaskFlag);
        taskDefinitionStatusOutReq.setRetailTenantId(RETAIL_TENANT_ID);
        taskDefinitionStatusOutReq.setMid(ContextUtil.getMiID());
        taskDefinitionStatusOutReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        try {
            log.info("toggleTaskDefinition req:{}", JSON.toJSONString(taskDefinitionStatusOutReq));
            Result<Void> result = brainPlatformOuterProvider.toggleTaskDefinition(taskDefinitionStatusOutReq);
            if (GeneralCodes.OK.getCode() != result.getCode()) {
                throw new RetailRunTimeException(
                        CharSequenceUtil.format("Failed to call brain platform API: {}", result.getMessage()));
            }
            return true;
        } catch (Exception e) {
            log.error("callToggleTaskDefinition error", e);
            throw new RetailRunTimeException(
                    CharSequenceUtil.format("调用大脑接口[toggleTaskDefinition]失败 {}", e.getMessage(), e));
        }
    }

    /**
     * 创建巡检任务-调用大脑平台
     * 创建步骤。巡检任务配置创建定义
     * 调用大脑接口创建任务
     * 大脑回调零售接口圈人
     */
    private AddTaskDefinitionResp createInspectionTaskByBrainPlatform(RuleConfig oldRuleConfig) {
        log.info("createInspectionTaskByBrainPlatform call");
        TaskDefinitionReq taskDefinitionReq = new TaskDefinitionReq();
        if (Objects.nonNull(oldRuleConfig.getTaskDefId()) && oldRuleConfig.getTaskDefId() > 0) {
            taskDefinitionReq.setTaskDefinitionId(oldRuleConfig.getTaskDefId());
        }
        Map<String, IntlRmsCountryTimezone> countryTimezoneMapByCountry =
                getCountryTimezoneMapByCountry(Lists.newArrayList(oldRuleConfig.getCountry()));

        taskDefinitionReq.setAppSign(CHANNEL_RETAIL_TAG);
        taskDefinitionReq.setArea(oldRuleConfig.getCountry());
        taskDefinitionReq.setTitle(oldRuleConfig.getTaskName());
        taskDefinitionReq.setSubtitle(oldRuleConfig.getTaskName());
        taskDefinitionReq.setBusinessTypeId(Long.valueOf(oldRuleConfig.getTaskType()));
        taskDefinitionReq.setBusinessTypeName(TaskTypeEnum.getNameByCode(oldRuleConfig.getTaskType()));
        taskDefinitionReq.setEventDefinitionIds(TaskTypeEnum.getEventsByCode(oldRuleConfig.getTaskType()));
        taskDefinitionReq.setDescription(oldRuleConfig.getTaskGuidance());
        taskDefinitionReq.setPushTitle(oldRuleConfig.getTaskName());

        taskDefinitionReq.setCreator(oldRuleConfig.getModifier());

        taskDefinitionReq.setSugStartTimeStamp(
                IntlTimeUtil.toBeiJingTimestamp(oldRuleConfig.getSuggestedTimeRangeStart(),
                        oldRuleConfig.getCountry()));
        taskDefinitionReq.setSugEndTimeStamp(
                IntlTimeUtil.toBeiJingTimestamp(oldRuleConfig.getSuggestedTimeRangeEnd(), oldRuleConfig.getCountry()));
        taskDefinitionReq.setCycleType(oldRuleConfig.getCycleType());

        setPushPeriod(oldRuleConfig, taskDefinitionReq);
        setChannelRetail(oldRuleConfig, taskDefinitionReq, countryTimezoneMapByCountry);
        setFixField(taskDefinitionReq);
        log.info("createInspectionTaskByBrainPlatform body {}", JSON.toJSONString(taskDefinitionReq));
        try {
            Result<AddTaskDefinitionResp> result =
                    brainPlatformOuterProvider.addOrEditTaskDefinitionReturnIdForWlXunjian(taskDefinitionReq);
            if (GeneralCodes.OK.getCode() != result.getCode()) {
                throw new RetailRunTimeException(
                        CharSequenceUtil.format("Failed to call brain platform API: {}", result.getMessage()));
            }
            log.info("createInspectionTaskByBrainPlatform result {}", JSON.toJSONString(result.getData()));
            return result.getData();
        } catch (Exception e) {
            log.error("createInspectionTaskByBrainPlatform error", e);
            throw new RetailRunTimeException(
                    StrUtil.format("调用大脑接口[addOrEditTaskDefinitionReturnId]失败 {}", e.getMessage(), e));
        }
    }

    /***
     * 设置固定字段
     */
    private void setFixField(TaskDefinitionReq taskDefinitionReq) {
        taskDefinitionReq.setDepartment(SHOP_BUSINESS_CENTER);
        taskDefinitionReq.setRetailTenantId(RETAIL_TENANT_ID);
        taskDefinitionReq.setRetailAppSign(CHANNEL_RETAIL_TAG);
        taskDefinitionReq.setRegion(INTERNATIONAL_CHANNEL);
        taskDefinitionReq.setLocale(Lists.newArrayList(INTERNATIONAL_CHANNEL_RETAIL));
        taskDefinitionReq.setExecutorType(5);
        taskDefinitionReq.setEmergencyPriority(2);
        taskDefinitionReq.setEmergencySwitch(0);
        taskDefinitionReq.setIncrementalPush(0);
        taskDefinitionReq.setSendFeishuMessage(0);
        TaskDefinitionReq.PushMessageRule pushMessageRule = new TaskDefinitionReq.PushMessageRule();
        pushMessageRule.setPushMessageOrNot(1);
        taskDefinitionReq.setPushMessageRule(pushMessageRule);
        taskDefinitionReq.setTaskTypeId(9);
        taskDefinitionReq.setTaskSecondTypeId(2001001);
        taskDefinitionReq.setTaskSecondTypeName("Rn 14 Task");
        taskDefinitionReq.setRank(15);
        taskDefinitionReq.setNoNeedComplete(0);
        taskDefinitionReq.setOuterTrigger(0);
        taskDefinitionReq.setUpdateFlag(false);
        taskDefinitionReq.setTaskMode(1);
        taskDefinitionReq.setParentTaskDefinitionId(0L);
        taskDefinitionReq.setTaskDuration(0);
        taskDefinitionReq.setFrequency(0);
    }

    /**
     * 设置渠道零售需要的额外字段
     */
    private void setChannelRetail(RuleConfig oldRuleConfig, TaskDefinitionReq taskDefinitionReq,
                                  Map<String, IntlRmsCountryTimezone> countryTimezoneMapByCountry) {
        TaskDefinitionReq.ChannelRetail channelRetail = new TaskDefinitionReq.ChannelRetail();
        channelRetail.setAreaList(Lists.newArrayList(oldRuleConfig.getCountry()));
        channelRetail.setRegion(oldRuleConfig.getRegion());
        IntlRmsCountryTimezone intlRmsCountryTimezone = countryTimezoneMapByCountry.get(oldRuleConfig.getCountry());
        if (intlRmsCountryTimezone != null) {
            channelRetail.setAreaNameList(Lists.newArrayList(intlRmsCountryTimezone.getCountryName()));
        }
        channelRetail.setAllRetailer(true);
        //ldu 4 其他为3。根据不同的type圈人
        if (TaskTypeEnum.LDU.getCode().compareTo(oldRuleConfig.getTaskType()) == 0) {
            channelRetail.setType(4);
        } else {
            channelRetail.setType(3);
        }
        channelRetail.setStoreType(Lists.newArrayList(oldRuleConfig.getStoreType()));
        if (oldRuleConfig.getAssignedStore() != null) {
            channelRetail.setStoreCodes(Lists.newArrayList(oldRuleConfig.getAssignedStore()));
        }
        channelRetail.setProject(Lists.newArrayList(oldRuleConfig.getProject()));
        taskDefinitionReq.setChannelRetail(channelRetail);

    }

    /**
     * 设置推送周期
     */
    private void setPushPeriod(RuleConfig oldRuleConfig, TaskDefinitionReq taskDefinitionReq) {
        PushOnceRule pushOnceRule = null;
        PushCycleRule pushCycleRule = null;

        if (CycleTypeEnum.NONE.getCode().compareTo(oldRuleConfig.getCycleType()) == 0) {
            //endTime
            Date endTime = IntlTimeUtil.toBeiJingTime(oldRuleConfig.getEndTime(), oldRuleConfig.getCountry());
            DateTime endTimeEndOfDay = DateUtil.endOfDay(endTime);

            //startTime转为北京时间字符串
            String startTimeStr =
                    IntlTimeUtil.toBeiJingTimeString(oldRuleConfig.getStartTime(), oldRuleConfig.getCountry());
            Long startTime =
                    IntlTimeUtil.toBeiJingTimestamp(oldRuleConfig.getStartTime(), oldRuleConfig.getCountry());
            log.info("setPushPeriod none endTime {} endTimeEndOfDay {} startTimeStr {} startTime {}", endTime,
                    endTimeEndOfDay, startTimeStr, startTime);
            taskDefinitionReq.setDeadlineStamp(endTimeEndOfDay.getTime());
            taskDefinitionReq.setTaskDefinitionEndTimeStamp(endTimeEndOfDay.getTime());
            pushOnceRule = new PushOnceRule();
            pushOnceRule.setDelayPush(1);
            pushOnceRule.setDelayDate(startTimeStr);
            pushOnceRule.setDelayDateStamp(startTime);

        } else {
            Date endTime = IntlTimeUtil.toBeiJingTime(oldRuleConfig.getEndTime(), oldRuleConfig.getCountry());
            Date startTime = IntlTimeUtil.toBeiJingTime(oldRuleConfig.getStartTime(), oldRuleConfig.getCountry());
            DateTime startTimeEndOfDay = DateUtil.endOfDay(startTime);
            DateTime endTimeEndOfDay = DateUtil.endOfDay(endTime);
            log.info("setPushPeriod not none endTime {} startTime {} startTimeEndOfDay {} endTimeEndOfDay {}", endTime,
                    startTime, startTimeEndOfDay, endTimeEndOfDay);

            taskDefinitionReq.setDeadlineStamp(startTimeEndOfDay.getTime());
            taskDefinitionReq.setTaskDefinitionEndTimeStamp(endTimeEndOfDay.getTime());

            pushCycleRule = new PushCycleRule();
            CycleTypeEnum cycleTypeEnum = CycleTypeEnum.valueOfCode(oldRuleConfig.getCycleType());
            if (cycleTypeEnum != null) {
                cycleRuleProcess(cycleTypeEnum, oldRuleConfig, pushCycleRule);
            }
        }
        Long sugStartTime =
                IntlTimeUtil.toBeiJingTimestamp(oldRuleConfig.getSuggestedTimeRangeStart(), oldRuleConfig.getCountry());
        Long sugEndTime =
                IntlTimeUtil.toBeiJingTimestamp(oldRuleConfig.getSuggestedTimeRangeEnd(), oldRuleConfig.getCountry());
        log.info("setPushPeriod sugStartTime {} sugEndTime {}", sugStartTime, sugEndTime);
        taskDefinitionReq.setSugStartTimeStamp(sugStartTime);
        taskDefinitionReq.setSugEndTimeStamp(sugEndTime);

        //规则
        TaskDefinitionReq.PushPeriod pushPeriod = new TaskDefinitionReq.PushPeriod();
        pushPeriod.setPushOnceRule(pushOnceRule);
        pushPeriod.setPushCycleRule(pushCycleRule);
        pushPeriod.setEffectiveTimeStamp(
                IntlTimeUtil.toBeiJingTimestamp(oldRuleConfig.getStartTime(), oldRuleConfig.getCountry()));
        pushPeriod.setPushType(oldRuleConfig.getCycleType());
        taskDefinitionReq.setPushPeriod(pushPeriod);
    }

    /**
     * 周期设置
     */
    private void cycleRuleProcess(CycleTypeEnum cycleTypeEnum, RuleConfig oldRuleConfig, PushCycleRule pushCycleRule) {
        switch (cycleTypeEnum) {
            case DAILY:
                pushCycleRule.setDayHourMinute(PATTERN_00_00);
                break;
            case WEEKLY:
                pushCycleRule.setWeekDay(oldRuleConfig.getCustomCycleDays());
                //零点执行
                pushCycleRule.setDayHourMinute(PATTERN_00_00);
                break;
            case MONTHLY:
                pushCycleRule.setMonthDay(oldRuleConfig.getCustomCycleDays());
                //零点执行
                pushCycleRule.setDayHourMinute(PATTERN_00_00);
                break;
            case CUSTOM:
                pushCycleRule.setCustomDay(oldRuleConfig.getCustomCycleDays());
                break;
            default:
                //不支持的任务周期类型
                throw new RetailRunTimeException("Unsupported task cycle type");
        }
    }

    /**
     * 停用
     */
    private void handleDisableTask(RuleConfig ruleConfig) {
        log.info("停用巡检任务，任务ID: {}", ruleConfig.getId());
        // 检查任务状态 - 只有启用状态的任务才能被停用
        checkRuleConfigIsActiveForInactive(ruleConfig);
        // 调用大脑平台接口停用任务
        callToggleTaskDefinition(ruleConfig, BrainPlatformOperateTypeEnum.DISABLE.getCode());
        ruleConfig.setRuleStatus(InspectionRuleStautsEnum.INACTIVE.getCode());
        setCreatorOrModifier(ruleConfig, false);
        ruleConfigService.update(ruleConfig,
                new LambdaQueryWrapper<RuleConfig>().eq(RuleConfig::getId, ruleConfig.getId())
                        .eq(RuleConfig::getRuleStatus, InspectionRuleStautsEnum.ACTIVE.getCode()));
        log.info("成功停用巡检任务，任务ID: {}", ruleConfig.getId());
    }

    /**
     * 启动
     */
    private void handleEnableTask(RuleConfig ruleConfig) {
        // 检查任务状态 - 只有草稿状态的任务才能被启用
        checkRuleConfigIsDraftForActive(ruleConfig);

        log.info("启用巡检任务，任务ID: {}", ruleConfig.getId());
        InspectionTaskConfigurationDTO inspectionTaskConfigurationDTO = new InspectionTaskConfigurationDTO();
        inspectionTaskConfigurationDTO.setId(ruleConfig.getId());
        this.submitInspectionTaskConfiguration(inspectionTaskConfigurationDTO);
        log.info("成功启用巡检任，任务ID: {}", ruleConfig.getId());
    }

    /**
     * 检查巡检任务配置是否为草稿状态，非草稿状态不允许启用
     */
    private void checkRuleConfigIsDraftForActive(RuleConfig ruleConfig) {
        if (InspectionRuleStautsEnum.DRAFT.getCode().compareTo(ruleConfig.getRuleStatus()) != 0) {
            //巡检任务配置不是草稿状态，不允许启用
            throw new RetailRunTimeException(
                    CharSequenceUtil.format(
                            "The inspection task configuration is not in the draft state, so it cannot be enabled."));
        }
    }

    /**
     * 检查巡检任务配置是否为草稿状态，非草稿状态不允许保存
     */
    private void checkRuleConfigIsDraftForSave(RuleConfig ruleConfig) {
        if (InspectionRuleStautsEnum.DRAFT.getCode().compareTo(ruleConfig.getRuleStatus()) != 0) {
            //巡检任务配置不是草稿状态，不允许保存
            throw new RetailRunTimeException(
                    CharSequenceUtil.format(
                            "The inspection task configuration is not in the draft state, so it cannot be saved."));
        }
    }

    /**
     * 检查巡检任务配置是否为启动状态，非启动状不允许停用
     */
    private void checkRuleConfigIsActiveForInactive(RuleConfig ruleConfig) {
        if (InspectionRuleStautsEnum.ACTIVE.getCode().compareTo(ruleConfig.getRuleStatus()) != 0) {
            //巡检任务配置不是启用状态，不允许停止
            throw new RetailRunTimeException(
                    CharSequenceUtil.format(
                            "The inspection task configuration is not in the enabled state, so it cannot be stopped."));
        }
    }

    /**
     * 检查巡检任务配置是否为空
     */
    private void checkRuleConfigNull(RuleConfig ruleConfig, Long id) {
        if (Objects.isNull(ruleConfig)) {
            //根据ID{}查询,该巡检任务配置不存在
            throw new RetailRunTimeException(
                    CharSequenceUtil.format("According to ID{}, the inspection task configuration does not exist.",
                            id));
        }
    }

    /**
     * 检查开始结束时间的有效性
     */
    private void checkStartTimeAndEndTime(InspectionTaskConfigurationDTO dto) {
        Long startTime = dto.getStartTime();
        Long endTime = dto.getEndTime();
        if (startTime.compareTo(endTime) > 0) {
            //开始时间不能大于结束时间
            throw new RetailRunTimeException("The start time cannot be greater than the end time");
        }
        long currentTimeMillis = System.currentTimeMillis();
        if (startTime.compareTo(currentTimeMillis) < 0) {
            //开始时间不能大于当前时间
            throw new RetailRunTimeException("The start time cannot be less than the current time");
        }
        if (endTime.compareTo(currentTimeMillis) < 0) {
            //结束时间不能小于当前时间
            throw new RetailRunTimeException("The end time cannot be less than the current time");
        }
    }

    private String timeDefaultConvert(Long time) {
        if (time == null) {
            return null;
        }
        return DateUtil.format(DateUtil.date(time), DatePattern.NORM_DATETIME_PATTERN);
    }

    /**
     * 抛出Validator处理的校验
     */
    private <T> void validatorHandler(Set<ConstraintViolation<T>> validate) {
        if (CollUtil.isNotEmpty(validate)) {
            throw new RetailRunTimeException(validate.iterator().next().getMessage());
        }
    }
}
