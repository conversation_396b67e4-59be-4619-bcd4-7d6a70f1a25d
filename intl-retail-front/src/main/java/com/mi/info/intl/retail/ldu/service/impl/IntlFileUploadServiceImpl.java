package com.mi.info.intl.retail.ldu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.JsonNode;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.core.utils.IntlRetailAssert;
import com.mi.info.intl.retail.intlretail.service.api.upload.FileUploadInfo;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.ldu.enums.ResultCodeEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlFileUpload;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlFileUploadMapper;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.api.so.upload.IntlSoImeiApiService;
import com.mi.info.intl.retail.api.so.upload.IntlSoQtyApiService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@Service
public class IntlFileUploadServiceImpl extends ServiceImpl<IntlFileUploadMapper, IntlFileUpload> implements IntlFileUploadService {


    @Autowired
    private IntlFileUploadMapper intlFileUploadMapper;

    @Autowired
    private IntlSoImeiApiService intlSoImeiApiService;

    @Autowired
    private IntlSoQtyApiService intlSoQtyApiService;

    @Override
    public CommonApiResponse<Object> updateByGuid(Object requestBody, JsonNode rootNode) {

        JsonNode photosNode = rootNode.path("Photos");
        if (photosNode.isArray() && photosNode.size() > 0) {
            JsonNode photoNode = photosNode.get(0);
            String moduleName = photoNode.path("ModuleName").asText();

            String guid = photoNode.path("GUID").asText();
            String fdsUrl = photoNode.path("fdsUrl").asText();

            if (StringUtils.isNotBlank(guid) && StringUtils.isNotBlank(fdsUrl)) {
                UpdateWrapper<IntlFileUpload> updateWrapper = new UpdateWrapper<>();
                updateWrapper.eq("guid", guid);
                updateWrapper.set("module_name", moduleName);
                updateWrapper.set("fds_url", fdsUrl);
                updateWrapper.set("suffix", getFileSuffix(fdsUrl));

                intlFileUploadMapper.update(updateWrapper);
            } else {
                log.warn("GUID or fdsUrl is blank in the request.");
            }
        } else {
            log.warn("Photos array is empty or not present in the request.");
        }
        return new CommonApiResponse<>(ResultCodeEnum.SUCCESS.getCode(), "ok", "");
    }


    @Override
    @Transactional
    public CommonApiResponse<Object> save(FileUploadInfo request) {
        List<FileUploadInfo.MetaData> metaDataList = request.getMetaDataList();
        IntlRetailAssert.notEmpty(metaDataList, "metaDataList can not be null");
        IntlRetailAssert.nonNull(request.getUploaderName(), "uploaderName can not be null");
        IntlRetailAssert.nonNull(FileUploadEnum.getByCode(request.getModuleName()), "unsupported module name");
        List<IntlFileUpload> records = new ArrayList<>();
        List<String> guids = new ArrayList<>();
        for (FileUploadInfo.MetaData metaData : metaDataList) {
            String guid = metaData.getGuid();
            Assert.notNull(guid, "guid can not be null");
            guids.add(guid);
            List<String> fdsUrls = metaData.getFdsUrls();
            if (CollectionUtils.isEmpty(fdsUrls)) {
                fdsUrls = Collections.singletonList(null);
            }
            fdsUrls.forEach(fdsUrl -> records.add(toIntlFileUpload(request, guid, fdsUrl)));
        }
        LambdaQueryWrapper<IntlFileUpload> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(IntlFileUpload::getGuid, guids);
        intlFileUploadMapper.delete(wrapper);
        Lists.partition(records, 200).forEach(intlFileUploadMapper::batchInsertDatas);

        // 新增逻辑：根据模块名称更新相应数据的图片存在标识
        updatePhotoExistFlag(request);

        return new CommonApiResponse<>(null);
    }

    @Override
    @Transactional
    public CommonApiResponse<Object> saveSimple(List<FileUploadInfo.MetaData> metaDataList, FileUploadEnum moduleName, String uploaderName) {
        Assert.notEmpty(metaDataList, "metaDataList can not be null");
        Assert.notNull(moduleName, "moduleName can not be null");
        Assert.notNull(uploaderName, "uploaderName can not be null");

        FileUploadInfo fileUploadInfo = new FileUploadInfo();
        fileUploadInfo.setMetaDataList(metaDataList);
        fileUploadInfo.setModuleName(moduleName.getCode());
        fileUploadInfo.setUploaderName(uploaderName);

        return save(fileUploadInfo);
    }

    private IntlFileUpload toIntlFileUpload(FileUploadInfo request, String guid, String fdsUrl) {
        IntlFileUpload fileUpload = new IntlFileUpload();
        // 必填字段（根据业务需求调整）
        fileUpload.setGuid(guid); // 使用传入的GUID
        fileUpload.setFdsUrl(fdsUrl); // 文件存储URL
        fileUpload.setModuleName(request.getModuleName()); // 模块名称
        fileUpload.setUploaderName(request.getUploaderName()); // 上传者

        fileUpload.setRelatedId(request.getRelatedId());
        fileUpload.setSuffix(getFileSuffix(fdsUrl));

        // 布尔类型转换（假设前端传0/1或true/false）
        fileUpload.setIsOfflineUpload(request.isOfflineUpload() ? 1 : 0);
        fileUpload.setIsUploadedToBlob(request.isUploadedToBlob() ? 1 : 0);
        fileUpload.setIsNoWatermark(request.isNoWatermark() ? 1 : 0);

        // 时间戳处理（单位：毫秒）
        long currentTime = System.currentTimeMillis();
        fileUpload.setUploaderTime(currentTime);
        fileUpload.setCreateTime(currentTime);
        fileUpload.setUpdateTime(currentTime);

        return fileUpload;
    }

    public static String getFileSuffix(String url) {
        if (StringUtils.isBlank(url)) {
            return "";
        }

        int lastDotIndex = url.lastIndexOf(".");
        int lastSlashIndex = url.lastIndexOf("/");

        if (lastDotIndex > lastSlashIndex && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex + 1); // 返回类似 ".jpg"
        }

        return "";
    }

    @Override
    public Map<String, List<String>> getUrlsByModuleAndGuids(FileUploadEnum moduleName, List<String> guids) {
        if (moduleName == null || guids == null || guids.isEmpty()) {
            return Collections.emptyMap();
        }

        // 构建查询条件
        QueryWrapper<IntlFileUpload> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("module_name", moduleName)
                   .in("guid", guids)
                   .select("guid", "fds_url");  // 只查询需要的字段

        // 执行查询
        List<IntlFileUpload> fileUploads = intlFileUploadMapper.selectList(queryWrapper);

        // 处理结果，按guid分组，收集fds_url
        Map<String, List<String>> result = fileUploads.stream()
                .filter(file -> StringUtils.isNotBlank(file.getFdsUrl()))
                .collect(Collectors.groupingBy(
                        IntlFileUpload::getGuid,
                        Collectors.mapping(IntlFileUpload::getFdsUrl, Collectors.toList())
                ));

        return result;
    }

    /**
     * 根据模块名称更新相应数据的图片存在标识
     *
     * @param request 文件上传请求
     */
    private void updatePhotoExistFlag(FileUploadInfo request) {
        String moduleName = request.getModuleName();
        if (StringUtils.isBlank(moduleName)) {
            log.warn("updatePhotoExistFlag: moduleName is blank");
            return;
        }

        // 获取所有有图片URL的MetaData的guid列表
        List<String> guidList = request.getMetaDataList().stream()
                .filter(metaData -> !CollectionUtils.isEmpty(metaData.getFdsUrls()))
                .map(FileUploadInfo.MetaData::getGuid)
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(guidList)) {
            log.info("updatePhotoExistFlag: no valid guid found for moduleName: {}", moduleName);
            return;
        }

        try {
            if (FileUploadEnum.IMEI_UPLOAD.getCode().equals(moduleName)) {
                // IMEI数据图片字段更新
                log.info("updatePhotoExistFlag: updating IMEI photo exist flag for {} guids", guidList.size());
                int updateCount = intlSoImeiApiService.updateIsPhotoExistByDetailIds(guidList);
                log.info("updatePhotoExistFlag: updated {} IMEI records", updateCount);
            } else if (FileUploadEnum.QTY_UPLOAD.getCode().equals(moduleName)) {
                // QTY数据图片字段更新
                log.info("updatePhotoExistFlag: updating QTY photo exist flag for {} guids", guidList.size());
                int updateCount = intlSoQtyApiService.updateIsPhotoExistByDetailIds(guidList);
                log.info("updatePhotoExistFlag: updated {} QTY records", updateCount);
            } else {
                log.debug("updatePhotoExistFlag: moduleName {} does not require photo exist flag update", moduleName);
            }
        } catch (Exception e) {
            log.error("updatePhotoExistFlag: failed to update photo exist flag for moduleName: {}", moduleName, e);
            // 不抛出异常，避免影响主流程
        }
    }
}
