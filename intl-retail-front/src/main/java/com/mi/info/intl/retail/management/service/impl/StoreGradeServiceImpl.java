package com.mi.info.intl.retail.management.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerChange;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.management.entity.CommonChangeLog;
import com.mi.info.intl.retail.management.mapper.CommonChangeLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeMapper;
import com.mi.info.intl.retail.management.service.enums.ApprovalBusinessKeyEnum;
import com.mi.info.intl.retail.management.service.enums.ApprovalStatus;
import com.mi.info.intl.retail.management.service.enums.ChannelTypeEnum;
import com.mi.info.intl.retail.management.service.enums.RuleStatusEnum;
import com.mi.info.intl.retail.management.service.enums.StoreGradeEnum;
import com.mi.info.intl.retail.management.service.enums.SubmitTypeEnum;
import com.mi.info.intl.retail.management.service.enums.TieringModificationMethodEnum;
import com.mi.info.intl.retail.model.RetailerChannelGradeCount;
import com.mi.info.intl.retail.model.ChannelTypeStatistics;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.maindataapi.api.PositionProvider;
import com.xiaomi.cnzone.maindataapi.model.req.org.OpInfo;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgCategory;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgMerge;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgParamEntity;
import com.xiaomi.cnzone.maindataapi.model.req.store.EditPositionInfoRequest;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.rpc.MainDataRpc;
import com.xiaomi.cnzone.maindataapi.model.OrgDataDto;
import com.xiaomi.cnzone.maindataapi.model.OrgResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Reference;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import java.util.Objects;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;

@DubboService(group = "${retail.dubbo.group:}", interfaceClass = StoreGradeService.class)
@Slf4j
@Service
public class StoreGradeServiceImpl implements StoreGradeService {

    @Reference(group = "${maindata.dubbo.group:}", version = "1.0", check = false, interfaceClass = PositionProvider.class)
    PositionProvider positionProvider;

    @Resource
    StoreGradeMapper storeGradeMapper;

    @Resource
    private MainDataRpc mainDataRpc;

    @Resource
    private StoreGradeRuleMapper storeGradeRuleMapper;

    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Resource
    private CommonChangeLogMapper commonChangeLogMapper;

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Resource
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    /**
     * 门店变化触发
     *
     * @param storeCode
     */
    @Override
    public CommonResponse<Void> storeChangeTrigger(String storeCode) {
        //先查询retailer、kapa、grade信息
        log.info("开始处理门店变化触发，门店编码：{}", storeCode);

        if (storeCode == null) {
            throw new RuntimeException("Store code cannot be empty");
        }

        // 1. 验证门店是否存在
        OrgResponse orgResponse = mainDataRpc.selectStoreByOrgIds(Lists.newArrayList(storeCode), null);
        if (Objects.isNull(orgResponse) || CollectionUtils.isEmpty(orgResponse.getOrgList())) {
            throw new RuntimeException("Store does not exist");
        }
        OrgDataDto orgDataDto = orgResponse.getOrgList().get(0);
        // 2. 获取门店信息
        String retailerCode = orgDataDto.getOrgBuild().getRetailerCode();
        Integer capacity = orgDataDto.getOrgExtension().getCapaMonth();
        LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
        retailerQueryWrapper.eq(IntlRmsRetailer::getName, retailerCode);
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
        if (CollectionUtils.isEmpty(intlRmsRetailers)) {
            log.error("Retailer information is empty");
            throw new RuntimeException("Retailer information is empty");
        }
        Integer channelTypeKey = intlRmsRetailers.get(0).getRetailerChannelType();
        String countryId = intlRmsRetailers.get(0).getCountryId();
        LambdaQueryWrapper<IntlRmsCountryTimezone> countryTimezoneLambdaQueryWrapper = Wrappers.lambdaQuery();
        countryTimezoneLambdaQueryWrapper.eq(IntlRmsCountryTimezone::getCountryId, countryId);
        IntlRmsCountryTimezone intlRmsCountryTimezone = intlRmsCountryTimezoneMapper.selectOne(countryTimezoneLambdaQueryWrapper);
        if (intlRmsCountryTimezone == null) {
            log.error("Country timezone information is empty");
            throw new RuntimeException("Country timezone information is empty");
        }
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.fromKey(channelTypeKey);
        //计算新的grade
        StoreGradeRule effectiveRule = getEffectiveRule(retailerCode, channelTypeEnum, intlRmsCountryTimezone.getCountryCode());
        if (Objects.isNull(effectiveRule) && Objects.nonNull(orgDataDto.getOrgMerge()) &&
                !Objects.equals(orgDataDto.getOrgMerge().getStoreGradingCalcStatus(), 0)) {
            updateStoreGradingStatus(storeCode, 0);
            log.info("No effective rule found, retailer code: {}, channel type: {}", retailerCode, channelTypeEnum.getValue());
            return new CommonResponse<>(null);
        }

        // 4. 重新计算门店grade
        String newGrade = getStoreGrade(capacity, effectiveRule);
        Integer storeGrading = orgDataDto.getOrgCategory().getStoreGrading() == null ?
                null : orgDataDto.getOrgCategory().getStoreGrading().intValue();
        StoreGradeEnum storeGradeEnum = StoreGradeEnum.fromKey(storeGrading);
        String oldGrade = null;
        if (Objects.nonNull(storeGradeEnum)) {
            oldGrade = storeGradeEnum.getValue();
        }

        log.info("Store grade calculation result - store code: {}, capacity: {}, old grade: {}, new grade: {}",
                storeCode, capacity, oldGrade, newGrade);

        // 5. 如果有变化则更新
        if (Objects.isNull(oldGrade) || !Objects.equals(oldGrade, newGrade) || (Objects.nonNull(orgDataDto.getOrgMerge()) &&
                Objects.equals(orgDataDto.getOrgMerge().getStoreGradingCalcStatus(), 0))) {
            log.info("Store grade updated, store code: {}, new grade: {}", storeCode, newGrade);
            CommonChangeLog commonChangeLog = new CommonChangeLog();
            commonChangeLog.setBusinessKey(ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue());
            commonChangeLog.setBusinessId(storeCode);
            commonChangeLog.setStoreGrade(newGrade);
            commonChangeLog.setChannelType(channelTypeEnum.getKey());
            commonChangeLog.setCompleteStatus(1);
            commonChangeLog.setChangeReason("store_grade_change");
            commonChangeLog.setStoreGradeRuleId(effectiveRule.getId());
            updateStoreGrade(commonChangeLog);

            commonChangeLogMapper.insert(commonChangeLog);
        } else {
            log.info("Store grade unchanged, store code: {}, grade: {}", storeCode, newGrade);
        }
        return new CommonResponse<>(null);
    }

    /**
     * 获取生效的规则数据
     *
     * @param retailerCode 零售商编码
     * @param channelTypeEnum  渠道类型
     * @param countryCode  国家编码
     * @return 规则数据
     */
    private StoreGradeRule getEffectiveRule(String retailerCode, ChannelTypeEnum channelTypeEnum, String countryCode) {
        LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
        if (Objects.equals(ChannelTypeEnum.IR.getKey(), channelTypeEnum.getKey())) {
            queryWrapper.eq(StoreGradeRule::getCountryCode, countryCode);
        } else {
            queryWrapper.eq(StoreGradeRule::getRetailerCode, retailerCode);
        }
        queryWrapper.eq(StoreGradeRule::getMethod, TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey())
                .eq(StoreGradeRule::getRulesStatus, RuleStatusEnum.IN_EFFECT.getCode())
                .eq(StoreGradeRule::getIsDeleted, 0)
                .orderByDesc(StoreGradeRule::getApplicationTime)
                .last("LIMIT 1");

        return storeGradeRuleMapper.selectOne(queryWrapper);
    }

    /**
     * 更新门店等级
     *
     * @param commonChangeLog
     */
    private void updateStoreGrade(CommonChangeLog commonChangeLog) {
        try {
            // TODO: 这里需要根据实际的业务逻辑来实现门店等级的更新
            OrgParamEntity orgParamEntity = new OrgParamEntity();
            orgParamEntity.setOpType(2);
            orgParamEntity.setSource("intl-retail");
            OpInfo opInfo = new OpInfo();
            //Optional.ofNullable(storeModel.getUpdaterMid()).ifPresent(item -> opInfo.setMid(item + ""));
            opInfo.setName(commonChangeLog.getChangeReason());

            opInfo.setDesc(commonChangeLog.getChangeReason());
            orgParamEntity.setOpInfo(opInfo);

            OrgCategory orgCategory = new OrgCategory();
            orgCategory.setOrgId(commonChangeLog.getBusinessId());
            StoreGradeEnum storeGradeEnum = StoreGradeEnum.fromValue(commonChangeLog.getStoreGrade());
            orgCategory.setStoreGrading(storeGradeEnum.getKey().byteValue());
            orgParamEntity.setOrgCategory(orgCategory);
            OrgMerge orgMerge = new OrgMerge();
            orgMerge.setStoreGradingCalcStatus(1);
            orgMerge.setOrgId(commonChangeLog.getBusinessId());
            orgParamEntity.setOrgMerge(orgMerge);
            mainDataRpc.pushEditStoreBeta(orgParamEntity);
        } catch (Exception e) {
            log.error("Failed to update store grade, store code: {}, new grade: {}", commonChangeLog.getBusinessId(), commonChangeLog.getStoreGrade(), e);
            throw new RuntimeException("Failed to update store grade: " + e.getMessage());
        }
    }

    private void updateStoreGradingStatus(String storeCode, Integer storeGradingCalcStatus) {
        try {
            // TODO: 这里需要根据实际的业务逻辑来实现门店等级的更新
            OrgParamEntity orgParamEntity = new OrgParamEntity();
            orgParamEntity.setOpType(2);
            orgParamEntity.setSource("intl-retail");
            OpInfo opInfo = new OpInfo();
            //Optional.ofNullable(storeModel.getUpdaterMid()).ifPresent(item -> opInfo.setMid(item + ""));
            opInfo.setName("store_grade_change");

            opInfo.setDesc("store_grade_change");
            orgParamEntity.setOpInfo(opInfo);

            OrgMerge orgMerge = new OrgMerge();
            orgMerge.setStoreGradingCalcStatus(storeGradingCalcStatus);
            orgMerge.setOrgId(storeCode);
            orgParamEntity.setOrgMerge(orgMerge);
            mainDataRpc.pushEditStoreBeta(orgParamEntity);
        } catch (Exception e) {
            log.error("Failed to update store grade status, store code: {}", storeCode, e);
            throw new RuntimeException("Failed to update store grade status: " + e.getMessage());
        }
    }

    private String getStoreGrade(Integer capa, StoreGradeRule storeGradeRule) {
        if (Objects.isNull(storeGradeRule)) {
            return null;
        }
        if (capa >= storeGradeRule.getSMinCount()) {
            return "S";
        }
        if (capa >= storeGradeRule.getAMinCount()) {
            return "A";
        }
        if (capa >= storeGradeRule.getBMinCount()) {
            return "B";
        }
        if (capa >= storeGradeRule.getCMinCount()) {
            return "C";
        }
        if (capa >= storeGradeRule.getDMinCount()) {
            return "D";
        }
        return null;
    }

    /**
     * 商变化触发
     *
     * @param retailerChange
     */
    @Override
    public CommonResponse<Void> retailerChangeTrigger(RetailerChange retailerChange) {
        log.info("Retailer change trigger processing, retailerChange: {}", JacksonUtil.toStr(retailerChange));
        if (retailerChange == null || retailerChange.getRetailerCode() == null) {
            throw new BusinessException("Retailer code cannot be empty");
        }
        String retailerCode = retailerChange.getRetailerCode();
        LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
        retailerQueryWrapper.eq(IntlRmsRetailer::getName, retailerCode);
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
        if (CollectionUtils.isEmpty(intlRmsRetailers)) {
            log.error("Retailer information is empty");
            throw new RuntimeException("Retailer information is empty");
        }
        Integer channelTypeKey = intlRmsRetailers.get(0).getRetailerChannelType();
        String countryId = intlRmsRetailers.get(0).getCountryId();
        LambdaQueryWrapper<IntlRmsCountryTimezone> countryTimezoneLambdaQueryWrapper = Wrappers.lambdaQuery();
        countryTimezoneLambdaQueryWrapper.eq(IntlRmsCountryTimezone::getCountryId, countryId);
        IntlRmsCountryTimezone intlRmsCountryTimezone = intlRmsCountryTimezoneMapper.selectOne(countryTimezoneLambdaQueryWrapper);
        if (intlRmsCountryTimezone == null) {
            log.error("Country timezone information is empty");
            throw new RuntimeException("Country timezone information is empty");
        }
        //改成从入参获取
        ChannelTypeEnum channelTypeEnum = ChannelTypeEnum.fromValue(retailerChange.getTargetChannelType());
        // 计算规则
        StoreGradeRule effectiveRule = getEffectiveRule(retailerCode, channelTypeEnum, intlRmsCountryTimezone.getCountryCode());
        if (effectiveRule == null) {
            log.info("No effective rule found, retailer code: {}, channel type: {}", retailerCode, channelTypeEnum.getValue());
        }
        // 使用 CompletableFuture 异步处理
        CompletableFuture.runAsync(() -> {
            try {
                int pageNum = 1;
                int pageSize = 100;
                boolean hasMore = true;
                while (hasMore) {
                    // 分页查询门店
                    Page<IntlRmsStore> page = new Page<>(pageNum, pageSize);
                    LambdaQueryWrapper<IntlRmsStore> storeQueryWrapper = Wrappers.lambdaQuery();
                    storeQueryWrapper.eq(IntlRmsStore::getRetailerIdName, retailerChange.getRetailerCode())
                            .ne(IntlRmsStore::getOperationStatusName, "Closed")
                            .select(IntlRmsStore::getCrssCode);
                    Page<IntlRmsStore> storePage = intlRmsStoreMapper.selectPage(page, storeQueryWrapper);
                    if (CollectionUtils.isEmpty(storePage.getRecords())) {
                        break;
                    }
                    List<String> storeCodes = storePage.getRecords().stream()
                            .map(IntlRmsStore::getCrssCode)
                            .collect(Collectors.toList());
                    calculationStoreGrade(effectiveRule, storeCodes, channelTypeEnum.getKey());
                    hasMore = storeCodes.size() == pageSize;
                    pageNum++;
                }
            } catch (Exception e) {
                log.error("Failed to calculate store grade, retailer code: {}", retailerCode, e);
            }
        });

        return new CommonResponse<>(null);
    }

    /**
     * 规则变化触发
     *
     * @param storeGradeRuleReq
     */
    @Override
    public CommonResponse<Void> ruleChangeTrigger(StoreGradeRuleReq storeGradeRuleReq) {
        log.info("Rule change trigger processing, ruleId: {}", storeGradeRuleReq.getId());
        StoreGradeRule storeGradeRule = storeGradeRuleMapper.selectById(storeGradeRuleReq.getId());
        if (storeGradeRule == null) {
            log.error("Rule does not exist, ruleId: {}", storeGradeRuleReq.getId());
            throw new RuntimeException("Rule does not exist");
        }
        CompletableFuture.runAsync(() -> {
            try {
                int pageNum = 1;
                int pageSize = 100;
                boolean hasMore = true;
                while (hasMore) {
                    // 分页查询门店
                    List<IntlRmsStore> intlRmsStores = new ArrayList<>();
                    if (Objects.equals(ChannelTypeEnum.IR.getKey(), storeGradeRule.getChannelType())) {
                        intlRmsStores = intlRmsStoreMapper.pageSelectByCountryCodeAndChannelType(storeGradeRule.getCountryCode(),
                                storeGradeRule.getChannelType(), (pageNum - 1) * pageSize, pageSize);
                    } else {
                        Page<IntlRmsStore> page = new Page<>(pageNum, pageSize);
                        LambdaQueryWrapper<IntlRmsStore> storeQueryWrapper = Wrappers.lambdaQuery();
                        storeQueryWrapper.eq(IntlRmsStore::getRetailerIdName, storeGradeRule.getRetailerCode())
                                .ne(IntlRmsStore::getOperationStatusName, "Closed")
                                .isNotNull(IntlRmsStore::getCrssCode)
                                .ne(IntlRmsStore::getCrssCode, "")
                                .select(IntlRmsStore::getCrssCode);
                        Page<IntlRmsStore> storePage = intlRmsStoreMapper.selectPage(page, storeQueryWrapper);
                        intlRmsStores = storePage.getRecords();
                    }
                    if (CollectionUtils.isEmpty(intlRmsStores)) {
                        break;
                    }
                    List<String> storeCodes = intlRmsStores.stream()
                            .map(IntlRmsStore::getCrssCode)
                            .filter(Objects::nonNull) // 过滤掉null值
                            .filter(code -> !code.trim().isEmpty())
                            .collect(Collectors.toList());
                    calculationStoreGrade(storeGradeRule, storeCodes, null);
                    hasMore = intlRmsStores.size() == pageSize;
                    pageNum++;
                }
            } catch (Exception e) {
                log.error("Failed to process rule change asynchronously, ruleId: {}", storeGradeRuleReq.getId(), e);
            }
        });


        return new CommonResponse<>(null);
    }

    private void calculationStoreGrade(StoreGradeRule storeGradeRule, List<String> storeCodes, Integer channelType) {
        if (CollectionUtils.isEmpty(storeCodes)) {
            return;
        }
        OrgResponse orgResponse = mainDataRpc.pageSelectStoreByOrgIds(storeCodes, 1, storeCodes.size());
        if (orgResponse == null || orgResponse.getOrgList() == null || orgResponse.getOrgList().isEmpty()) {
            log.warn("No store information found, store codes: {}", storeCodes);
            return;
        }
        // 按照 orgId 对 orgList 进行分组
        Map<String, List<OrgDataDto>> orgListGroupedByOrgId = orgResponse.getOrgList().stream()
                .collect(Collectors.groupingBy(orgDataDto -> orgDataDto.getOrgBase().getOrgId()));

        for (String storeCode : storeCodes) {
            try {
                // 查询门店信息
                if (CollectionUtils.isEmpty(orgListGroupedByOrgId.get(storeCode))) {
                    log.warn("Store does not exist, storeCode: {}", storeCode);
                    continue;
                }
                OrgDataDto orgDataDto = orgListGroupedByOrgId.get(storeCode).get(0);
                if (Objects.isNull(storeGradeRule) && Objects.nonNull(orgDataDto.getOrgMerge()) &&
                        !Objects.equals(orgDataDto.getOrgMerge().getStoreGradingCalcStatus(), 0)) {
                    updateStoreGradingStatus(storeCode, 0);
                    continue;
                }
                Integer capacity = orgDataDto.getOrgExtension().getCapaMonth();
                Integer storeGrading = orgDataDto.getOrgCategory().getStoreGrading() == null ?
                        null : orgDataDto.getOrgCategory().getStoreGrading().intValue();
                StoreGradeEnum storeGradeEnum = StoreGradeEnum.fromKey(storeGrading);
                String oldGrade = null;
                if (Objects.nonNull(storeGradeEnum)) {
                    oldGrade = storeGradeEnum.getValue();
                }
                String newGrade = getStoreGrade(capacity, storeGradeRule);
                if (Objects.isNull(oldGrade) || !Objects.equals(oldGrade, newGrade) || (Objects.nonNull(orgDataDto.getOrgMerge()) &&
                        !Objects.equals(orgDataDto.getOrgMerge().getStoreGradingCalcStatus(), 1))) {
                    log.info("Store grade updated, store code: {}, new grade: {}", storeCode, newGrade);
                    CommonChangeLog commonChangeLog = new CommonChangeLog();
                    commonChangeLog.setBusinessKey(ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue());
                    commonChangeLog.setBusinessId(storeCode);
                    commonChangeLog.setStoreGrade(newGrade);
                    if (channelType != null) {
                        commonChangeLog.setChannelType(channelType);
                    } else {
                        commonChangeLog.setChannelType(storeGradeRule.getChannelType());
                    }
                    commonChangeLog.setCompleteStatus(1);
                    commonChangeLog.setChangeReason("store_grade_change");
                    commonChangeLog.setStoreGradeRuleId(storeGradeRule.getId());
                    updateStoreGrade(commonChangeLog);
                    commonChangeLogMapper.insert(commonChangeLog);
                } else {
                    log.info("Store grade unchanged, store code: {}, grade: {}", storeCode, newGrade);
                }
            } catch (Exception e) {
                log.error("Failed to process store change for storeCode: {}", storeCode, e);
            }
        }
    }


    /**
     * 获取每个渠道类型的统计信息
     *
     * @return 渠道类型统计列表
     */
    @Override
    public CommonResponse<List<ChannelTypeStatistics>> getChannelTypeStatistics() {
        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        List<RetailerChannelGradeCount> channelGradeCounts = storeGradeMapper.selectRetailerChannelGradeCount(countryCode);

        // 按渠道类型分组，包括null值
        Map<String, List<RetailerChannelGradeCount>> channelTypeMap = channelGradeCounts.stream()
                .collect(Collectors.groupingBy(
                        count -> count.getRetailerChannelType() != null ? count.getRetailerChannelType() : "EMPTY",
                        Collectors.toList()
                ));

        List<ChannelTypeStatistics> result = new ArrayList<>();

        for (Map.Entry<String, List<RetailerChannelGradeCount>> entry : channelTypeMap.entrySet()) {
            String channelType = entry.getKey();
            List<RetailerChannelGradeCount> counts = entry.getValue();

            // 计算完成和未完成数量，包括null值
            Integer completeCount = counts.stream()
                    .filter(count -> "1".equals(count.getGradeCalFlag()))
                    .mapToInt(count -> count.getCount().intValue())
                    .sum();

            Integer notCompleteCount = counts.stream()
                    .filter(count -> "0".equals(count.getGradeCalFlag()) || count.getGradeCalFlag() == null)
                    .mapToInt(count -> count.getCount().intValue())
                    .sum();

            Integer totalCount = completeCount + notCompleteCount;

            // 计算百分比
            Double completePercentage = totalCount > 0 ? (double) completeCount / totalCount : 0.0;
            Double notCompletePercentage = totalCount > 0 ? (double) notCompleteCount / totalCount : 0.0;

            ChannelTypeStatistics statistics = new ChannelTypeStatistics(
                    channelType, completeCount, notCompleteCount, totalCount,
                    completePercentage, notCompletePercentage
            );

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCompleteCount = result.stream()
                .mapToInt(ChannelTypeStatistics::getCompleteCount)
                .sum();
        Integer totalNotCompleteCount = result.stream()
                .mapToInt(ChannelTypeStatistics::getNotCompleteCount)
                .sum();
        Integer grandTotal = totalCompleteCount + totalNotCompleteCount;

        Double totalCompletePercentage = grandTotal > 0 ? (double) totalCompleteCount / grandTotal : 0.0;
        Double totalNotCompletePercentage = grandTotal > 0 ? (double) totalNotCompleteCount / grandTotal : 0.0;

        ChannelTypeStatistics totalStatistics = new ChannelTypeStatistics(
                "ALL", totalCompleteCount, totalNotCompleteCount, grandTotal,
                totalCompletePercentage, totalNotCompletePercentage
        );

        result.add(0, totalStatistics); // 将总计放在第一位

        return new CommonResponse<>(result);
    }

    /**
     * 获取门店等级统计信息
     *
     * @param ruleQueryReq 门店等级规则ID
     * @return 门店等级统计列表
     */
    @Override
    public CommonResponse<List<StoreGradeCompleteStatistics>> getStoreGradeCompleteStatistics(RuleQueryReq ruleQueryReq) {
        // 通过 ruleId 查询门店等级规则
        StoreGradeRule storeGradeRule = storeGradeRuleMapper.selectById(ruleQueryReq.getRuleId());
        if (storeGradeRule == null) {
            log.warn("门店等级规则不存在，ruleId: {}", ruleQueryReq.getRuleId());
            return new CommonResponse<>(new ArrayList<>());
        }

        // 根据计算方法决定走不同的逻辑
        Integer method = storeGradeRule.getMethod();
        if (TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey().equals(method)) {
            // 当前逻辑：基于现有门店等级统计
            return new CommonResponse<>(getCurrentGradeStatistics(storeGradeRule));
        } else if (TieringModificationMethodEnum.MANUAL_UPLOAD.getKey().equals(method)) {
            // 基于门店等级关系表统计
            return new CommonResponse<>(getRelationGradeStatistics(storeGradeRule));
        } else {
            // TODO: 其他计算方法的逻辑
            log.info("暂未实现的计算方法: {}, ruleId: {}", method, ruleQueryReq.getRuleId());
            return new CommonResponse<>(new ArrayList<>());
        }
    }

    /**
     * 获取所有枚举信息汇总
     *
     * @return 包含所有枚举信息的Map
     */
    @Override
    public CommonResponse<Map<String, Object>> getAllEnums() {
        Map<String, Object> allEnums = new HashMap<>();

        // 渠道类型枚举
        List<Map<String, Object>> channelTypeEnums = new ArrayList<>();
        for (ChannelTypeEnum channelType : ChannelTypeEnum.values()) {
            Map<String, Object> enumMap = new HashMap<>();
            enumMap.put("key", channelType.getKey());
            enumMap.put("value", channelType.getValue());
            channelTypeEnums.add(enumMap);
        }
        allEnums.put("channelTypes", channelTypeEnums);

        // 门店等级枚举
        List<Map<String, Object>> storeGradeEnums = new ArrayList<>();
        for (StoreGradeEnum storeGrade : StoreGradeEnum.values()) {
            Map<String, Object> enumMap = new HashMap<>();
            enumMap.put("key", storeGrade.getKey());
            enumMap.put("value", storeGrade.getValue());
            enumMap.put("name", storeGrade.getName());
            enumMap.put("description", storeGrade.getDescription());
            enumMap.put("types", storeGrade.getTypes());
            storeGradeEnums.add(enumMap);
        }
        allEnums.put("storeGrades", storeGradeEnums);

        // 规则状态枚举
        List<Map<String, Object>> ruleStatusEnums = new ArrayList<>();
        for (RuleStatusEnum ruleStatus : RuleStatusEnum.values()) {
            Map<String, Object> enumMap = new HashMap<>();
            enumMap.put("code", ruleStatus.getCode());
            enumMap.put("value", ruleStatus.getValue());
            ruleStatusEnums.add(enumMap);
        }
        allEnums.put("ruleStatuses", ruleStatusEnums);

        // 提交类型枚举
        List<Map<String, Object>> submitTypeEnums = new ArrayList<>();
        for (SubmitTypeEnum submitType : SubmitTypeEnum.values()) {
            Map<String, Object> enumMap = new HashMap<>();
            enumMap.put("type", submitType.getType());
            enumMap.put("desc", submitType.getDesc());
            submitTypeEnums.add(enumMap);
        }
        allEnums.put("submitTypes", submitTypeEnums);

        // 审批状态枚举
        List<Map<String, Object>> approvalStatusEnums = new ArrayList<>();
        for (ApprovalStatus approvalStatus : ApprovalStatus.values()) {
            Map<String, Object> enumMap = new HashMap<>();
            enumMap.put("code", approvalStatus.getCode());
            enumMap.put("value", approvalStatus.getValue());
            approvalStatusEnums.add(enumMap);
        }
        allEnums.put("approvalStatuses", approvalStatusEnums);

        // 分级修改方法枚举
        List<Map<String, Object>> tieringModificationMethodEnums = new ArrayList<>();
        for (TieringModificationMethodEnum method : TieringModificationMethodEnum.values()) {
            Map<String, Object> enumMap = new HashMap<>();
            enumMap.put("key", method.getKey());
            enumMap.put("value", method.getValue());
            enumMap.put("desc", method.getDesc());
            tieringModificationMethodEnums.add(enumMap);
        }
        allEnums.put("tieringModificationMethods", tieringModificationMethodEnums);

        return new CommonResponse<>(allEnums);
    }

    /**
     * 基于现有门店等级统计的逻辑
     *
     * @param storeGradeRule 门店等级规则
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> getCurrentGradeStatistics(StoreGradeRule storeGradeRule) {
        // 从规则中获取渠道类型和渠道代码
        String retailerChannelType = String.valueOf(storeGradeRule.getChannelType());
        String retailerCode = storeGradeRule.getRetailerCode();

        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        List<StoreGradeCompleteCount> gradeCompleteCounts = storeGradeMapper.selectStoreGradeCompleteCount(countryCode,
                retailerChannelType, retailerCode);

        List<StoreGradeCompleteStatistics> result = new ArrayList<>();

        // 先计算总数
        Integer totalStoreCount = gradeCompleteCounts.stream()
                .mapToInt(count -> count.getCount().intValue())
                .sum();

        for (StoreGradeCompleteCount count : gradeCompleteCounts) {
            String grade = count.getGrade() != null ? count.getGrade() : "EMPTY";
            Integer storeCount = count.getCount().intValue();

            // 计算占总数的百分比
            Double percentage = totalStoreCount > 0 ? (double) storeCount / totalStoreCount : 0.0;

            StoreGradeCompleteStatistics statistics = new StoreGradeCompleteStatistics(
                    grade, storeCount, percentage, 0L
            );

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCount = result.stream()
                .mapToInt(StoreGradeCompleteStatistics::getCount)
                .sum();

        Double totalPercentage = 1.0; // 总计占总数的100%

        StoreGradeCompleteStatistics totalStatistics = new StoreGradeCompleteStatistics(
                "ALL", totalCount, totalPercentage, 0L
        );

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    /**
     * 基于门店等级关系表统计的逻辑
     *
     * @param storeGradeRule 门店等级规则
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> getRelationGradeStatistics(StoreGradeRule storeGradeRule) {
        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        Integer ruleId = storeGradeRule.getId();

        List<StoreGradeCompleteCount> gradeCompleteCounts = storeGradeRelationMapper.selectStoreGradeCountByRuleId(ruleId, countryCode);

        List<StoreGradeCompleteStatistics> result = new ArrayList<>();

        // 先计算总数
        Integer totalStoreCount = gradeCompleteCounts.stream()
                .mapToInt(count -> count.getCount().intValue())
                .sum();

        for (StoreGradeCompleteCount count : gradeCompleteCounts) {
            String grade = count.getGrade() != null ? count.getGrade() : "EMPTY";
            Integer storeCount = count.getCount().intValue();

            // 计算占总数的百分比
            Double percentage = totalStoreCount > 0 ? (double) storeCount / totalStoreCount : 0.0;

            StoreGradeCompleteStatistics statistics = new StoreGradeCompleteStatistics(
                    grade, storeCount, percentage, 0L
            );

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCount = result.stream()
                .mapToInt(StoreGradeCompleteStatistics::getCount)
                .sum();

        Double totalPercentage = 1.0; // 总计占总数的100%

        StoreGradeCompleteStatistics totalStatistics = new StoreGradeCompleteStatistics(
                "ALL", totalCount, totalPercentage, 0L
        );

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    @Override
    public CommonResponse<Void> editPositionInfo(EditPositionInfoRequest editPositionInfoRequest) {
        // 注意：这里positionProvider.editPositionInfo返回的是Result<String>，但我们不需要处理返回值
        positionProvider.editPositionInfo(editPositionInfoRequest);
        return new CommonResponse<>(null);
    }
}