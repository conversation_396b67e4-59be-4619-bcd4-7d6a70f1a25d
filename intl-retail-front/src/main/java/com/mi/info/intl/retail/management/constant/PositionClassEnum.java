package com.mi.info.intl.retail.management.constant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

public enum PositionClassEnum {
    FRONT_W_PC(Arrays.asList("<PERSON>YY<PERSON>", "YYN<PERSON>"), "Front w PC", 6),
    FRONT_NO_PC_W_SR(Arrays.asList("YNYN"), "Front No PC w SR", 5),
    FRONT_NO_FF(Arrays.asList("YNNN"), "Front No FF", 4),
    POS_W_PC(Arrays.asList("NYYY", "NYNY"), "POS w PC", 3),
    POS_NO_PC_W_SR(Arrays.asList("NNYY"), "POS No PC w SR", 2),
    POS_NO_FF(Arrays.asList("NNNY"), "POS No FF", 1),
    EMPTY(Arrays.asList("NNNN"), "", 0);

    private Set<String> keys;
    private String key;  // 保留原来的主 key
    private String displayValue;
    private Integer level;

    PositionClassEnum(List<String> keys, String displayValue, Integer level) {
        this.keys = new HashSet<>(keys);
        this.key = keys.get(0);  // 第一个作为主 key
        this.displayValue = displayValue;
        this.level = level;
    }

    public Integer getLevel() {
        return level;
    }

    // 保持向后兼容
    public String getKey() {
        return key;
    }

    public Set<String> getKeys() {
        return keys;
    }

    public String getDisplayValue() {
        return displayValue;
    }

    // 根据 key 查找对应的枚举值
    public static PositionClassEnum fromKey(String key) {
        for (PositionClassEnum enumValue : values()) {
            if (enumValue.keys.contains(key)) {
                return enumValue;
            }
        }
        return null;
    }
}
