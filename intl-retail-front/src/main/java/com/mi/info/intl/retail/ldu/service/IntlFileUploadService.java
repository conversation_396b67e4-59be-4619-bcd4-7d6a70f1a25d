package com.mi.info.intl.retail.ldu.service;

import java.util.List;
import java.util.Map;

import com.fasterxml.jackson.databind.JsonNode;
import com.mi.info.intl.retail.intlretail.service.api.upload.FileUploadInfo;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.model.CommonApiResponse;

public interface IntlFileUploadService {

    CommonApiResponse<Object> updateByGuid(Object requestBody, JsonNode rootNode);

    CommonApiResponse<Object> save(FileUploadInfo request);

    CommonApiResponse<Object> saveSimple(List<FileUploadInfo.MetaData> metaDataList, FileUploadEnum moduleName, String uploaderName);

    /**
     * 通过guids和moduleName获取url集合
     * @param moduleName
     * @param guids
     * @return
     */
    Map<String, List<String>> getUrlsByModuleAndGuids(FileUploadEnum moduleName, List<String> guids);

}
