package com.mi.info.intl.retail.org.domain;

import javax.annotation.Resource;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoImeiMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoQtyMapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.xiaomi.nr.global.dev.neptune.T;
import org.springframework.stereotype.Service;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.api.so.rule.blacklist.SoBlacklistApiService;
import com.mi.info.intl.retail.api.so.rule.model.SoRuleRetailerModel;
import com.mi.info.intl.retail.api.so.rule.service.IntlSoRuleRetailerApiService;
import com.mi.info.intl.retail.cooperation.task.infra.entity.IntlRmsSignRule;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.IntlRmsSignRuleMapper;
import com.mi.info.intl.retail.cooperation.task.infra.mapper.read.IntlRmsSignRuleReadMapper;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbContentRequest;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsDbRequest;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlRmsProductMapper;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsCountryTimezoneReadMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsPositionReadMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsStoreReadMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsCity;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsProvince;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsRrp;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlRmsSecondarychannel;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsCityMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsProvinceMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsRrpMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsSecondarychannelMapper;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsPersonnelPositionMapper;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper;
import com.mi.info.intl.retail.user.infra.mapper.read.IntlRmsPersonnelPositionReadMapper;
import com.mi.info.intl.retail.user.infra.mapper.read.IntlRmsUserReadMapper;
import com.mi.info.intl.retail.utils.JsonUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
public class RmsSyncDbManager {

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;
    @Resource
    private IntlRmsStoreReadMapper intlRmsStoreReadMapper;
    @Resource
    private IntlRmsPositionMapper intlRmsPositionMapper;
    @Resource
    private IntlRmsPositionReadMapper intlRmsPositionReadMapper;
    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;
    @Resource
    private IntlRmsCountryTimezoneReadMapper intlRmsCountryTimezoneReadMapper;
    @Resource
    private IntlRmsUserMapper intlRmsUserMapper;
    @Resource
    private IntlRmsUserReadMapper intlRmsUserReadMapper;
    @Resource
    private IntlRmsPersonnelPositionMapper intlRmsPersonnelPositionMapper;
    @Resource
    private IntlRmsPersonnelPositionReadMapper intlRmsPersonnelPositionReadMapper;
    @Resource
    private IntlRmsSignRuleMapper intlRmsSignRuleMapper;
    @Resource
    private IntlRmsSignRuleReadMapper intlRmsSignRuleReadMapper;
    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;
    @Resource
    private SoBlacklistApiService soBlacklistApiService;
    @Resource
    private IntlRmsRrpMapper intlRmsRrpMapper;
    @Resource
    private IntlRmsProvinceMapper intlRmsProvinceMapper;
    @Resource
    private IntlRmsCityMapper intlRmsCityMapper;
    @Resource
    private IntlRmsSecondarychannelMapper intlRmsSecondarychannelMapper;
    @Resource
    private IntlRmsProductMapper intlRmsProductMapper;
    @Resource
    private IntlSoRuleRetailerApiService intlSoRuleRetailerApiService;
    @Resource
    private IntlSoImeiMapper intlSoImeiMapper;
    @Resource
    private IntlSoQtyMapper intlSoQtyMapper;
    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;

    public void editDb(RmsDbRequest rmsDBRequest) {
        rmsDBRequest.getRmsDBContentList().forEach(rmsDBContentRequest -> {
            try {
                switch (rmsDBContentRequest.getTable()) {
                    case "intl_rms_store":
                        this.addStore(rmsDBContentRequest);
                        break;
                    case "intl_rms_user":
                        this.addUser(rmsDBContentRequest);
                        break;
                    case "intl_rms_position":
                        this.addPosition(rmsDBContentRequest);
                        break;
                    case "intl_rms_country_timezone":
                        this.addCountryTimezone(rmsDBContentRequest);
                        break;
                    case "intl_rms_personnel_position":
                        this.addPersonnelPosition(rmsDBContentRequest);
                        break;
                    case "intl_rms_sign_rule":
                        this.addSignRule(rmsDBContentRequest);
                        break;
                    case "intl_rms_retailer":
                        this.addRetailer(rmsDBContentRequest);
                        break;
                    case "intl_so_sn_blacklist":
                        this.addSoSnBlacklist(rmsDBContentRequest);
                        break;
                    case "intl_rms_rrp":
                        this.addRrp(rmsDBContentRequest);
                        break;
                    case "intl_rms_province":
                        this.addProvince(rmsDBContentRequest);
                        break;
                    case "intl_rms_city":
                        this.addCity(rmsDBContentRequest);
                        break;
                    case "intl_rms_secondarychannel":
                        this.addSecondaryChannel(rmsDBContentRequest);
                        break;
                    case "intl_rms_product":
                        this.updateProduct(rmsDBContentRequest);
                        break;
                    default:
                        throw new RuntimeException(
                                "SyncRmsDb:table not found : table" + rmsDBContentRequest.getTable());
                }
            } catch (Exception e) {

                log.error("SyncRmsDbError:{}, rmsDBContentRequest:{}", e, rmsDBContentRequest);
            }

        });
    }

    private void addSoSnBlacklist(RmsDbContentRequest rmsDBContentRequest) {
        log.info("SoSnBlacklist start :{}", rmsDBContentRequest);
        soBlacklistApiService.syncSoSnBlacklist(rmsDBContentRequest.getContent());
    }

    private void addRetailer(RmsDbContentRequest rmsDBContentRequest) {
        log.info("Retailer:{}", rmsDBContentRequest);
        IntlRmsRetailer intlRmsRetailer =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsRetailer.class);
        if (intlRmsRetailer != null && intlRmsRetailer.getRetailerId() != null) {
            LambdaQueryWrapper<IntlRmsRetailer> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsRetailer::getRetailerId, intlRmsRetailer.getRetailerId());
            IntlRmsRetailer haveStore = intlRmsRetailerMapper.selectOne(lambdaQuery);
            if (haveStore != null) {
                intlRmsRetailer.setId(haveStore.getId());
                intlRmsRetailer.setUpdatedAt(System.currentTimeMillis());
                intlRmsRetailer.setIsNew(0);
                intlRmsRetailerMapper.updateById(intlRmsRetailer);
                log.info("updateRetailer_update:{}", intlRmsRetailer);
            } else {
                intlRmsRetailer.setCreatedAt(System.currentTimeMillis());
                intlRmsRetailer.setUpdatedAt(System.currentTimeMillis());
                intlRmsRetailer.setIsNew(1);
                intlRmsRetailerMapper.insert(intlRmsRetailer);
                log.info("addRetailer_insert:{}", intlRmsRetailer);
            }
            saveOrUpdateSoRuleRetailer(intlRmsRetailer);
        }
    }

    /**
     * 保存或更新，so规则零售商
     *
     * @param intlRmsRetailer INTL RMS零售商
     */
    private void saveOrUpdateSoRuleRetailer(IntlRmsRetailer intlRmsRetailer) {
        SoRuleRetailerModel retailerModel = new SoRuleRetailerModel();
        retailerModel.setRetailerCode(intlRmsRetailer.getName());
        retailerModel.setRetailerName(intlRmsRetailer.getRetailerName());
        retailerModel.setChannelType(intlRmsRetailer.getRetailerChannelTypeName());
        retailerModel.setCountryName(intlRmsRetailer.getCountryName());
        retailerModel.setCountryCode(intlRmsRetailer.getCountryShortcode());
        intlSoRuleRetailerApiService.saveOrUpdateByRetailerCode(retailerModel);
    }

    private void addStore(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsStore intlRmsStore =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsStore.class);
        if (intlRmsStore != null && intlRmsStore.getStoreId() != null) {
            LambdaQueryWrapper<IntlRmsStore> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsStore::getStoreId, intlRmsStore.getStoreId());
            IntlRmsStore haveStore = intlRmsStoreReadMapper.selectOne(lambdaQuery);
            if (haveStore != null) {
                intlRmsStore.setId(haveStore.getId());
                intlRmsStore.setUpdatedAt(System.currentTimeMillis());
                intlRmsStoreMapper.updateById(intlRmsStore);
                log.info("addStore_update:{}", intlRmsStore);
                //更新时，Store Name(name)，Store City（city_code/city_id_name），StoreProvince（province_code\province_label），
                // 这些字段发生更新，触发ES更新
                if (!intlRmsStore.getName().equalsIgnoreCase(haveStore.getName()) ||
                        !intlRmsStore.getCityCode().equalsIgnoreCase(haveStore.getCityCode()) ||
                        !intlRmsStore.getCityIdName().equalsIgnoreCase(haveStore.getCityIdName()) ||
                        !intlRmsStore.getProvinceCode().equalsIgnoreCase(haveStore.getProvinceCode()) ||
                        !intlRmsStore.getProvinceLabel().equalsIgnoreCase(haveStore.getProvinceLabel())) {
                    //更新IMEI数据ES信息
                    processDataSync(haveStore.getCode(), intlSoImeiMapper,
                            IntlSoImei::getStoreRmsCode, IntlSoImei::getId, DataSyncDataTypeEnum.IMEI);
                    //更新QTY数据ES信息
                    processDataSync(haveStore.getCode(), intlSoQtyMapper,
                            IntlSoQty::getStoreRmsCode, IntlSoQty::getId, DataSyncDataTypeEnum.QTY);
                }
            } else {
                intlRmsStore.setCreatedAt(System.currentTimeMillis());
                intlRmsStore.setUpdatedAt(System.currentTimeMillis());
                intlRmsStoreMapper.insert(intlRmsStore);
                log.info("addStore_insert:{}", intlRmsStore);
            }
        }
    }

    private <T> void processDataSync(String code, BaseMapper<T> mapper,
                                     SFunction<T, String> codeField,
                                     SFunction<T, Long> idField,
                                     DataSyncDataTypeEnum dataType) {
        try {
            LambdaQueryWrapper<T> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(codeField, code);
            List<Long> idList = mapper.selectList(lambdaQuery).stream().map(idField).collect(Collectors.toList());
            if (!idList.isEmpty()) {
                syncSoToEsProducer.sendSyncEsMsg(dataType, idList, true);
            } else {
                // 处理空集合情况，记录详细日志
                log.warn("获取{}类型数据ID列表为空或为null，dataType: {}, idList: {}", dataType, dataType, idList);
            }
        } catch (Exception e) {
            log.error("处理{}类型数据更新时发生异常", dataType, e);
            // 根据业务需求决定是否重新抛出异常
            throw new RuntimeException("数据更新失败: " + dataType, e);
        }
    }

    private void addUser(RmsDbContentRequest rmsDBContentRequest) {

        log.info("addUser_MQ:{}", rmsDBContentRequest);
        IntlRmsUser intlRmsUser = JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                IntlRmsUser.class);
        log.info("addUser_parsed result:{}", intlRmsUser);
        if (intlRmsUser != null && intlRmsUser.getRmsUserid() != null) {
            LambdaQueryWrapper<IntlRmsUser> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsUser::getRmsUserid, intlRmsUser.getRmsUserid());
            IntlRmsUser haveUser = intlRmsUserReadMapper.selectOne(lambdaQuery);
            if (haveUser != null) {
                intlRmsUser.setId(haveUser.getId());
                intlRmsUser.setUpdatedAt(System.currentTimeMillis());
                intlRmsUserMapper.updateById(intlRmsUser);
            } else {
                intlRmsUser.setCreatedAt(System.currentTimeMillis());
                intlRmsUser.setUpdatedAt(System.currentTimeMillis());
                intlRmsUserMapper.insert(intlRmsUser);
            }
            log.info("addUser_result:{}", "success");
        }
    }

    private void addPosition(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsPosition intlRmsPosition =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                        IntlRmsPosition.class);
        if (intlRmsPosition != null && intlRmsPosition.getPositionId() != null) {
            LambdaQueryWrapper<IntlRmsPosition> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsPosition::getPositionId, intlRmsPosition.getPositionId());
            IntlRmsPosition havePosition = intlRmsPositionReadMapper.selectOne(lambdaQuery);
            //设置json默认值
            intlRmsPosition.setPositionCategory(StringUtils.isBlank(intlRmsPosition.getPositionCategory()) ?
                    "[]" : intlRmsPosition.getPositionCategory());
            if (havePosition != null) {
                intlRmsPosition.setId(havePosition.getId());
                intlRmsPosition.setUpdatedAt(System.currentTimeMillis());
                intlRmsPositionMapper.updateById(intlRmsPosition);
                log.info("addPosition_update:{}", intlRmsPosition);
                //更新时，Store Name(name)字段发生更新，触发更新ES信息
                //判断name字段是否发生更新
                if (!intlRmsPosition.getName().equalsIgnoreCase(havePosition.getName())) {
                    log.info("addPosition_update_name id:{}, code:{}", intlRmsPosition.getId(),
                            intlRmsPosition.getCode());
                    //更新IMEI数据es信息
                    processDataSync(havePosition.getCode(), intlSoImeiMapper, IntlSoImei::getPositionRmsCode,
                            IntlSoImei::getId, DataSyncDataTypeEnum.IMEI);
                    //更新QTY数据es信息
                    processDataSync(havePosition.getCode(), intlSoQtyMapper, IntlSoQty::getPositionRmsCode,
                            IntlSoQty::getId, DataSyncDataTypeEnum.QTY);
                }
            } else {
                intlRmsPosition.setCreatedAt(System.currentTimeMillis());
                intlRmsPosition.setUpdatedAt(System.currentTimeMillis());
                intlRmsPositionMapper.insert(intlRmsPosition);
                log.info("addPosition_insert:{}", intlRmsPosition);
            }

        }
    }

    private void addCountryTimezone(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsCountryTimezone intlRmsCountryTimezone =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsCountryTimezone.class);
        if (intlRmsCountryTimezone != null && intlRmsCountryTimezone.getCountryCode() != null) {
            LambdaQueryWrapper<IntlRmsCountryTimezone> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsCountryTimezone::getCountryCode, intlRmsCountryTimezone.getCountryCode());
            IntlRmsCountryTimezone haveCountryTimezone = intlRmsCountryTimezoneReadMapper.selectOne(lambdaQuery);
            if (haveCountryTimezone != null) {
                intlRmsCountryTimezone.setId(haveCountryTimezone.getId());
                intlRmsCountryTimezone.setUpdatedAt(System.currentTimeMillis());
                intlRmsCountryTimezoneMapper.updateById(intlRmsCountryTimezone);
                log.info("addCountryTimezone_update:{}", intlRmsCountryTimezone);
            } else {
                intlRmsCountryTimezone.setCreatedAt(System.currentTimeMillis());
                intlRmsCountryTimezone.setUpdatedAt(System.currentTimeMillis());
                intlRmsCountryTimezoneMapper.insert(intlRmsCountryTimezone);
                log.info("addCountryTimezone_insert:{}", intlRmsCountryTimezone);
            }
        }
    }

    private void addPersonnelPosition(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsPersonnelPosition intlRmsPersonnelPosition =
                JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                        IntlRmsPersonnelPosition.class);
        if (intlRmsPersonnelPosition != null && intlRmsPersonnelPosition.getAssociationId() != null) {
            LambdaQueryWrapper<IntlRmsPersonnelPosition> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsPersonnelPosition::getAssociationId, intlRmsPersonnelPosition.getAssociationId());
            IntlRmsPersonnelPosition havePersonnelPosition = intlRmsPersonnelPositionReadMapper.selectOne(lambdaQuery);
            if (havePersonnelPosition != null) {
                intlRmsPersonnelPosition.setId(havePersonnelPosition.getId());
                intlRmsPersonnelPosition.setUpdatedAt(System.currentTimeMillis());
                intlRmsPersonnelPositionMapper.updateById(intlRmsPersonnelPosition);
                log.info("addPersonnelPosition_update:{}", intlRmsPersonnelPosition);
            } else {
                intlRmsPersonnelPosition.setCreatedAt(System.currentTimeMillis());
                intlRmsPersonnelPosition.setUpdatedAt(System.currentTimeMillis());
                intlRmsPersonnelPositionMapper.insert(intlRmsPersonnelPosition);
                log.info("addPersonnelPosition_insert:{}", intlRmsPersonnelPosition);
            }
        }
    }

    private void addSignRule(RmsDbContentRequest rmsDBContentRequest) {
        IntlRmsSignRule intlRmsSignRule = JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                IntlRmsSignRule.class);
        if (intlRmsSignRule != null && intlRmsSignRule.getSignRuleId() != null) {
            LambdaQueryWrapper<IntlRmsSignRule> lambdaQuery = Wrappers.lambdaQuery();
            lambdaQuery.eq(IntlRmsSignRule::getSignRuleId, intlRmsSignRule.getSignRuleId());
            IntlRmsSignRule haveSignRule = intlRmsSignRuleReadMapper.selectOne(lambdaQuery);
            if (haveSignRule != null) {
                intlRmsSignRule.setId(haveSignRule.getId());
                intlRmsSignRule.setUpdatedAt(System.currentTimeMillis());
                intlRmsSignRuleMapper.updateById(intlRmsSignRule);
                log.info("addSignRule_update:{}", intlRmsSignRule);
            } else {
                intlRmsSignRule.setCreatedAt(System.currentTimeMillis());
                intlRmsSignRule.setUpdatedAt(System.currentTimeMillis());
                intlRmsSignRuleMapper.insert(intlRmsSignRule);
                log.info("addSignRule_insert:{}", intlRmsSignRule);
            }
        }
    }

    private void addRrp(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsRrp intlRmsRrp =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsRrp.class);
            if (intlRmsRrp != null && intlRmsRrp.getRrpId() != null) {
                LambdaQueryWrapper<IntlRmsRrp> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsRrp::getRrpId, intlRmsRrp.getRrpId());
                IntlRmsRrp haveRrp = intlRmsRrpMapper.selectOne(lambdaQuery);
                if (haveRrp != null) {
                    intlRmsRrp.setId(haveRrp.getId());
                    intlRmsRrp.setUpdatedAt(System.currentTimeMillis());
                    intlRmsRrpMapper.updateById(intlRmsRrp);
                    log.info("addRrp_update:{}", intlRmsRrp);
                } else {
                    intlRmsRrp.setCreatedAt(System.currentTimeMillis());
                    intlRmsRrp.setUpdatedAt(System.currentTimeMillis());
                    intlRmsRrpMapper.insert(intlRmsRrp);
                    log.info("addRrp_insert:{}", intlRmsRrp);
                }
            }
        } catch (Exception e) {
            log.error("addRrp error", e);
        }
    }

    private void addProvince(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsProvince intlRmsProvince =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsProvince.class);
            if (intlRmsProvince != null && intlRmsProvince.getProvinceCode() != null) {
                LambdaQueryWrapper<IntlRmsProvince> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsProvince::getProvinceCode, intlRmsProvince.getProvinceCode());
                IntlRmsProvince haveProvince = intlRmsProvinceMapper.selectOne(lambdaQuery);
                if (haveProvince != null) {
                    intlRmsProvince.setId(haveProvince.getId());
                    intlRmsProvince.setUpdatedAt(System.currentTimeMillis());
                    intlRmsProvinceMapper.updateById(intlRmsProvince);
                    log.info("addProvince_update:{}", intlRmsProvince);
                } else {
                    intlRmsProvince.setCreatedAt(System.currentTimeMillis());
                    intlRmsProvince.setUpdatedAt(System.currentTimeMillis());
                    intlRmsProvinceMapper.insert(intlRmsProvince);
                    log.info("addProvince_insert:{}", intlRmsProvince);
                }
            }
        } catch (Exception e) {
            log.error("addProvince_error", e);
        }
    }

    private void addCity(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsCity intlRmsCity =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsCity.class);
            if (intlRmsCity != null && intlRmsCity.getCityCode() != null) {
                LambdaQueryWrapper<IntlRmsCity> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsCity::getCityCode, intlRmsCity.getCityCode());
                IntlRmsCity haveCity = intlRmsCityMapper.selectOne(lambdaQuery);
                if (haveCity != null) {
                    intlRmsCity.setId(haveCity.getId());
                    intlRmsCity.setUpdatedAt(System.currentTimeMillis());
                    intlRmsCityMapper.updateById(intlRmsCity);
                    log.info("addCity_update:{}", intlRmsCity);
                } else {
                    intlRmsCity.setCreatedAt(System.currentTimeMillis());
                    intlRmsCity.setUpdatedAt(System.currentTimeMillis());
                    intlRmsCityMapper.insert(intlRmsCity);
                    log.info("addCity_insert:{}", intlRmsCity);
                }
            }
        } catch (Exception e) {
            log.error("addCity_error", e);
        }
    }

    private void addSecondaryChannel(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsSecondarychannel intlRmsSecondarychannel =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()),
                            IntlRmsSecondarychannel.class);
            if (intlRmsSecondarychannel != null && intlRmsSecondarychannel.getChannelId() != null) {
                LambdaQueryWrapper<IntlRmsSecondarychannel> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsSecondarychannel::getChannelId, intlRmsSecondarychannel.getChannelId());
                IntlRmsSecondarychannel haveSecondaryChannel = intlRmsSecondarychannelMapper.selectOne(lambdaQuery);
                if (haveSecondaryChannel != null) {
                    intlRmsSecondarychannel.setId(haveSecondaryChannel.getId());
                    intlRmsSecondarychannel.setUpdatedAt(System.currentTimeMillis());
                    intlRmsSecondarychannelMapper.updateById(intlRmsSecondarychannel);
                    log.info("addSecondaryChannel_update:{}", intlRmsSecondarychannel);
                } else {
                    intlRmsSecondarychannel.setCreatedAt(System.currentTimeMillis());
                    intlRmsSecondarychannel.setUpdatedAt(System.currentTimeMillis());
                    intlRmsSecondarychannelMapper.insert(intlRmsSecondarychannel);
                    log.info("addSecondaryChannel_insert:{}", intlRmsSecondarychannel);
                }
            }
        } catch (Exception e) {
            log.error("addSecondaryChannel_error", e);
        }
    }

    private void updateProduct(RmsDbContentRequest rmsDBContentRequest) {
        try {
            IntlRmsProduct intlRmsProduct =
                    JsonUtil.json2bean(JsonUtil.bean2json(rmsDBContentRequest.getContent()), IntlRmsProduct.class);
            if (intlRmsProduct != null && intlRmsProduct.getGoodsId() != null) {
                LambdaQueryWrapper<IntlRmsProduct> lambdaQuery = Wrappers.lambdaQuery();
                lambdaQuery.eq(IntlRmsProduct::getGoodsId, intlRmsProduct.getGoodsId());
                IntlRmsProduct haveProduct = intlRmsProductMapper.selectOne(lambdaQuery);
                if (haveProduct != null) {
                    haveProduct.setModelLevel(intlRmsProduct.getModelLevel());
                    haveProduct.setModelLevelName(intlRmsProduct.getModelLevelName());
                    intlRmsProductMapper.updateById(haveProduct);
                }
            }
        } catch (Exception e) {
            log.error("updateProduct_error", e);
        }

    }
}
