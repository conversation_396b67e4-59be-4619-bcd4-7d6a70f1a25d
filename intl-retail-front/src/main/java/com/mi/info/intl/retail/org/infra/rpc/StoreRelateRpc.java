package com.mi.info.intl.retail.org.infra.rpc;

import com.mi.info.intl.retail.org.domain.PositionCommonItemList;
import com.mi.info.intl.retail.org.domain.util.BeanConverter;
import com.mi.info.intl.retail.org.domain.util.RpcUtil;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import com.xiaomi.cnzone.storems.api.model.req.store.CommonConfigReq;
import org.apache.commons.lang.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Service
public class StoreRelateRpc {

    @DubboReference(group = "${dubbo-group.consumer.maindata:}", version = "1.0", check = false, interfaceClass = StoreRelateProvider.class)
    StoreRelateProvider storeRelateProvider;

    public PositionCommonItemList get3CCommonPositionItemList(String areaId, String businessScene, String language) {
        CommonConfigReq req = new CommonConfigReq();
        req.setLanguage(language);
        if (StringUtils.isNotEmpty(areaId)) {
            req.setInternationalAreaId(areaId);
        }
        if (StringUtils.isNotEmpty(businessScene)) {
            req.setBusinessScene(businessScene);
        }
        CommonConfigDTO2 commonConfig =
                RpcUtil.getRpcResult(storeRelateProvider.get3CCommonConfig(req));
        return BeanConverter.INSTANCE.map(commonConfig, language);
    }
}
