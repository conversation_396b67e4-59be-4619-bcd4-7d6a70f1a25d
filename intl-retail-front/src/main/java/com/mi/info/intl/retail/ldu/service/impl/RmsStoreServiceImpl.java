package com.mi.info.intl.retail.ldu.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsPersonnelPosition;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsPersonnelPositionMapper;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = RmsStoreService.class)
public class RmsStoreServiceImpl implements RmsStoreService {

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Resource
    private IntlRmsPositionMapper intlRmsPositionMapper;

    @Resource
    private IntlRmsUserMapper intlRmsUserMapper;

    @Resource
    private IntlRmsPersonnelPositionMapper intlRmsPersonnelPositionMapper;

    @Override
    public Optional<RmsStoreInfoDto> getStoreInfoByStoreCode(String storeCode) {
        log.info("getStoreInfoByStoreCode storeCode = {}", storeCode);
        if (StringUtils.isEmpty(storeCode)) {
            log.error("store can not be null");
            return Optional.empty();
        }

        LambdaQueryWrapper<IntlRmsStore> qw = Wrappers.lambdaQuery();
        qw.eq(IntlRmsStore::getCode, storeCode);
        IntlRmsStore storeInfo = intlRmsStoreMapper.selectOne(qw);

        if (storeInfo == null) {
            log.error("storeCode can not find store");
            return Optional.empty();
        }
        RmsStoreInfoDto rmsStoreInfoDto = new RmsStoreInfoDto();
        ComponentLocator.getConverter().convert(storeInfo, rmsStoreInfoDto);
        rmsStoreInfoDto.setCityName(storeInfo.getCityIdName());
        rmsStoreInfoDto.setProvinceName(storeInfo.getProvinceLabel());
        return Optional.of(rmsStoreInfoDto);
    }

    @Override
    public Optional<RmsPositionInfoRes> getPositionIfoByPositionCode(String positionCode) {
        log.info("getPositionIfoByStoreCode positionCode = {}", positionCode);
        if (StringUtils.isEmpty(positionCode)) {
            log.error("positionCode can not be null");
            return Optional.empty();
        }

        LambdaQueryWrapper<IntlRmsPosition> qw = Wrappers.lambdaQuery();
        qw.eq(IntlRmsPosition::getCode, positionCode);
        IntlRmsPosition intlRmsPosition = intlRmsPositionMapper.selectOne(qw);

        if (intlRmsPosition == null) {
            log.error("positionCode can not find position");
            return Optional.empty();
        }
        RmsPositionInfoRes rmsPositionInfoDto = new RmsPositionInfoRes();
        ComponentLocator.getConverter().convert(intlRmsPosition, rmsPositionInfoDto);
        return Optional.of(rmsPositionInfoDto);
    }

    @Override
    public List<Integer> getUserStoreIdsByMiId(Long miId) {
        log.info("getUserStoreIdsByMiId miId = {}", miId);
        if (miId == null) {
            log.error("miId can not be null");
            return new java.util.ArrayList<>();
        }

        try {
            // 根据miId查询用户信息
            LambdaQueryWrapper<IntlRmsUser> userQuery = Wrappers.lambdaQuery();
            userQuery.eq(IntlRmsUser::getMiId, miId);
            userQuery.eq(IntlRmsUser::getIsDisabled, 0); // 用户必须是启用状态
            IntlRmsUser user = intlRmsUserMapper.selectOne(userQuery);

            if (user == null) {
                log.warn("用户不存在或已禁用, miId: {}", miId);
                return new java.util.ArrayList<>();
            }

            // 查询用户关联的阵地
            LambdaQueryWrapper<IntlRmsPersonnelPosition> positionQuery = Wrappers.lambdaQuery();
            positionQuery.eq(IntlRmsPersonnelPosition::getUserId, user.getRmsUserid());
            positionQuery.eq(IntlRmsPersonnelPosition::getStateCode, 0); // 关联关系必须是有效状态
            List<IntlRmsPersonnelPosition> personnelPositions =
                    intlRmsPersonnelPositionMapper.selectList(positionQuery);

            if (personnelPositions.isEmpty()) {
                log.warn("用户没有关联的阵地, miId: {}", miId);
                return new java.util.ArrayList<>();
            }

            // 获取阵地ID列表
            List<String> positionIds = personnelPositions.stream()
                    .map(IntlRmsPersonnelPosition::getPositionId)
                    .collect(Collectors.toList());

            // 查询阵地关联的门店
            LambdaQueryWrapper<IntlRmsPosition> positionStoreQuery = Wrappers.lambdaQuery();
            positionStoreQuery.in(IntlRmsPosition::getPositionId, positionIds);
            positionStoreQuery.eq(IntlRmsPosition::getStateCode, 0); // 阵地必须是有效状态
            List<IntlRmsPosition> positions = intlRmsPositionMapper.selectList(positionStoreQuery);

            if (positions.isEmpty()) {
                log.warn("阵地没有关联的门店, miId: {}", miId);
                return new java.util.ArrayList<>();
            }

            // 获取门店ID列表
            List<String> storeIds = positions.stream()
                    .map(IntlRmsPosition::getStoreId)
                    .filter(StringUtils::isNotBlank)
                    .distinct()
                    .collect(Collectors.toList());

            // 验证门店是否有效
            if (!storeIds.isEmpty()) {
                LambdaQueryWrapper<IntlRmsStore> storeQuery = Wrappers.lambdaQuery();
                storeQuery.in(IntlRmsStore::getStoreId, storeIds);
                storeQuery.eq(IntlRmsStore::getStateCode, 0); // 门店必须是有效状态
                List<IntlRmsStore> stores = intlRmsStoreMapper.selectList(storeQuery);

                List<Integer> validStoreIds = stores.stream()
                        .map(IntlRmsStore::getId)
                        .collect(Collectors.toList());

                log.info("用户关联的有效门店数量: {}, miId: {}", validStoreIds.size(), miId);
                return validStoreIds;
            }

            return new java.util.ArrayList<>();
        } catch (Exception e) {
            log.error("获取用户关联门店失败, miId: {}", miId, e);
            return new java.util.ArrayList<>();
        }
    }

    @Override
    public Map<String, RmsStoreInfoDto> getStoreInfoByStoreCodes(List<String> storeCodes) {
        Map<String, RmsStoreInfoDto> result = new HashMap<>();
        if (CollectionUtils.isEmpty(storeCodes)) {
            return result;
        }

        // 分批处理，避免参数过多
        int batchSize = 100;
        for (int i = 0; i < storeCodes.size(); i += batchSize) {
            int endIndex = Math.min(i + batchSize, storeCodes.size());
            List<String> batchCodes = storeCodes.subList(i, endIndex);

            try {
                // 调用批量查询接口
                List<RmsStoreInfoDto> batchResult = getStoreInfoByStoreCodesBatch(batchCodes);
                batchResult.forEach(store -> result.put(store.getCode(), store));
            } catch (Exception e) {
                log.error("Batch query store info failed for batch: {}", batchCodes, e);
                throw e;
            }
        }

        return result;
    }

    // 实际的批量查询实现（根据具体API调整）
    private List<RmsStoreInfoDto> getStoreInfoByStoreCodesBatch(List<String> storeCodes) {
        LambdaQueryWrapper<IntlRmsStore> storeQuery = Wrappers.lambdaQuery();
        storeQuery.in(IntlRmsStore::getCode, storeCodes).select(IntlRmsStore::getCode,
                IntlRmsStore::getType, IntlRmsStore::getGrade,
                IntlRmsStore::getHasPc, IntlRmsStore::getHasSr, IntlRmsStore::getChannelType,
                IntlRmsStore::getCountryShortcode);
        List<IntlRmsStore> intlRmsStores = intlRmsStoreMapper.selectList(storeQuery);
        return intlRmsStores.stream().map(store -> {
            RmsStoreInfoDto storeInfoDto = new RmsStoreInfoDto();
            storeInfoDto.setCode(store.getCode());
            storeInfoDto.setType(store.getType());
            storeInfoDto.setGrade(store.getGrade());
            storeInfoDto.setChannelType(store.getChannelType());
            storeInfoDto.setHasPc(store.getHasPc());
            storeInfoDto.setHasSR(storeInfoDto.getHasSR());
            storeInfoDto.setCountryShortcode(store.getCountryShortcode());
            return storeInfoDto;
        }).collect(Collectors.toList());
    }

    public Map<String, RmsStoreInfoDto> batchGetStoreInfoByStoreCode(List<String> storeCodes) {
        if (CollectionUtils.isEmpty(storeCodes)) {
            log.error("stores can not be null");
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<IntlRmsStore> qw = Wrappers.lambdaQuery();
        qw.in(IntlRmsStore::getCode, storeCodes);
        List<IntlRmsStore> storeInfoList = intlRmsStoreMapper.selectList(qw);

        if (CollectionUtils.isEmpty(storeInfoList)) {
            return Collections.emptyMap();
        }
        Map<String, RmsStoreInfoDto> map = new HashMap<>(storeInfoList.size());
        storeInfoList.forEach(storeInfo -> {
            RmsStoreInfoDto rmsStoreInfoDto = new RmsStoreInfoDto();
            ComponentLocator.getConverter().convert(storeInfo, rmsStoreInfoDto);
            rmsStoreInfoDto.setCityName(storeInfo.getCityIdName());
            rmsStoreInfoDto.setProvinceName(storeInfo.getProvinceLabel());
            map.put(storeInfo.getCode(), rmsStoreInfoDto);
        });
        return map;
    }

    @Override
    public Map<String, RmsPositionInfoRes> batchGetPositionIfoByPositionCode(List<String> positionCodes) {
        if (CollectionUtils.isEmpty(positionCodes)) {
            log.error("positionCodes can not be null");
            return Collections.emptyMap();
        }

        LambdaQueryWrapper<IntlRmsPosition> qw = Wrappers.lambdaQuery();
        qw.in(IntlRmsPosition::getCode, positionCodes);
        List<IntlRmsPosition> intlRmsPositions = intlRmsPositionMapper.selectList(qw);

        if (CollectionUtils.isEmpty(intlRmsPositions)) {
            log.error("positionCodes can not find position");
            return Collections.emptyMap();
        }

        Map<String, RmsPositionInfoRes> map = new HashMap<>();
        intlRmsPositions.forEach(intlRmsPosition -> {
            RmsPositionInfoRes rmsPositionInfoDto = new RmsPositionInfoRes();
            ComponentLocator.getConverter().convert(intlRmsPosition, rmsPositionInfoDto);
            map.put(intlRmsPosition.getCode(), rmsPositionInfoDto);
        });
        return map;
    }
}
