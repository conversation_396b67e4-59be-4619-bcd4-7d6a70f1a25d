package com.mi.info.intl.retail.org.app.event.listener;

import cn.hutool.core.io.FileUtil;
import cn.mioffice.guard.mail.MailMessage;
import cn.mioffice.guard.mail.MiMailSender;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionFurnitureRequest;
import com.mi.info.intl.retail.org.app.event.PositionImageBatchUploadEvent;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.position.service.PositionInspectionDomainService;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.infra.entity.ImageLocal;
import com.mi.info.intl.retail.org.infra.entity.PositionImageInfo;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import com.xiaomi.nr.global.dev.neptune.T;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.event.EventListener;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
@RequiredArgsConstructor
public class PositionImageBatchUploadEventListener {

    private final InspectionRecordRepository inspectionRecordRepository;

    private final FdsService fdsService;

    private final MiMailSender miMailSender;

    private final RuleConfigRepository ruleConfigRepository;

    private final PositionInspectionDomainService positionInspectionDomainService;

    private final IntlRmsUserService userService;

    private final IntlCountryTimezoneService countryTimezoneService;

    private final PositionRepository positionRepository;

    @Async
    @EventListener
    public void handlePositionImageBatchUploadEvent(PositionImageBatchUploadEvent event) {
        try {
            batchSetPositionAndCheckCountry(event);

            Optional<IntlRmsUserDto> userDto = userService.getIntlRmsUserByMiId(event.getMiId());
            // 获取操作人姓名
            String operatorName = event.getOperatorName();
            
            event.getSource().stream()
                    .peek(img -> img.setAccount(userDto.map(IntlRmsUserDto::getEnglishName).orElse("unknown")))
                    .filter(PositionImageInfo::isSuccess)
                    .filter(this::checkFurnitureExist)
                    .filter(positionImageInfo -> positionImageInfo.uploadIfCompleted(
                            f -> fdsService.upload(f.getName(), f, true).getUrl()))
                    .map(PositionImageInfo::buildUploadDataJson)
                    .forEach(img -> updateAndFinishTask(img, operatorName));
            sendEmail(event, userDto);
        } finally {
            FileUtil.del(event.getWorkerDir());
        }
    }

    private void batchSetPositionAndCheckCountry(PositionImageBatchUploadEvent event) {
        String areaId = RequestContextInfo.getAreaId();
        List<String> positions =
                event.getSource().stream().map(PositionImageInfo::getPositionCode).collect(Collectors.toList());
        // 查询RmsPosition
        List<PositionDomain> positionDomains = positionRepository.getByPositionCodeList(positions);
        // 查询国家
        List<String> countryIds =
                positionDomains.stream().map(PositionDomain::getCountry).distinct().collect(Collectors.toList());
        Map<String, String> countryMap = countryTimezoneService.getCountryCodeByCountryIds(countryIds);
        Map<String, PositionDomain> positonMap =
                positionDomains.stream().collect(Collectors.toMap(PositionDomain::getCrpsCode, Function.identity()));
        // 查询PositionRecord
        List<InspectionRecordDomain> inspectionRecordDomainList =
                inspectionRecordRepository.getByBusinessCodeList(positions);
        Map<String, InspectionRecordDomain> positionCodeRecordMap = new HashMap<>();
        List<String> ruleCodes = new ArrayList<>();
        for (InspectionRecordDomain record : inspectionRecordDomainList) {
            InspectionRecordDomain exist = positionCodeRecordMap.get(record.getPositionCode());
            if (exist == null || exist.getCreatedOn() < record.getCreatedOn()) {
                positionCodeRecordMap.put(record.getPositionCode(), record);
                ruleCodes.add(record.getRuleCode());
            }
        }
        // 查询RuleConfig
        List<RuleConfigDomain> ruleCodeDomains = ruleConfigRepository.getByRuleCodeList(ruleCodes);
        Map<String, RuleConfigDomain> ruleCodeConfigMap =
                ruleCodeDomains.stream().collect(Collectors.toMap(RuleConfigDomain::getRuleCode, Function.identity()));

        for (PositionImageInfo img : event.getSource()) {
            String positionCode = img.getPositionCode();
            PositionDomain positionDomain = positonMap.get(positionCode);
            if (positionDomain == null) {
                log.warn("rms position domain not found: {}", positionCode);
                img.markFailed(T.tr("inspection.position_code_not_found"));
                continue;
            }
            img.setPositionDomain(positionDomain);
            String countryCode = countryMap.get(positionDomain.getCountry());
            if (!Objects.equals(areaId, countryCode)) {
                log.warn("country not found: {}", positionCode);
                img.markFailed(T.tr("inspection.position_code_not_found"));
                continue;
            }
            InspectionRecordDomain inspectionRecord = positionCodeRecordMap.get(positionCode);
            if (inspectionRecord == null) {
                log.warn("inspection record not found: {}", positionCode);
                img.markFailed(T.tr("inspection.position_code_not_found"));
                continue;
            }
            img.buildInspectionRecord(inspectionRecord);
            RuleConfigDomain ruleConfigDomain = ruleCodeConfigMap.get(inspectionRecord.getRuleCode());
            if (ruleConfigDomain == null) {
                log.warn("rule config not found: {}", positionCode);
                img.markFailed("inspection.position_code_not_found");
                continue;
            }
            img.buildRuleConfig(ruleConfigDomain);
        }
    }

    private boolean checkFurnitureExist(PositionImageInfo positionImageInfo) {
        PositionFurnitureRequest furnitureRequest = new PositionFurnitureRequest();
        furnitureRequest.setPositionCode(positionImageInfo.getPositionCode());
        List<OptionalItem<Integer>> positionFurnitureList =
                positionInspectionDomainService.getPositionFurnitureList(furnitureRequest);
        Map<String, List<ImageLocal>> furnitureGroup =
                positionImageInfo.getFurniturePicture().stream().collect(Collectors.groupingBy(ImageLocal::getName));
        // 文件夹和家具数量对不上
        if (positionFurnitureList.size() != furnitureGroup.size()) {
           return positionImageInfo.markFailed(T.tr("inspection.furniture_picture_not_matched"));
        }
        List<String> furnitures =
                positionFurnitureList.stream().map(OptionalItem::getValue).collect(Collectors.toList());
        // 比对文件夹名称是否和家具名称匹配(由于Windows和Mac不支持部分特殊符号,去掉特殊符号后比较)
        furnitureGroup.forEach((dirName, list) -> {
            String replacedDir = dirName.replaceAll("[\\\\/:*?\"<>|]", "");
            Iterator<String> each = furnitures.iterator();
            while (each.hasNext()) {
                String furnitureName = each.next();
                String replacedName = furnitureName.replaceAll("[\\\\/:*?\"<>|]", "");
                if (StringUtils.startsWith(replacedDir, replacedName)) {
                    list.forEach(e -> e.setFurnitureName(furnitureName));
                    each.remove();
                    break;
                }
            }
        });
        // 文件夹和家具名称对不上
        if (!furnitures.isEmpty()) {
            positionImageInfo.markFailed(T.tr("inspection.furniture_picture_not_matched"));
        }
        return furnitures.isEmpty();
    }

    public void updateAndFinishTask(PositionImageInfo img, String operatorName) {
        try {
            positionInspectionDomainService.updateInspectionRecordAndFinishTask(img, operatorName);
        } catch (Exception e) {
            log.error("updateAndFinishTask failed", e);
            img.markFailed(T.tr("inspection.store_record_failed"));
        }
    }

    @SneakyThrows
    private void sendEmail(PositionImageBatchUploadEvent event, Optional<IntlRmsUserDto> userDto) {
        String subject = T.tr("inspection.batch_upload_mail_subject");
        String content = generateSimpleTaskTableHtml(event.getSource());
        String email = userDto.map(IntlRmsUserDto::getEmail).orElse(event.getEmail());
        // 邮件发送逻辑
        log.info("Email to: {}, Subject: {}, Content: {}", email, subject, content);
        if (!StringUtils.isEmpty(email)) {
            MailMessage message = MailMessage.build().subject(subject).content(content).to(email);
            String messageId = miMailSender.send(message);
            log.info("Send mail success, messageId: {}", messageId);
        }
    }

    public String generateSimpleTaskTableHtml(Collection<PositionImageInfo> items) {
        StringBuilder sb = new StringBuilder();
        sb.append("<h3>").append(T.tr("inspection.batch_upload_mail_title")).append("</h3>")
                .append("<table border='1' style='border-collapse:collapse;'>")
                .append("<tr><th>").append(T.tr("inspection.front_code")).append("</th><th>")
                .append(T.tr("inspection.status")).append("</th><th>").append(T.tr("inspection.remark"))
                .append("</th></tr>");

        for (PositionImageInfo item : items) {
            sb.append("<tr>").append("<td>").append(item.getPositionCode()).append("</td>")
                    .append("<td>").append(item.isSuccess() ? T.tr("inspection.success") : T.tr("inspection.failed"))
                    .append("</td>")
                    .append("<td>").append(item.getRemark()).append("</td>")
                    .append("</tr>");
        }

        sb.append("</table>");
        return sb.toString();
    }
}
