package com.mi.info.intl.retail.ldu.infra.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-04
 */

public interface IntlLduTargetMapper extends BaseMapper<IntlLduTarget> {

    void batchInsert(@Param("confList") List<IntlLduTarget> confList);

    IntlLduTarget selectByCountryRetailerProduct(@Param("countryCode") String countryCode,
                                                    @Param("goodsId") String goodsId,
                                                    @Param("retailerCode") String retailerCode);

    int updateById(@Param("conf") IntlLduTarget conf);

    void batchUpdate(@Param("intlLduTargetList") List<IntlLduTarget> intlLduTargetList);
}
