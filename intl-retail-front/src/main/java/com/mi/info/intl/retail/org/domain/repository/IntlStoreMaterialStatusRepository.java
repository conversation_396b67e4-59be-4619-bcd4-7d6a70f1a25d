package com.mi.info.intl.retail.org.domain.repository;

import com.mi.info.intl.retail.intlretail.service.api.material.dto.IntlStoreMaterialStatusConfrimReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.IntlStoreMaterialStatusVO;
import com.mi.info.intl.retail.org.domain.IntlStoreMaterialStatusDomain;

import javax.validation.Valid;
import java.util.List;

public interface IntlStoreMaterialStatusRepository {
    
    List<IntlStoreMaterialStatusVO> findByBusinessCode(String businessCode);

    void batchSave(List<IntlStoreMaterialStatusDomain> intlStoreMaterialStatusDomain);

    int materialConfirm(@Valid List<IntlStoreMaterialStatusConfrimReq> list);

    List<String> findNotExistBusinessCodes(List<String> orgIds);
}
