package com.mi.info.intl.retail.ldu.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;


@TableName(value = "intl_ldu_target", autoResultMap = true)
@Data
public class IntlLduTarget implements Serializable {

    private static final long serialVersionUID = -765432109876543123L;

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 区域
     */
    @TableField(value = "region")
    private String region;

    /**
     * 区域编码
     */
    @TableField(value = "region_code")
    private String regionCode;

    /**
     * 国家
     */
    @TableField(value = "country")
    private String country;

    /**
     * 国家编码
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 渠道类型
     */
    @TableField(value = "channel_type")
    private String channelType;

    /**
     * 零售商编码
     */
    @TableField(value = "retailer_code")
    private String retailerCode;

    /**
     * 零售商名称
     */
    @TableField(value = "retailer_name")
    private String retailerName;


    /**
     * 产品线
     */
    @TableField(value = "product_line")
    private String productLine;


    /**
     * 产品ID
     */
    @TableField(value = "goods_id")
    private String goodsId;

    /**
     * 产品名称
     */
    @TableField(value = "goods_name")
    private String goodsName;

    /**
     * 产品ID
     */
    @TableField(value = "product_id")
    private String productId;

    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 项目代码
     */
    @TableField(value = "project_code")
    private String projectCode;

    /**
     * RAM容量
     */
    @TableField(value = "ram_capacity")
    private String ramCapacity;

    /**
     * 目标创建日期
     */
    @TableField(value = "target_create_date")
    private long targetCreateDate;

    /**
     * 目标修改日期
     */
    @TableField(value = "target_update_date")
    private long targetUpdateDate;

    /**
     * ROM容量
     */
    @TableField(value = "rom_capacity")
    private String romCapacity;


    /**
     * 创建人ID
     */
    @TableField(value = "create_user_id")
    private String createUserId;

    /**
     * 创建人姓名
     */
    @TableField(value = "create_user_name")
    private String createUserName;

    /**
     * 修改人ID
     */
    @TableField(value = "update_user_id")
    private String updateUserId;

    /**
     * 修改人ID
     */
    @TableField(value = "update_user_name")
    private String updateUserName;

    /**
     * 目标覆盖门店数
     */
    @TableField(value = "target_covered_stores")
    private int targetCoveredStores;

    /**
     * 实际覆盖门店数
     */
    @TableField(value = "actual_covered_stores")
    private int actualCoveredStores;

    /**
     * 目标销出样品数
     */
    @TableField(value = "target_sample_out")
    private int targetSampleOut;

    /**
     * 实际销出样品数
     */
    @TableField(value = "actual_sample_out")
    private int actualSampleOut;

}
