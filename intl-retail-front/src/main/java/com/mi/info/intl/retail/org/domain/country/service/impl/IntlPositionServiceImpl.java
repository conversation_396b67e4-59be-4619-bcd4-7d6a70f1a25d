package com.mi.info.intl.retail.org.domain.country.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.front.dto.RmsPositionIAndStoreRes;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.front.position.dto.StoreValidationInfoDTO;
import com.mi.info.intl.retail.org.domain.country.service.IntlPositionService;
import com.mi.info.intl.retail.org.domain.country.service.IntlStoreService;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 国际零售组织-职位服务实现类
 *
 * <AUTHOR>
 * @date 2025/07/28
 */
@Slf4j
@Service
public class IntlPositionServiceImpl extends ServiceImpl<IntlRmsPositionMapper, IntlRmsPosition>
        implements IntlPositionService, IntlPositionApiService {

    @Resource
    private IntlStoreService intlStoreService;

    @Resource
    private RedisClient redisClient;

    @Override
    public Optional<IntlPositionDTO> getRetailerByPositionCode(IntlPositionDTO dto) {
        LambdaQueryWrapper<IntlRmsPosition> wrapper = new LambdaQueryWrapper<>();
        wrapper =
                wrapper.select(IntlRmsPosition::getPositionId, IntlRmsPosition::getCode, IntlRmsPosition::getRetailerId,
                        IntlRmsPosition::getRetailerName, IntlRmsPosition::getStoreId);
        wrapper.eq(IntlRmsPosition::getCode, dto.getPositionCode()).isNotNull(IntlRmsPosition::getRetailerId)
                .ne(IntlRmsPosition::getRetailerId, "");
        IntlRmsPosition position = this.getOne(wrapper);
        if (position == null) {
            return Optional.empty();
        }
        IntlPositionDTO result = getIntlPositionDTO(position);

        Optional<IntlRmsStore> rmsStore = intlStoreService.getRetailerByStoreId(position.getStoreId());
        rmsStore.ifPresent(intlRmsStore -> {
            result.setStoreCode(intlRmsStore.getCode());
            result.setRetailerCode(intlRmsStore.getRetailerIdName());
        });
        return Optional.of(result);
    }

    private IntlPositionDTO getIntlPositionDTO(IntlRmsPosition position) {
        IntlPositionDTO result = new IntlPositionDTO();
        result.setPositionId(position.getPositionId());
        result.setPositionCode(position.getCode());
        result.setRetailerId(position.getRetailerId());
        return result;
    }

    @Override
    public Optional<PositionStoreInfoDTO> queryPositionWithStoreByCode(String positionCode) {
        log.info("查询阵地门店信息, positionCode: {}", positionCode);

        try {
            PositionStoreInfoDTO result = baseMapper.selectPositionWithStoreByCode(positionCode);

            if (result != null) {
                log.info("查询阵地门店信息成功, positionCode: {}, storeCode: {}",
                        positionCode, result.getStoreCode());
                return Optional.of(result);
            } else {
                log.warn("未找到阵地门店信息, positionCode: {}", positionCode);
                return Optional.empty();
            }

        } catch (Exception e) {
            log.error("查询阵地门店信息失败, positionCode: {}", positionCode, e);
            return Optional.empty();
        }
    }

    public List<RmsPositionInfoRes> getPositionsByStoreCode(String storeCode) {
        log.info("查询阵地门店信息, storeCode: {}", storeCode);
        try {
            List<IntlRmsPosition> result = baseMapper.getPositionsByStoreCode(storeCode);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            if (result != null && !result.isEmpty()) {
                return result.stream().map(item -> {
                    RmsPositionInfoRes res = new RmsPositionInfoRes();
                    res.setPositionId(item.getPositionId());
                    res.setStoreId(item.getStoreId());
                    res.setTypeName(item.getTypeName());
                    res.setType(item.getType());
                    res.setCreatedOn(sdf.format(item.getCreatedOn())); // 创建时间转换为yyyy-MM-dd HH:mm:ss
                    res.setCode(item.getCode());
                    res.setName(item.getName());
                    return res;
                }).collect(Collectors.toList());

            } else {
                log.warn("未找到阵地门店信息, storeCode: {}", storeCode);
                return Collections.emptyList();
            }

        } catch (Exception e) {
            log.error("查询阵地门店信息失败, storeCode: {}", storeCode, e);
            return Collections.emptyList();
        }
    }

    /**
     * 获取最优阵地列表
     */
    public List<RmsPositionInfoRes> getBestPositions(List<RmsPositionInfoRes> positions) {
        if (positions == null || positions.isEmpty()) {
            return Collections.emptyList();
        }
        // 根据store code分组
        return positions.stream().collect(Collectors.groupingBy(RmsPositionInfoRes::getStoreId)).values().stream()
                .map(this::getBestPosition).collect(Collectors.toList());
    }

    @Override
    public List<RmsPositionIAndStoreRes> getPositionsByPositionIds(List<String> positionIds) {
        if (CollectionUtils.isEmpty(positionIds)) {
            return Collections.emptyList();
        }

        // 批量从 Redis 获取数据
        List<RmsPositionIAndStoreRes> cachedPositions = getCachedPositions(positionIds);

        //找出缓存中没有的数据
        List<String> cachedPositionIds = cachedPositions.stream()
                .map(RmsPositionIAndStoreRes::getPositionId)
                .collect(Collectors.toList());

        List<String> missingPositionIds = positionIds.stream()
                .filter(id -> !cachedPositionIds.contains(id))
                .collect(Collectors.toList());

        // 3. 如果所有数据都已缓存，直接返回
        if (missingPositionIds.isEmpty()) {
            return cachedPositions;
        }

        // 4. 从数据库查询缺失的数据
        List<RmsPositionIAndStoreRes> dbPositions = baseMapper.getPositionsByPositionIds(missingPositionIds);

        // 5. 将数据库查询结果存入 Redis
        cachePositions(dbPositions);

        // 6. 合并缓存数据和数据库数据
        List<RmsPositionIAndStoreRes> result = new ArrayList<>(cachedPositions);
        result.addAll(dbPositions);

        return result;
    }

    private List<RmsPositionIAndStoreRes> getCachedPositions(List<String> positionIds) {
        List<RmsPositionIAndStoreRes> result = new ArrayList<>();
        for (String positionId : positionIds) {
            RmsPositionIAndStoreRes position =
                    redisClient.getObj(RedisKeyEnum.RMS_POSITION_CACHE.get(positionId), RmsPositionIAndStoreRes.class);
            if (position != null) {
                result.add(position);
            }
        }
        return result;
    }

    private void cachePositions(List<RmsPositionIAndStoreRes> positions) {
        for (RmsPositionIAndStoreRes position : positions) {
            redisClient.set(RedisKeyEnum.RMS_POSITION_CACHE.get(position.getPositionId()),
                    JacksonUtil.toStr(position));
        }
    }

    /**
     * 获取最优阵地
     */
    private RmsPositionInfoRes getBestPosition(List<RmsPositionInfoRes> positions) {
        if (positions == null || positions.isEmpty()) {
            return null;
        }

        // 先按照创建时间升序排序
        positions.sort(Comparator.comparing(RmsPositionInfoRes::getCreatedOn));

        // 按照优先级排序：SIS-ES-DZ-DC-POS
        String[] positionTypes = {"SIS", "ES", "DZ", "DC", "POS"};

        for (String positionType : positionTypes) {
            for (RmsPositionInfoRes position : positions) {
                if (positionType.equals(position.getTypeName())) {
                    return position;
                }
            }
        }

        // 如果没有找到优先级类型，返回创建时间最早的（排序后第一条）
        return positions.get(0);
    }

    @Override
    public List<IntlPositionDTO> getPositionsByCountryName(String countryName) {
        LambdaQueryWrapper<IntlRmsPosition> wrapper = new LambdaQueryWrapper<>();
        wrapper = wrapper.select(IntlRmsPosition::getPositionId, IntlRmsPosition::getCode,
                IntlRmsPosition::getRetailerId, IntlRmsPosition::getRetailerName, IntlRmsPosition::getStoreId);
        wrapper.eq(IntlRmsPosition::getCountryName, countryName);
        List<IntlRmsPosition> list = this.list(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return Lists.newArrayList();
        }
        return list.stream().map(this::getIntlPositionDTO).collect(Collectors.toList());
    }

    @Override
    public Optional<StoreValidationInfoDTO> getStoreValidationInfo(String storeCode, Long miId) {
        log.info("查询门店校验信息, storeCode: {}, miId: {}", storeCode, miId);
        try {
            // 1. 查询基础信息（门店信息和用户门店关系）
            StoreValidationInfoDTO result = baseMapper.getStoreValidationInfo(storeCode, miId);
            if (result == null) {
                log.warn("未找到门店信息或用户无权限, storeCode: {}, miId: {}", storeCode, miId);
                return Optional.empty();
            }

            // 2. 查询门店下所有阵地
            List<RmsPositionInfoRes> positions = getPositionsByStoreCode(storeCode);
            result.setPositions(positions);

            // 3. 获取最优阵地
            if (!positions.isEmpty()) {
                List<RmsPositionInfoRes> bestPositions = getBestPositions(positions);
                if (!bestPositions.isEmpty()) {
                    result.setBestPosition(bestPositions.get(0));
                }
            }

            log.info("查询门店校验信息成功, storeCode: {}, miId: {}", storeCode, miId);
            return Optional.of(result);

        } catch (Exception e) {
            log.error("查询门店校验信息失败, storeCode: {}, miId: {}", storeCode, miId, e);
            return Optional.empty();
        }
    }

    @Override
    public RmsStoreInfoDto getStoreByCode(String storeCode) {
        log.info("检查门店是否存在, storeCode: {}", storeCode);
        try {
            return baseMapper.getStoreByCode(storeCode);
        } catch (Exception e) {
            log.error("检查门店是否存在失败, storeCode: {}", storeCode, e);
            return null;
        }
    }

    @Override
    public Map<String, RmsPositionInfoRes> getPositionInfoByPositionCodes(List<String> positionCodes) {
        Map<String, RmsPositionInfoRes> result = new HashMap<>();
        if (CollectionUtils.isEmpty(positionCodes)) {
            return result;
        }
        LambdaQueryWrapper<IntlRmsPosition> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(IntlRmsPosition::getPositionId, IntlRmsPosition::getCode, IntlRmsPosition::getType
        ).in(IntlRmsPosition::getCode,
                positionCodes);
        List<IntlRmsPosition> intlRmsPositions = baseMapper.selectList(queryWrapper);
        List<RmsPositionInfoRes> positionInfoRes = intlRmsPositions.stream()
                .map(position -> {
                    return new RmsPositionInfoRes().setPositionId(position.getPositionId()).setCode(position.getCode());
                }).collect(Collectors.toList());
        positionInfoRes.forEach(rmsPositionInfoRes -> result.put(rmsPositionInfoRes.getCode(),
                rmsPositionInfoRes));
        return result;
    }
}
