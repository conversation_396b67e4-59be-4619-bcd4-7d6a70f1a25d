package com.mi.info.intl.retail.management.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.management.domain.PositionChangeLogDomain;
import com.mi.info.intl.retail.management.domain.repository.PositionChangeLogRepository;
import com.mi.info.intl.retail.management.infra.entity.PositionChangeLog;
import com.mi.info.intl.retail.management.infra.mapper.PositionChangeLogMapper;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 岗位变更日志仓储实现
 */
@Repository
public class PositionChangeLogRepositoryImpl implements PositionChangeLogRepository {

    @Autowired
    private PositionChangeLogMapper positionChangeLogMapper;

    @Override
    public boolean save(PositionChangeLogDomain domain) {
        PositionChangeLog entity = convertToEntity(domain);
        return positionChangeLogMapper.insert(entity) > 0;
    }

    @Override
    public PositionChangeLogDomain getById(Integer id) {
        PositionChangeLog entity = positionChangeLogMapper.selectById(id);
        return entity != null ? convertToDomain(entity) : null;
    }

    @Override
    public List<PositionChangeLogDomain> listAll() {
        List<PositionChangeLog> entities = positionChangeLogMapper.selectList(new LambdaQueryWrapper<>());
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    @Override
    public PositionChangeLogDomain findByPositionCode(String positionCode) {
        LambdaQueryWrapper<PositionChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PositionChangeLog::getPositionCode, positionCode)
                    .eq(PositionChangeLog::getEffectiveStatus, 1)
                    .orderByDesc(PositionChangeLog::getEffectiveTime)
                    .last("limit 1");
        PositionChangeLog entity = positionChangeLogMapper.selectOne(queryWrapper);
        return convertToDomain(entity);
    }

    @Override
    public List<PositionChangeLogDomain> findByPositionCodeAndEffectTime(String positionCode, Date effectTime) {
        LambdaQueryWrapper<PositionChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PositionChangeLog::getPositionCode, positionCode)
                    .eq(PositionChangeLog::getEffectiveStatus, 1)
                    .ge(PositionChangeLog::getEffectiveTime, effectTime)
                    .orderByDesc(PositionChangeLog::getEffectiveTime);
        List<PositionChangeLog> entities = positionChangeLogMapper.selectList(queryWrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    @Override
    public List<PositionChangeLogDomain> findByStoreCode(String storeCode) {
        LambdaQueryWrapper<PositionChangeLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(PositionChangeLog::getStoreCode, storeCode)
                    .eq(PositionChangeLog::getEffectiveStatus, 1)
                    .orderByDesc(PositionChangeLog::getEffectiveTime);
        List<PositionChangeLog> entities = positionChangeLogMapper.selectList(queryWrapper);
        return entities.stream().map(this::convertToDomain).collect(Collectors.toList());
    }

    private PositionChangeLog convertToEntity(PositionChangeLogDomain domain) {
        if (domain == null) return null;
        PositionChangeLog entity = new PositionChangeLog();
        BeanUtils.copyProperties(domain, entity);
        return entity;
    }

    private PositionChangeLogDomain convertToDomain(PositionChangeLog entity) {
        if (entity == null) return null;
        PositionChangeLogDomain domain = new PositionChangeLogDomain();
        BeanUtils.copyProperties(entity, domain);
        return domain;
    }
} 