package com.mi.info.intl.retail.org.domain.newproducttarget.listener;

import cn.hutool.core.convert.Convert;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.read.listener.ReadListener;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.convert.NewProductStartTimestampToAreaTimeConverter;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.GetUserInfoRespForAuth;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums.GetUserInfoRespForAuthTheGlobal;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums.NewProductStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums.NewProductTargetTypeEnum;
import com.mi.info.intl.retail.org.domain.RmsSyncDbManager;
import com.mi.info.intl.retail.org.infra.entity.IntlNewProductTarget;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlNewProductTargetMapper;
import com.mi.info.intl.retail.utils.DateUtils;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 导入新品目标的监听器
 *
 * @author: chuang
 * @since: 2025/8/11
 */
@Data
@Slf4j
public class NewProductTargetImportListener
        implements ReadListener<NewProductTargetImportListener.NewProductTargetImportData> {
    
    private final List<NewProductTargetImportListener.NewProductTargetImportData> importDataList = new ArrayList<>();
    
    IntlNewProductTargetMapper intlNewProductTargetMapper;
    
    String mid;
    
    String mname;
    
    long currentTime;
    
    String errorDataFileUrl = "";
    
    FdsService fdsService;
    
    RmsSyncDbManager rmsSyncDbManager;
    
    GetUserInfoRespForAuth userInfoAuth;
    public NewProductTargetImportListener(IntlNewProductTargetMapper intlNewProductTargetMapper, String mid,
            String mname, long currentTime, FdsService fdsService, RmsSyncDbManager rmsSyncDbManager,
            GetUserInfoRespForAuth userInfoAuth) {
        this.intlNewProductTargetMapper = intlNewProductTargetMapper;
        this.mid = mid;
        this.mname = mname;
        this.currentTime = currentTime;
        this.fdsService = fdsService;
        this.rmsSyncDbManager = rmsSyncDbManager;
        this.userInfoAuth = userInfoAuth;
    }
    
    /**
     * 导入数据类
     */
    @Data
    public static class NewProductTargetImportData {
        
        /**
         * 产品线
         */
        @ExcelProperty(value = "Product Line")
        private String productLineName;
        
        /**
         * 项目代码
         */
        @ExcelProperty(value = "Project")
        private String project;
        
        /**
         * 区域长码
         */
        @ExcelProperty(value = "Region")
        private String region;
        
        /**
         * 国家长码
         */
        @ExcelProperty(value = "Country")
        private String countryName;
        
        /**
         * 国家短码
         */
        String country;
        
        /**
         * 目标类型 1 首销期 2 生命周期
         */
        @ExcelProperty(value = "Period(1:First Sale;2:Life Cycle)")
        private String targetType;
        
        /**
         * LDU计划数
         */
        //        @ExcelProperty(value = "LDU  Plan Number")
        private Integer lduPlanCount;
        
        /**
         * LDU覆盖门店数
         */
        //        @ExcelProperty(value = "LDU Coverage Stores Target")
        private Integer lduStoreCoverage;
        
        /**
         * 库存覆盖门店目标数
         */
        @ExcelProperty(value = "Inventory Coverage Stores Target")
        private Integer priceTagCoverageTarget;
        
        /**
         * Dummy覆盖门店数
         */
        @ExcelProperty(value = "Dummy Coverage Stores Target")
        private Integer dummyStoreCoverage;
        
        /**
         * POSM覆盖门店数
         */
        @ExcelProperty(value = "POSM Coverage Stores Target")
        private Integer posmStoreCoverage;
        
        /**
         * 销售激活门店数
         */
        @ExcelProperty(value = "Sales Activations")
        private Integer salesActivation;
        
        /**
         * 零售可做功目标
         */
        @ExcelProperty(value = "Retail SO Target")
        private Integer retailEffortTarget;
        
        @ExcelProperty(value = "Start Date(mm/dd/yyyy)")
        private String startTimeStr;
        
//                @ExcelProperty(value = "Start Date(mm/dd/yyyy)",  converter = NewProductStartTimestampToAreaTimeConverter.class)
        private Long startTime;
        
        @ExcelProperty(value = "End Date(mm/dd/yyyy)")
        private String endTimeStr;
        
        //        @ExcelProperty(value = "End Date(mm/dd/yyyy)", converter = NewProductStartTimestampToAreaTimeConverter.class)
        Long endTime;
        
        public void initTimeStr(RmsSyncDbManager rmsSyncDbManager) {
            if (StringUtils.isBlank(countryName)) {
                return;
            }
            IntlRmsCountryTimezone intlRmsCountryTimezone = rmsSyncDbManager.getCountryTimezoneByCountryName(
                    countryName);
            if (intlRmsCountryTimezone == null) {
                return;
            }
            country = intlRmsCountryTimezone.getCountryCode();
            if (country == null) {
                return;
            }
            try {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(startTimeStr)) {
                    // 使用IntlTimeUtil将时间戳转换为对应时区的格式化时间
                    long[] timestampRange = IntlTimeUtil.getDayTimestampRange(startTimeStr,
                            DateUtils.DATE_FORMAT_MM_DD_YYYY, country);
                    startTime = timestampRange[0];
                }
                if (org.apache.commons.lang3.StringUtils.isNotBlank(endTimeStr)) {
                    // 使用IntlTimeUtil将时间戳转换为对应时区的格式化时间
                    long[] timestampRange = IntlTimeUtil.getDayTimestampRange(endTimeStr,
                            DateUtils.DATE_FORMAT_MM_DD_YYYY, country);
                    endTime = timestampRange[1];
                }
                
            } catch (Exception e) {
                log.error(
                        "时间戳转换失败: startTimeStr:{};endTimeStr:{} countryName:{}; country:{};timestamp={}, error={}",
                        startTimeStr, endTimeStr, countryName, country, e.getMessage(), e);
                // 发生异常时，返回null
                return;
            }
            return;
        }
        
    }
    
    /**
     * 异常数据类
     */
    @Data
    public static class NewProductTargetImportErrorData {
        
        
        @ExcelProperty("ErrorMessage")
        private String errorMessage;
        
        /**
         * 产品线
         */
        @ExcelProperty(value = "Product Line")
        private String productLineName;
        
        /**
         * 项目代码
         */
        @ExcelProperty(value = "Project")
        private String project;
        
        /**
         * 区域
         */
        @ExcelProperty(value = "Region")
        private String region;
        
        /**
         * 国家长码
         */
        @ExcelProperty(value = "Country")
        private String countryName;
        
//        /**
//         * 国家短码
//         */
//        String country;
        
        /**
         * 目标类型 1 首销期 2 生命周期
         */
        @ExcelProperty(value = "Period(1:First Sale;2:Life Cycle)")
        private String targetType;
        
        //        /**
        //         * LDU计划数
        //         */
        //        //        @ExcelProperty(value = "LDU  Plan Number")
        //        private Integer lduPlanCount;
        //
        //        /**
        //         * LDU覆盖门店数
        //         */
        //        //        @ExcelProperty(value = "LDU Coverage Stores Target")
        //        private Integer lduStoreCoverage;
        
        /**
         * 库存覆盖门店目标数
         */
        @ExcelProperty(value = "Inventory Coverage Stores Target")
        private Integer priceTagCoverageTarget;
        
        /**
         * Dummy覆盖门店数
         */
        @ExcelProperty(value = "Dummy Coverage Stores Target")
        private Integer dummyStoreCoverage;
        
        /**
         * POSM覆盖门店数
         */
        @ExcelProperty(value = "POSM Coverage Stores Target")
        private Integer posmStoreCoverage;
        
        /**
         * 销售激活门店数
         */
        @ExcelProperty(value = "Sales Activations")
        private Integer salesActivation;
        
        /**
         * 零售可做功目标
         */
        @ExcelProperty(value = "Retail SO Target")
        private Integer retailEffortTarget;
        
        @ExcelProperty(value = "Start Date(mm/dd/yyyy)")
        private String startTimeStr;
        
        //        @ExcelProperty(value = "Start Date(mm/dd/yyyy)", converter = NewProductStartTimestampToAreaTimeConverter.class)
//        private Long startTime;
        
        @ExcelProperty(value = "End Date(mm/dd/yyyy)")
        private String endTimeStr;
        
//        //        @ExcelProperty(value = "End Date(mm/dd/yyyy)", converter = NewProductStartTimestampToAreaTimeConverter.class)
//        Long endTime;
    }
    
    /**
     * 校验结果类
     */
    @Data
    private static class ValidationResultForNewProduct {
        
        /**
         * 校验成功的数据
         */
        private final List<NewProductTargetImportData> successData;
        
        /**
         * 校验失败的数据
         */
        private final List<NewProductTargetImportErrorData> errorData;
        
        public ValidationResultForNewProduct(List<NewProductTargetImportData> successData,
                List<NewProductTargetImportErrorData> errorData) {
            this.successData = successData;
            this.errorData = errorData;
        }
    }
    
    @Override
    public void invoke(NewProductTargetImportData data, AnalysisContext context) {
        importDataList.add(data);
        data.initTimeStr(rmsSyncDbManager);
    }
    
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        if (importDataList.isEmpty()) {
            errorDataFileUrl = "";
        }
        ValidationResultForNewProduct result = validateDataBase(importDataList);
        List<NewProductTargetImportData> successDataP1 = result.getSuccessData();
        List<NewProductTargetImportErrorData> errorData = result.getErrorData();
        if (!successDataP1.isEmpty()) {
            Map<String, NewProductTargetImportData> successImportDataP1Map = successDataP1.stream()
                    .collect(Collectors.toMap(x -> {
                        return x.getProductLineName() + "_" + x.getProject()  + "_" + x.getCountry()
                                + "_" + x.getTargetType();
                    }, v -> v, (o, n) -> o));
            //判断是否存在表中
            List<IntlNewProductTarget> addListTmp = Convert.toList(IntlNewProductTarget.class, successDataP1);
            List<IntlNewProductTarget> exsitsInitNewProdcutTarget = intlNewProductTargetMapper.selectListByConditionForExists(
                    addListTmp, "import");
            List<IntlNewProductTarget> canUpExsitsInitNewProdcutTarget = new LinkedList<>();
            Map<String, IntlNewProductTarget> exsitsInitNewProdcutTargetMap = exsitsInitNewProdcutTarget.stream()
                    .collect(Collectors.toMap(x -> {
                        return x.getProductLineName() + "_" + x.getProject()  + "_" + x.getCountry()
                                + "_" + x.getTargetType();
                    }, v -> v, (o, n) -> o));
            
            successImportDataP1Map.keySet().forEach(key -> {
                //原始的经过基本判断的导入数据
                NewProductTargetImportData importData = successImportDataP1Map.get(key);
                //经过筛选真的存在的导入数据
                IntlNewProductTarget intlNewProductTarget = exsitsInitNewProdcutTargetMap.get(key);
                //TODO 判断是否在这个时间中 ,角色
                if (intlNewProductTarget != null) {
                    if (GetUserInfoRespForAuthTheGlobal.NO_GLOBAL.getCode().equals(userInfoAuth.getTheGlobal())) {
                        if (!userInfoAuth.getThePosition().contains(intlNewProductTarget.getCountry())) {
                            NewProductTargetImportErrorData errorDataItem = Convert.convert(
                                    NewProductTargetImportErrorData.class, importData);
                            errorDataItem.setErrorMessage("User does not have permission to modify data for Country: " + importData.getCountryName());
                            errorData.add(errorDataItem);
                            return;
                        }
                        if (intlNewProductTarget.getCanModifyStartTime() == null
                                || intlNewProductTarget.getCanModifyStartTime() > currentTime
                                || intlNewProductTarget.getCanModifyEndTime() == null
                                || intlNewProductTarget.getCanModifyEndTime() < currentTime) {
                            NewProductTargetImportErrorData errorDataItem = Convert.convert(
                                    NewProductTargetImportErrorData.class, importData);
                            //                        errorDataItem.setErrorMessage(String.format(
                            //                                "modification time not set or not within allowed range",
                            //                                importData.getProductLine(), importData.getProject(), importData.getRegion(),
                            //                                importData.getCountry(), importData.getTargetType()));
                            errorDataItem.setErrorMessage("modification time not set or not within allowed range");
                            errorData.add(errorDataItem);
                            return;
                        }
                        
                    }
                    intlNewProductTarget.setPriceTagCoverageTarget(importData.getPriceTagCoverageTarget());
                    intlNewProductTarget.setDummyStoreCoverage(importData.getDummyStoreCoverage());
                    intlNewProductTarget.setPosmStoreCoverage(importData.getPosmStoreCoverage());
                    intlNewProductTarget.setSalesActivation(importData.getSalesActivation());
                    intlNewProductTarget.setRetailEffortTarget(importData.getRetailEffortTarget());
                    intlNewProductTarget.setStartTime(importData.getStartTime());
                    intlNewProductTarget.setEndTime(importData.getEndTime());
                    canUpExsitsInitNewProdcutTarget.add(intlNewProductTarget);
                } else {
                    NewProductTargetImportErrorData errorDataItem = Convert.convert(
                            NewProductTargetImportErrorData.class, importData);
                    //                    errorDataItem.setErrorMessage(String.format(
                    //                            "product line: %s, project: %s, region: %s, country: %s, targetType: %s, do not exist",
                    //                            importData.getProductLine(), importData.getProject(), importData.getRegion(),
                    //                            importData.getCountry(), importData.getTargetType()));
                    errorDataItem.setErrorMessage(
                            "Please check whether the country, product category, project and period are filled in correctly.");
                    errorData.add(errorDataItem);
                }
                
            });
            //批量更新
            for (IntlNewProductTarget upSub : canUpExsitsInitNewProdcutTarget) {
                upSub.setStatus(NewProductStatusEnum.MOIDFY.getCode());
                upSub.setProductLine(null);
                upSub.setProductLineName(null);
                upSub.setProject(null);
                upSub.setRegion(null);
                upSub.setCountry(null);
                upSub.setRegionName(null);
                upSub.setCountryName(null);
                upSub.setLduPlanCount(null);
                upSub.setLduStoreCoverage(null);
                upSub.setCanModifyStartTime(null);
                upSub.setCanModifyEndTime(null);
                if (NewProductTargetTypeEnum.LIFE_CIRCLE.getCode().equals(upSub.getTargetType())) {
                    upSub.setStartTime(null);
                    upSub.setEndTime(null);
                }
                upSub.setTargetType(null);
                upSub.setUpdatedTime(currentTime);
                upSub.setUpdatedBy(mid);
                upSub.setUpdatedName(mname);
                intlNewProductTargetMapper.updateById(upSub);
            }
        }
        //错误处理
        // 处理异常数据，导出到Excel
        if (!errorData.isEmpty()) {
            exportErrorDataToExcel(errorData);
            log.warn("新品目标导入 发现 {} 条异常数据，已导出到Excel", errorData.size());
        } else {
            errorDataFileUrl = "ok";
        }
    }
    
    /**
     * 基本的判断
     */
    private ValidationResultForNewProduct validateDataBase(List<NewProductTargetImportData> importDataList) {
        List<NewProductTargetImportData> successDataP1 = new ArrayList<>();
        List<NewProductTargetImportErrorData> errorData = new ArrayList<>();
        //基本的数据过滤
        for (NewProductTargetImportData importData : importDataList) {
            String errorMessage = validateSingleRecord(importData);
            if (errorMessage == null) {
                successDataP1.add(importData);
            } else {
                NewProductTargetImportErrorData errorDataItem = Convert.convert(NewProductTargetImportErrorData.class,
                        importData);
                
                errorDataItem.setErrorMessage(errorMessage);
                errorData.add(errorDataItem);
            }
        }
        
        return new ValidationResultForNewProduct(successDataP1, errorData);
    }
    
    private String validateSingleRecord(NewProductTargetImportData importData) {
        if (StringUtils.isBlank(importData.getProductLineName())) {
            return "Product Line cannot be empty.";
        }
        if (StringUtils.isBlank(importData.getProject())) {
            return "Project cannot be empty.";
        }
        if (StringUtils.isBlank(importData.getRegion())) {
            return "Region cannot be empty.";
        }
        if (StringUtils.isBlank(importData.getCountry())) {
            return "Country cannot be empty or country mapping not found.";
        }
        if (StringUtils.isBlank(importData.getTargetType())) {
            return "Target Type cannot be empty.";
        }
        if (!"1".equals(importData.getTargetType()) && !"2".equals(importData.getTargetType())) {
            return "Target Type must be 1 or 2.";
        }
        if ("1".equals(importData.getTargetType())) {
            if (importData.getStartTime() == null) {
                return "Start Time cannot be null when targetType is 1.";
            }
            if (importData.getEndTime() == null) {
                return "End Time cannot be null when targetType is 1.";
            }
            if (importData.getStartTime() > importData.getEndTime()) {
                return "Start Time cannot be greater than End Time.";
            }
        }
        
        if (importData.getPriceTagCoverageTarget() == null || importData.getPriceTagCoverageTarget() < 0) {
            return "Inventory Coverage Stores Target cannot be null or negative.";
        }
        
        if (importData.getDummyStoreCoverage() == null || importData.getDummyStoreCoverage() < 0) {
            return "Dummy Coverage Stores Target cannot be null or negative.";
        }
        
        if (importData.getPosmStoreCoverage() == null || importData.getPosmStoreCoverage() < 0) {
            return "POSM Coverage Stores Target cannot be null or negative.";
        }
        
        if (importData.getSalesActivation() == null || importData.getSalesActivation() < 0) {
            return "Sales Activations cannot be null or negative.";
        }
        
        if (importData.getRetailEffortTarget() == null || importData.getRetailEffortTarget() < 0) {
            return "Retail SO Target cannot be null or negative.";
        }
        
        return null;
    }
    
    /**
     * 导出异常数据到Excel
     */
    private void exportErrorDataToExcel(List<NewProductTargetImportErrorData> errorDataList) {
        File tempFile = null;
        ExcelWriter excelWriter = null;
        String random = UUID.randomUUID().toString().substring(0, 8);
        try {
            tempFile = File.createTempFile("error_data_new_product_target" + random, ".xlsx");
            
            excelWriter = EasyExcel.write(tempFile, NewProductTargetImportErrorData.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("异常数据").build();
            
            excelWriter.write(errorDataList, writeSheet);
            excelWriter.finish();
            
            // 上传到FDS服务器
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            String fileUrl = fdsService.upload("error_data_error_data_new_product_target" + timestamp + ".xlsx",
                    tempFile, true).getUrl();
            log.info("新品目标异常数据已上传到FDS服务器: {}", fileUrl);
            
            // 更新异常数据文件URL
            errorDataFileUrl = fileUrl;
            
        } catch (Exception e) {
            log.error("导出异常数据失败", e);
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }
}