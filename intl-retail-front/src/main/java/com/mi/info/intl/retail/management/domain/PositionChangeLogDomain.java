package com.mi.info.intl.retail.management.domain;

import lombok.Data;
import java.io.Serializable;
import java.util.Date;

/**
 * 岗位变更日志领域对象
 */
@Data
public class PositionChangeLogDomain implements Serializable {
    private Integer id;
    private String positionName;
    private String positionCode;
    private String storeCode;
    private String rmsPositionCode;
    private Date effectiveTime;
    private String positionClass;
    private Integer workableType;
    private String positionType;
    private Integer hasPc;
    private Integer hasSr;
    private String positionStatus;
    private String changeType;
    private String changeReason;
    private Date logCreateTime;
    private Integer effectiveStatus;
} 