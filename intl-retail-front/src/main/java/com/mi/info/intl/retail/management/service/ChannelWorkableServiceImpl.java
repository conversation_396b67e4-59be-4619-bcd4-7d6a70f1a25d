package com.mi.info.intl.retail.management.service;

import com.mi.info.intl.retail.intlretail.service.api.management.ChannelWorkableService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.ChannelWorkableChangeParam;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.ChannelWorkableChangeType;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.management.constant.PositionClassEnum;
import com.mi.info.intl.retail.management.constant.PositionStatusEnum;
import com.mi.info.intl.retail.management.constant.PositionTypeEnum;
import com.mi.info.intl.retail.management.domain.StoreChangeLogDomain;
import com.mi.info.intl.retail.management.domain.repository.PositionChangeLogRepository;

import com.mi.info.intl.retail.management.domain.repository.StoreChangeLogRepository;
import com.mi.info.intl.retail.management.rpc.StorePositionMainDataRpc;
import com.xiaomi.cnzone.storems.api.model.dto.international.InternationalChannelStoreDetail;
import com.xiaomi.cnzone.storems.api.model.req.store.GetPositionInfoRequest;
import com.xiaomi.cnzone.storems.api.model.req.store.GetPositionInfoResponse;
import com.xiaomi.cnzone.storems.api.model.req.store.international.GetInternationalChannelStoreDetailRequest;
import lombok.extern.java.Log;

import java.util.Objects;

import com.mi.info.intl.retail.management.domain.PositionChangeLogDomain;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import com.mi.info.intl.retail.intlretail.service.api.management.dto.ChannelWorkableChangeResultType;

@Log
@DubboService(group = "${retail.dubbo.group:}", interfaceClass = ChannelWorkableService.class)
public class ChannelWorkableServiceImpl implements ChannelWorkableService {

    // 定义常量，提高代码可读性和可维护性
    private static final int HAS_PC_FALSE = 0;
    private static final int HAS_SR_FALSE = 0;
    private static final String POSITION_TYPE_POS = "POS";
    private static final int WORKABLE_TYPE_INACTIVE = 0;
    private static final int WORKABLE_TYPE_ACTIVE = 1;

    @Autowired
    private PositionChangeLogRepository positionChangeLogRepository;

    @Autowired
    private StoreChangeLogRepository storeChangeLogRepository;

    @Autowired
    private StorePositionMainDataRpc storePositionMainDataRpc;

    @Override
    public CommonResponse applyChannelWorkableChanges(List<ChannelWorkableChangeParam> changes) {
        // 参数校验，防止空指针异常
        if (changes == null || changes.isEmpty()) {
            log.info("Changes list is null or empty, skipping processing");
            return CommonResponse.failure(50001, "Changes list is null or empty, skipping processing");
        }

        // 对changes按照effectiveTime从小到大排序
        List<ChannelWorkableChangeParam> sortedChanges = changes.stream()
                .sorted(Comparator.comparingLong(ChannelWorkableChangeParam::getEffectiveTime))
                .collect(Collectors.toList());

        // 根据positionCode获取最近状态
        String positionCode = sortedChanges.get(0).getPositionCode();
        long effectiveTime = sortedChanges.get(0).getEffectiveTime();

        PositionChangeLogDomain lastPositionChangeLogDomain = positionChangeLogRepository.findLastedByEffectTime(positionCode, effectiveTime);
        if (Objects.isNull(lastPositionChangeLogDomain)) {
            lastPositionChangeLogDomain = createNewPositionChangeLog(positionCode);
        }
        //根据变化量changes和上一次lastPositionChangeLogDomain的状态，计算出当前的状态
        try {
            //TODO fz这个计算逻辑需要检查下
            PositionChangeLogDomain currentPositionChangeLogDomain = calculateCurrentStatus(changes, lastPositionChangeLogDomain);
            positionChangeLogRepository.save(currentPositionChangeLogDomain);

            List<PositionChangeLogDomain> oldPositionChangeList = positionChangeLogRepository.findByPositionCodeAndEffectTime(
                    currentPositionChangeLogDomain.getPositionCode(),
                    currentPositionChangeLogDomain.getEffectiveTime());
            //刷新旧数据
            PositionChangeLogDomain tagerPositionChangeLogDomain = currentPositionChangeLogDomain;
            for (PositionChangeLogDomain positionChangeLogDomain : oldPositionChangeList) {
                PositionChangeLogDomain newPositionChangeLogDomain = calculateCurrentStatus(positionChangeLogDomain.getChangeData(),
                        tagerPositionChangeLogDomain);
                positionChangeLogRepository.save(newPositionChangeLogDomain);
                tagerPositionChangeLogDomain = newPositionChangeLogDomain;
                //删除当前数据positionChangeLogDomain，更新status为0
                positionChangeLogDomain.setEffectiveStatus(0);
                positionChangeLogRepository.updateById(positionChangeLogDomain);
            }

            //刷新门店class数据

            List<PositionChangeLogDomain> allPositionChangeLogList = positionChangeLogRepository.findByStoreCode(
                    currentPositionChangeLogDomain.getStoreCode());
            List<StoreChangeLogDomain> oldStoreChangeLogList = storeChangeLogRepository.findByStoreCodeAndEffectiveTime(
                    currentPositionChangeLogDomain.getStoreCode(),
                    currentPositionChangeLogDomain.getEffectiveTime());
            StoreChangeLogDomain tagerStoreChangeLogDomain = storeChangeLogRepository.findLastedByStoreCodeAndEffectiveTime(
                    currentPositionChangeLogDomain.getStoreCode(),
                    currentPositionChangeLogDomain.getEffectiveTime());
            GetInternationalChannelStoreDetailRequest request = new GetInternationalChannelStoreDetailRequest();
            request.setOrgId(lastPositionChangeLogDomain.getStoreCode());
            InternationalChannelStoreDetail storeDetail = storePositionMainDataRpc.getStore(request);
            if (Objects.isNull(tagerStoreChangeLogDomain)) {
                tagerStoreChangeLogDomain = new StoreChangeLogDomain();
                request.setOrgId(currentPositionChangeLogDomain.getStoreCode());
                tagerStoreChangeLogDomain.setStoreName(storeDetail.getStoreInformation().getShopName());
                tagerStoreChangeLogDomain.setStoreCode(storeDetail.getStoreInformation().getOrgId());
                tagerStoreChangeLogDomain.setRmsStoreCode(storeDetail.getStoreInformation().getRmsStoreCode());
            }
            // 留下 effectiveTime <= 当前时间的数据，并按 positionCode 分组，每组按 effectiveTime 从大到小排序
            tagerStoreChangeLogDomain = buildStoreChangeLog(currentPositionChangeLogDomain, allPositionChangeLogList,
                    tagerStoreChangeLogDomain, storeDetail, oldStoreChangeLogList);

            List<PositionChangeLogDomain> futurePositionChangeLogList = allPositionChangeLogList.stream()
                    .filter(log -> log.getEffectiveTime() != null && log.getEffectiveTime().compareTo(currentPositionChangeLogDomain.getEffectiveTime()) > 0)
                    .collect(Collectors.toList());

            for (PositionChangeLogDomain positionChangeLogDomain : futurePositionChangeLogList) {
                tagerStoreChangeLogDomain = buildStoreChangeLog(positionChangeLogDomain, allPositionChangeLogList,
                        tagerStoreChangeLogDomain, storeDetail, oldStoreChangeLogList);
            }

        } catch (Exception e) {
            log.info("No changes detected: " + e.getMessage());
            return CommonResponse.failure(50002, "No changes detected: " + e.getMessage());
        }
        return new CommonResponse("success");
    }

    private StoreChangeLogDomain buildStoreChangeLog(PositionChangeLogDomain currentPositionChangeLogDomain,
                                                     List<PositionChangeLogDomain> allPositionChangeLogList,
                                                     StoreChangeLogDomain tagerStoreChangeLogDomain, InternationalChannelStoreDetail storeDetail,
                                                     List<StoreChangeLogDomain> oldStoreChangeLogList) {
        if (Objects.isNull(tagerStoreChangeLogDomain)) {
            return null;
        }
        Map<String, List<PositionChangeLogDomain>> filteredAndGrouped = allPositionChangeLogList.stream()
                .filter(log -> log.getEffectiveTime() != null && log.getEffectiveTime().compareTo(
                        currentPositionChangeLogDomain.getEffectiveTime()) <= 0) // effectiveTime <= 当前时间
                .collect(Collectors.groupingBy(
                        PositionChangeLogDomain::getPositionCode, // 按 positionCode 分组
                        Collectors.collectingAndThen(
                                Collectors.toList(),
                                list -> list.stream()
                                        .sorted(Comparator.comparing(PositionChangeLogDomain::getEffectiveTime).reversed()) // 每组按 effectiveTime 从大到小排序
                                        .collect(Collectors.toList())
                        )
                ));

        // 然后从每组中提取第一条记录
        List<PositionChangeLogDomain> firstOfEachGroup = filteredAndGrouped.values().stream()
                .filter(group -> !group.isEmpty())
                .map(group -> group.get(0))
                .collect(Collectors.toList());

        //与最新一条进行对比，判断是否需要新增数据
        Integer hasFront = firstOfEachGroup.stream()
                .filter(position -> !Objects.equals(position.getPositionStatus(), PositionStatusEnum.CLOSED.getKey()))
                .map(PositionChangeLogDomain::getPositionType)
                .filter(Objects::nonNull)
                .anyMatch(type -> !Objects.equals(type, PositionTypeEnum.POS.getKey())) ? 1 : 0;
        Integer hasPos = firstOfEachGroup.stream()
                .filter(position -> !Objects.equals(position.getPositionStatus(), PositionStatusEnum.CLOSED.getKey()))
                .map(PositionChangeLogDomain::getPositionType)
                .filter(Objects::nonNull)
                .anyMatch(PositionTypeEnum.POS.getKey()::equals) ? 1 : 0;
        Integer hasPc = firstOfEachGroup.stream()
                .filter(position -> !Objects.equals(position.getPositionStatus(), PositionStatusEnum.CLOSED.getKey()))
                .map(PositionChangeLogDomain::getHasPc)
                .filter(Objects::nonNull)
                .anyMatch(pc -> Objects.equals(pc, 1)) ? 1 : 0;
        Integer hasSr = firstOfEachGroup.stream()
                .filter(position -> !Objects.equals(position.getPositionStatus(), PositionStatusEnum.CLOSED.getKey()))
                .map(PositionChangeLogDomain::getHasSr)
                .filter(Objects::nonNull)
                .anyMatch(sr -> Objects.equals(sr, 1)) ? 1 : 0;
        PositionClassEnum newStoreClassEnum = getStoreClass(firstOfEachGroup);
        if (!Objects.equals(hasFront, tagerStoreChangeLogDomain.getHasFront()) || !Objects.equals(hasPos, tagerStoreChangeLogDomain.getHasPos())
                || !Objects.equals(hasPc, tagerStoreChangeLogDomain.getHasPc()) || !Objects.equals(hasSr, tagerStoreChangeLogDomain.getHasSr())
                || !Objects.equals(newStoreClassEnum.getLevel(), tagerStoreChangeLogDomain.getStoreClass())) {
            StoreChangeLogDomain newStoreChangeLogDomain = new StoreChangeLogDomain();
            //TODO fz赋值
            newStoreChangeLogDomain.setStoreCode(tagerStoreChangeLogDomain.getStoreCode());
            newStoreChangeLogDomain.setStoreName(tagerStoreChangeLogDomain.getStoreName());
            //TODO fz赋值
            newStoreChangeLogDomain.setPositionChangeId(allPositionChangeLogList.get(0).getId());
            newStoreChangeLogDomain.setEffectiveTime(currentPositionChangeLogDomain.getEffectiveTime());
            newStoreChangeLogDomain.setStoreClass(newStoreClassEnum.getLevel());
            newStoreChangeLogDomain.setHasFront(hasFront);
            newStoreChangeLogDomain.setHasPos(hasPos);
            newStoreChangeLogDomain.setHasPc(hasPc);
            newStoreChangeLogDomain.setHasSr(hasSr);
            newStoreChangeLogDomain.setEffectiveStatus(1);
            newStoreChangeLogDomain.setRecordCreateTime(new Date());
            newStoreChangeLogDomain.setWorkableType(1);
            //TODO fz待定赋值
            newStoreChangeLogDomain.setRmsStoreCode(tagerStoreChangeLogDomain.getRmsStoreCode());
            newStoreChangeLogDomain.setStoreType(tagerStoreChangeLogDomain.getStoreType());
            //TODO fz待定赋值
            newStoreChangeLogDomain.setStoreStatus("OPEN");
            newStoreChangeLogDomain.setStoreGrade(tagerStoreChangeLogDomain.getStoreGrade());
            newStoreChangeLogDomain.setStoreChannelType(tagerStoreChangeLogDomain.getStoreChannelType());
            newStoreChangeLogDomain.setRemainingXiaomiStore(tagerStoreChangeLogDomain.getRemainingXiaomiStore());
            tagerStoreChangeLogDomain = newStoreChangeLogDomain;
            // 筛选出effectiveTime大于当前时间的数据
            List<Integer> idList = oldStoreChangeLogList.stream()
                    .filter(log -> log.getEffectiveTime() != null && log.getEffectiveTime().compareTo(currentPositionChangeLogDomain.getEffectiveTime()) >= 0)
                    .map(StoreChangeLogDomain::getId).collect(Collectors.toList());
            storeChangeLogRepository.batchDelete(idList);
            storeChangeLogRepository.save(newStoreChangeLogDomain);
        }
        return tagerStoreChangeLogDomain;
    }

    private PositionClassEnum getStoreClass(List<PositionChangeLogDomain> positionChangeLogDomains) {
        List<Integer> positionClassList = positionChangeLogDomains.stream().map(PositionChangeLogDomain::getPositionClass).collect(Collectors.toList());

        // 1. 将字符串转换为 PositionClassEnum 列表
        List<PositionClassEnum> positionClassEnums = positionClassList.stream()
                .map(PositionClassEnum::fromLevel)  // 使用 fromKey 方法将字符串转换为枚举
                .filter(Objects::nonNull)         // 过滤掉无法转换的无效值
                .collect(Collectors.toList());

        // 2. 获取 level 最高的枚举（假设 level 值越大表示优先级越高）
        Optional<PositionClassEnum> highestLevelEnum = positionClassEnums.stream()
                .max(Comparator.comparingInt(PositionClassEnum::getLevel));  // 根据 level 排序并取最大值
        // 3. 如果需要获取对应的枚举实例
        PositionClassEnum result = highestLevelEnum.orElse(null);  // 或者提供默认值
        return result;
    }

    private Integer getPositionClass(PositionChangeLogDomain positionChangeLogDomain) {
        if (positionChangeLogDomain == null) {
            return PositionClassEnum.EMPTY.getLevel();
        }

        // 构建 key: [Front/POS][PC][SR][POS]
        String key = (Objects.equals(positionChangeLogDomain.getPositionType(), PositionTypeEnum.POS.getKey()) ? "N" : "Y") +
                (Objects.equals(positionChangeLogDomain.getHasPc(), 1) ? "Y" : "N") +
                (Objects.equals(positionChangeLogDomain.getHasSr(), 1) ? "Y" : "N") +
                (Objects.equals(positionChangeLogDomain.getPositionType(), PositionTypeEnum.POS.getKey()) ? "Y" : "N");

        PositionClassEnum positionClassEnum = PositionClassEnum.fromKey(key);
        return positionClassEnum != null ? positionClassEnum.getLevel() : PositionClassEnum.EMPTY.getLevel();
    }


    /**
     * 根据变化量changes和上一次lastPositionChangeLogDomain的状态，计算出当前的状态
     */
    private PositionChangeLogDomain calculateCurrentStatus(List<ChannelWorkableChangeParam> changes,
                                                           PositionChangeLogDomain lastPositionChangeLogDomain) throws CheckedException {
        PositionChangeLogDomain result = lastPositionChangeLogDomain == null ? new PositionChangeLogDomain() : cloneDomain(lastPositionChangeLogDomain);
        if (changes != null) {
            for (ChannelWorkableChangeParam change : changes) {
                // 空值检查，防止空指针异常
                if (change == null || change.getChangeType() == null) {
                    log.info("Skip null change or change with null changeType");
                    continue;
                }

                switch (change.getChangeType()) {
                    case "HAS_PC":
                        result.setHasPc(ChannelWorkableChangeResultType.TRUE.name().equals(change.getChangeResult()) ? 1 : 0);
                        break;
                    case "HAS_SR":
                        result.setHasSr(ChannelWorkableChangeResultType.TRUE.name().equals(change.getChangeResult()) ? 1 : 0);
                        break;
                    case "POSITION_TYPE":
                        result.setPositionType(PositionTypeEnum.getKeyByValue(change.getChangeResult()));
                        break;
                    case "POSITION_STATUS":
                        result.setPositionStatus(PositionStatusEnum.getKeyByValue(change.getChangeResult()));
                        break;
                    default:
                        throw new CheckedException("Invalid change type: " + change.getChangeType());
                        // 可根据实际业务扩展更多case
                }
                // 更新时间为本次变更的effectiveTime
                if (change.getEffectiveTime() != null) {
                    result.setEffectiveTime(change.getEffectiveTime());
                }
            }
            // 只有HasPs和HasSr为false且PositionType为POS的时候，workableType为0
            if (isPositionInactive(result)) {
                result.setWorkableType(WORKABLE_TYPE_INACTIVE);
            } else {
                result.setWorkableType(WORKABLE_TYPE_ACTIVE);
            }
            //lastPositionChangeLogDomain和result比较，如果原始值和有变化才返回，否则直接返回null,并抛出一个检查异常
            if (lastPositionChangeLogDomain.getId() != null && !hasChanges(lastPositionChangeLogDomain, result)) {
                throw new CheckedException("没有变化");
            }
            if (Objects.equals(PositionStatusEnum.CLOSED.getKey(), result.getPositionStatus())) {
                result.setPositionClass(PositionClassEnum.EMPTY.getLevel());
            } else {
                result.setPositionClass(getPositionClass(result));
            }
            result.setEffectiveStatus(1);
        }
        //上一条有数据，并且时间不同，插入而非更新，时间相同，直接更新
        if (Objects.nonNull(result.getId()) && Objects.nonNull(lastPositionChangeLogDomain) &&
                !Objects.equals(lastPositionChangeLogDomain.getEffectiveTime(), result.getEffectiveTime())) {
            result.setId(null);
        }
        result.setChangeData(changes);
        return result;
    }

    /**
     * 检查两个对象是否有变化
     */
    private boolean hasChanges(PositionChangeLogDomain old, PositionChangeLogDomain current) {
        return !Objects.equals(old.getHasPc(), current.getHasPc()) ||
                !Objects.equals(old.getHasSr(), current.getHasSr()) ||
                !Objects.equals(old.getPositionType(), current.getPositionType()) ||
                !Objects.equals(old.getPositionStatus(), current.getPositionStatus());
    }

    /**
     * 创建新的PositionChangeLogDomain对象
     */
    private PositionChangeLogDomain createNewPositionChangeLog(String positionCode) {
        GetPositionInfoRequest request = new GetPositionInfoRequest();
        request.setPositionCode(positionCode);
        GetPositionInfoResponse positionInfoRsp = storePositionMainDataRpc.getPosition(request);
        if (Objects.isNull(positionInfoRsp)) {
            return null;
        }

        PositionChangeLogDomain newDomain = new PositionChangeLogDomain();
        newDomain.setPositionName(positionInfoRsp.getPositionName());
        newDomain.setPositionCode(positionInfoRsp.getPositionCode());
        newDomain.setStoreCode(positionInfoRsp.getOrgId());
        return newDomain;
    }

    /**
     * 简单克隆domain对象（只复制字段，不复制引用）
     */
    private PositionChangeLogDomain cloneDomain(PositionChangeLogDomain src) {
        if (src == null) return null;
        PositionChangeLogDomain dest = new PositionChangeLogDomain();
        dest.setId(src.getId());
        dest.setPositionName(src.getPositionName());
        dest.setPositionCode(src.getPositionCode());
        dest.setStoreCode(src.getStoreCode());
        dest.setRmsPositionCode(src.getRmsPositionCode());
        dest.setEffectiveTime(src.getEffectiveTime());
        dest.setPositionClass(src.getPositionClass());
        dest.setWorkableType(src.getWorkableType());
        dest.setPositionType(src.getPositionType());
        dest.setHasPc(src.getHasPc());
        dest.setHasSr(src.getHasSr());
        dest.setPositionStatus(src.getPositionStatus());
        dest.setChangeType(src.getChangeType());
        dest.setChangeReason(src.getChangeReason());
        dest.setLogCreateTime(src.getLogCreateTime());
        dest.setEffectiveStatus(src.getEffectiveStatus());
        return dest;
    }


    /**
     * 判断位置是否为非活跃状态
     * 当HasPs和HasSr都为false且PositionType为POS时，位置为非活跃状态
     *
     * @param position 位置信息
     * @return true表示位置为非活跃状态
     */
    private boolean isPositionInactive(PositionChangeLogDomain position) {
        // 空值检查，防止空指针异常
        if (position == null) {
            return false;
        }

        // 只有当HasPc和HasSr都不为null且都为0，且PositionType为"POS"时，才返回true
        return Objects.equals(HAS_PC_FALSE, position.getHasPc())
                && Objects.equals(HAS_SR_FALSE, position.getHasSr())
                && PositionTypeEnum.POS.getKey().equals(position.getPositionType());
    }
}
