package com.mi.info.intl.retail.management.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.intlretail.service.api.bpm.BpmService;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.ApprovalTaskListResp;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeRuleService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveHistoryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveHistoryResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.CommonApproveReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.DetailsBelowResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.DetailsTopmostResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.FileTemplateReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RetailerQueryResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleModificationLog;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleModifyVersionResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeExportReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleDetailResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleFormDataReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRulePageQueryReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRulePageResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleRecallReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.StoreGradeRuleReq;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.SubmitResp;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.UploadManuallyRuleDetailsResp;
import com.mi.info.intl.retail.intlretail.service.api.management.StoreGradeService;
import com.mi.info.intl.retail.intlretail.service.api.management.dto.*;
import com.mi.info.intl.retail.management.config.StoreGradeRuleConfig;
import com.mi.info.intl.retail.management.constant.CommonConstant;
import com.mi.info.intl.retail.management.constant.NrJobConst;
import com.mi.info.intl.retail.management.constant.RouteConstants;
import com.mi.info.intl.retail.management.dto.PicCommitParam;
import com.mi.info.intl.retail.management.dto.StoreGradeBatchData;
import com.mi.info.intl.retail.management.dto.StoreGradeExprot;
import com.mi.info.intl.retail.management.entity.CommonApproveLog;
import com.mi.info.intl.retail.management.entity.StoreGradeRelation;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.management.mapper.CommonApproveLogMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.rpc.FileRemoteRpc;
import com.mi.info.intl.retail.management.rpc.MainDataRpc;
import com.mi.info.intl.retail.management.service.CommonGatewayService;
import com.mi.info.intl.retail.management.service.enums.ApprovalBusinessKeyEnum;
import com.mi.info.intl.retail.management.service.enums.ApprovalStatus;
import com.mi.info.intl.retail.management.service.enums.ChannelTypeEnum;
import com.mi.info.intl.retail.management.service.enums.FileTemplateEnum;
import com.mi.info.intl.retail.management.service.enums.RuleStatusEnum;
import com.mi.info.intl.retail.management.service.enums.StoreGradeEnum;
import com.mi.info.intl.retail.management.service.enums.SubmitTypeEnum;
import com.mi.info.intl.retail.management.service.enums.TieringModificationMethodEnum;
import com.mi.info.intl.retail.model.BaseCountryEntity;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsStore;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsStoreMapper;
import com.mi.info.intl.retail.retailer.entity.IntlRmsRetailer;
import com.mi.info.intl.retail.retailer.mapper.IntlRmsRetailerMapper;
import com.mi.info.intl.retail.utils.JsonUtils;
import com.mi.info.intl.retail.utils.RpcContextUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import com.xiaomi.cnzone.commons.utils.StringUtils;
import com.xiaomi.cnzone.maindataapi.model.OrgResponse;
import com.xiaomi.cnzone.maindataapi.model.enums.DomainEnum;
import com.xiaomi.cnzone.maindataapi.model.req.org.OrgExtension;
import com.xiaomi.cnzone.storeapi.api.channelbuild.common.ChannelCommonProvider;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.req.CountryRoleAdminReq;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.resp.CountryRoleAdminResp;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import com.xiaomi.cnzone.storems.common.exception.ErrorCodeEnums;
import com.xiaomi.nr.eiam.admin.dto.provider.user.SearchUserSensitiveInfoRequest;
import com.xiaomi.nr.eiam.admin.provider.UserAdminProvider;
import com.xiaomi.nr.eiam.admin.vo.provider.user.UserSensitiveInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetParentOrganPositionUserReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import com.xiaomi.nr.eiam.common.enums.PositionEnum;
import com.xiaomi.nr.job.admin.dto.TriggerJobRequestDTO;
import com.xiaomi.nr.job.admin.service.NrJobService;
import com.xiaomi.nr.job.core.biz.model.HandleMsg;
import com.xiaomi.nr.job.core.context.JobHelper;
import com.xiaomi.nr.job.core.handler.annotation.NrJob;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang.time.DateFormatUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.config.annotation.Reference;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 门店等级规则服务实现类
 */

@DubboService(group = "${retail.dubbo.group:}", interfaceClass = StoreGradeRuleService.class)
@Slf4j
@Service
public class StoreGradeRuleServiceImpl extends ServiceImpl<StoreGradeRuleMapper, StoreGradeRule>
        implements StoreGradeRuleService {

    private static final Integer LOCK_TIMEOUT = 10;

    @Resource
    private CommonApproveLogMapper commonApproveLogMapper;

    @Resource
    private IntlRmsRetailerMapper intlRmsRetailerMapper;

    @Resource
    private IntlRmsStoreMapper intlRmsStoreMapper;

    @Resource
    private BpmService bpmService;

    @Resource
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @Resource
    private FdsService fdsService;

    @Resource
    private MainDataRpc mainDataRpc;

    @Resource
    private FileRemoteRpc fileRemoteRpc;

    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Reference(group = "${store.dubbo.group:}", check = false, timeout = 20000, retries = 0)
    private ChannelCommonProvider channelCommonProvider;

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserProvider.class, check = false,
            timeout = 5000)
    private UserProvider userProvider;

    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserAdminProvider.class, check = false,
            timeout = 5000)
    private UserAdminProvider userAdminProvider;

    @Reference(group = "${nr.dubbo.group}", check = false, interfaceClass = NrJobService.class)
    private NrJobService nrJobService;

    @Value("${proretail.project.id:11}")
    private Long projectId;

    @Resource
    private StoreGradeRuleConfig storeGradeRuleConfig;

    @Resource
    private StoreGradeMapper storeGradeMapper;

    @Resource
    private TransactionAspectSupport transactionAspectSupport;

    @Resource
    private CommonGatewayService commonGatewayService;

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Integer> saveStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq) {
        boolean lockAcquired = false;
        log.info("Start saving store grade rule, request parameters: {}", storeGradeRuleReq);

        // 验证零售商列表
        if (Objects.isNull(storeGradeRuleReq) || (Objects.equals(SubmitTypeEnum.SUBMIT.getType(), storeGradeRuleReq.getSubmitType())
                && !Objects.equals(ChannelTypeEnum.IR.getKey(), storeGradeRuleReq.getChannelType())
                && Objects.isNull(storeGradeRuleReq.getRetailerCode()))) {
            log.error("param err");
            throw new BusinessException("param err");
        }
        String lockKey = CommonConstant.STORE_RULE_LOCK_TOKEN_KEY + "_" + storeGradeRuleReq.getChannelType() + "_"
                + storeGradeRuleReq.getRetailerCode() + "_" + storeGradeRuleReq.getCountryCode();
        try {
            lockAcquired = commonGatewayService.getCommonLockByHolding(lockKey, LOCK_TIMEOUT);
            if (!lockAcquired) {
                throw new BusinessException("Please do not resubmit.");
            }

            //获取操作人
            String user = Optional.ofNullable(RpcContextUtil.getCurrentAccount()).orElseThrow(() -> new BusinessException("未查询到用户信息"));

            StoreGradeRule oldStoreGradeRule = checkReSubmit(storeGradeRuleReq);

            boolean submitFlag = SubmitTypeEnum.SUBMIT.getType().equals(storeGradeRuleReq.getSubmitType());

            StoreGradeRule storeGradeRule = buildStoreGradeRule(storeGradeRuleReq, user, oldStoreGradeRule, submitFlag);
            if (Objects.nonNull(storeGradeRuleReq.getFileData())) {
                PicCommitParam param = new PicCommitParam();
                param.setIds(Arrays.asList(storeGradeRuleReq.getFileData().getId()));
                fileRemoteRpc.fileCommit(param);
            }
            IntlRmsCountryTimezone country = getCountryByCode(storeGradeRuleReq.getCountryCode());
            if (Objects.nonNull(country)) {
                storeGradeRuleReq.setCountryName(country.getCountryName());
            }

            //TODO 判断是否是第一次提交
            if (Objects.isNull(storeGradeRule.getId())) {
                this.save(storeGradeRule);
            } else if (Objects.nonNull(oldStoreGradeRule) && !Objects.equals(RuleStatusEnum.IN_EFFECT.getCode(), oldStoreGradeRule.getRulesStatus())) {
                this.updateById(storeGradeRule);
            } else {
                // 更新审批状态
                StoreGradeRule tagerStoreGradeRule = new StoreGradeRule();
                tagerStoreGradeRule.setId(storeGradeRule.getId());
                tagerStoreGradeRule.setApproveStatus(storeGradeRule.getApproveStatus());
                tagerStoreGradeRule.setUpdateBy(storeGradeRule.getUpdateBy());
                this.updateById(tagerStoreGradeRule);
            }
            //storeGradeRules.add(storeGradeRule);
            CommonApproveLog commonApproveLog = buildCommonApproveLog(storeGradeRuleReq, user, oldStoreGradeRule, storeGradeRule);
            if (Objects.nonNull(commonApproveLog.getFlowInstId())) {
                storeGradeRuleReq.setFlowInstId(commonApproveLog.getFlowInstId());
            }
            if (submitFlag) {
                commonApproveLog.setFlowStatus(ApprovalStatus.IN_APPROVAL.getCode());
                //TODO fz调用审批接口
                storeGradeRuleReq.setId(storeGradeRule.getId());
                SubmitResp submitResp = null;
                if (Objects.equals(TieringModificationMethodEnum.MANUAL_UPLOAD.getKey(), storeGradeRuleReq.getModificationMethod())) {
                    //TODO fz手动上传特殊处理
                    submitResp = batchUpdateStoreGrade(storeGradeRuleReq);
                } else if (Objects.equals(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey(), storeGradeRuleReq.getModificationMethod())) {
                    submitResp = submit(storeGradeRuleReq);
                }
                if (Objects.isNull(submitResp.getFlowInstId())) {
                    log.error("Failed to submit store grade rule approval");
                    throw new BusinessException("Failed to submit store grade rule");
                }
                commonApproveLog.setFlowInstId(submitResp.getFlowInstId());
            } else {
                commonApproveLog.setFlowStatus(ApprovalStatus.DRAFT.getCode());
            }
            if (Objects.isNull(commonApproveLog.getId())) {
                commonApproveLogMapper.insert(commonApproveLog);
            } else {
                commonApproveLogMapper.updateById(commonApproveLog);
            }

            return new CommonApiResponse<>(storeGradeRule.getId());

        } catch (Exception e) {
            log.error("Failed to save store grade rule", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return CommonApiResponse.failure(500, e.getMessage());
        } finally {
            // 只有在成功获取锁的情况下才释放锁
            if (lockAcquired) {
                commonGatewayService.expireCommonLock(lockKey);
                log.info("Released distributed lock: {}", lockKey);
            }
        }
    }

    @NotNull
    private CommonApproveLog buildCommonApproveLog(StoreGradeRuleReq storeGradeRuleReq, String user,
                                                   StoreGradeRule oldStoreGradeRule, StoreGradeRule storeGradeRule) {
        CommonApproveLog commonApproveLog = null;
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        if (storeGradeRule.getId() != null) {
            queryWrapper.eq(CommonApproveLog::getBusinessId, storeGradeRule.getId());
        }
        if (storeGradeRuleReq.getRuleLogId() != null) {
            queryWrapper.eq(CommonApproveLog::getId, storeGradeRuleReq.getRuleLogId());
        }
        queryWrapper.eq(CommonApproveLog::getFlowStatus, ApprovalStatus.DRAFT.getCode());
        queryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                .orderByDesc(CommonApproveLog::getId);
        List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(queryWrapper);
        if (!CollectionUtils.isEmpty(commonApproveLogs)) {
            commonApproveLog = commonApproveLogs.get(0);
        } else {
            commonApproveLog = new CommonApproveLog();
        }

        if (Objects.nonNull(commonApproveLog.getId())) {
            commonApproveLog.setTargetBody(JsonUtils.toStr(storeGradeRule));
        } else {
            commonApproveLog.setBusinessKey(ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue());
            commonApproveLog.setBusinessId(storeGradeRule.getId().toString());
            commonApproveLog.setCreatedAt(LocalDateTime.now());
            commonApproveLog.setUpdatedAt(LocalDateTime.now());
            commonApproveLog.setApplicationTime(LocalDateTime.now());
            commonApproveLog.setTargetBody(JsonUtils.toStr(storeGradeRule));
            if (Objects.nonNull(oldStoreGradeRule)) {
                commonApproveLog.setOriginBody(JsonUtils.toStr(oldStoreGradeRule));
            }
        }
        commonApproveLog.setCreatedBy(user);
        return commonApproveLog;
    }

    @NotNull
    private StoreGradeRule buildStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq, String user, StoreGradeRule oldStoreGradeRule, boolean submitFlag) {
        StoreGradeRule storeGradeRule = new StoreGradeRule();
        // 复制基本属性
        BeanUtils.copyProperties(storeGradeRuleReq, storeGradeRule);

        storeGradeRule.setMethod(storeGradeRuleReq.getModificationMethod());
        // 设置零售商信息
        if (Objects.nonNull(storeGradeRuleReq.getRetailerCode())) {
            LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
            retailerQueryWrapper.eq(IntlRmsRetailer::getName, storeGradeRuleReq.getRetailerCode());
            List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
            if (CollectionUtils.isEmpty(intlRmsRetailers)) {
                log.error("Retailer information is empty");
                throw new BusinessException("Retailer information is empty");
            }
            storeGradeRuleReq.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
            StoreGradeRuleReq.Retailer retailer = new StoreGradeRuleReq.Retailer();
            retailer.setName(intlRmsRetailers.get(0).getRetailerName());
            retailer.setCode(storeGradeRuleReq.getRetailerCode());
            storeGradeRuleReq.setRetailer(retailer);
            storeGradeRule.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
            storeGradeRule.setRetailerCode(storeGradeRuleReq.getRetailerCode());
        }

        if (Objects.isNull(oldStoreGradeRule) || !Objects.equals(RuleStatusEnum.IN_EFFECT.getCode(), oldStoreGradeRule.getRulesStatus())) {
            storeGradeRule.setRulesStatus(RuleStatusEnum.NOT_IN_EFFECT.getCode());
        }
        if (submitFlag) {
            // 设置规则状态为待审核
            storeGradeRule.setApplicationTime(LocalDateTime.now());
            storeGradeRule.setApplicationTime(LocalDateTime.now());
            storeGradeRule.setApproveStatus(ApprovalStatus.IN_APPROVAL.getCode());
        } else {
            // 待保存状态
            //TODO fz审批状态是否需要待提交
            storeGradeRule.setApproveStatus(ApprovalStatus.DRAFT.getCode());
        }
        storeGradeRule.setCreateBy(user);
        storeGradeRule.setUpdateBy(user);
        if (Objects.nonNull(storeGradeRuleReq.getFileData())) {
            storeGradeRule.setFileData(JsonUtils.toStr(storeGradeRuleReq.getFileData()));
        }
        return storeGradeRule;
    }

    private void fillRetailInfo(StoreGradeRuleReq storeGradeRuleReq, StoreGradeRule storeGradeRule) {
        LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
        retailerQueryWrapper.eq(IntlRmsRetailer::getName, storeGradeRuleReq.getRetailerCode());
        List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
        if (CollectionUtils.isEmpty(intlRmsRetailers)) {
            log.error("Retailer information is empty");
            throw new BusinessException("Retailer information is empty");
        }
        storeGradeRuleReq.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
        StoreGradeRuleReq.Retailer retailer = new StoreGradeRuleReq.Retailer();
        retailer.setName(intlRmsRetailers.get(0).getRetailerName());
        retailer.setCode(storeGradeRuleReq.getRetailerCode());
        storeGradeRuleReq.setRetailer(retailer);
        storeGradeRule.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
        storeGradeRule.setRetailerCode(storeGradeRuleReq.getRetailerCode());
    }

    @Nullable
    private StoreGradeRule checkReSubmit(StoreGradeRuleReq storeGradeRuleReq) {
        StoreGradeRule oldStoreGradeRule = null;
        if (Objects.equals(ChannelTypeEnum.IR.getKey(), storeGradeRuleReq.getChannelType())) {
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getCountryCode, storeGradeRuleReq.getCountryCode())
                    .eq(StoreGradeRule::getChannelType, storeGradeRuleReq.getChannelType())
                    .eq(BaseCountryEntity::getIsDeleted, 0);
            if (Objects.nonNull(storeGradeRuleReq.getId())) {
                queryWrapper.ne(StoreGradeRule::getId, storeGradeRuleReq.getId());
            }

            StoreGradeRule existingRule = this.getOne(queryWrapper);
            if (existingRule != null) {
                log.warn("IR rule already exists for current country {}, skipping save", storeGradeRuleReq.getCountryCode());
                throw new BusinessException("IR rule already exists for current country");
            }
        } else if (Objects.nonNull(storeGradeRuleReq.getChannelType()) && Objects.nonNull(storeGradeRuleReq.getRetailerCode())) {
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getRetailerCode, storeGradeRuleReq.getRetailerCode())
                    .eq(StoreGradeRule::getChannelType, storeGradeRuleReq.getChannelType())
                    .eq(BaseCountryEntity::getIsDeleted, 0);
            if (Objects.nonNull(storeGradeRuleReq.getId())) {
                queryWrapper.ne(StoreGradeRule::getId, storeGradeRuleReq.getId());
            }

            StoreGradeRule existingRule = this.getOne(queryWrapper);
            if (existingRule != null) {
                log.warn("Rule already exists for retailer code {}, skipping save", storeGradeRuleReq.getRetailerCode());
                throw new BusinessException("Rule already exists for retailer code");
            }
        }
        if (Objects.nonNull(storeGradeRuleReq.getId())) {
            oldStoreGradeRule = this.getById(storeGradeRuleReq.getId());
        }
        if (Objects.nonNull(oldStoreGradeRule) && Objects.equals(oldStoreGradeRule.getApproveStatus(), ApprovalStatus.IN_APPROVAL.getCode())) {
            log.warn("The current rule is under approval, please do not add duplicate");
            throw new BusinessException("The current rule is under approval, please do not add duplicate");
        }
        if (Objects.nonNull(oldStoreGradeRule) && Objects.equals(oldStoreGradeRule.getRulesStatus(), RuleStatusEnum.IN_EFFECT.getCode())) {
            if (Objects.equals(oldStoreGradeRule.getMethod(), TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey()) &&
                    Objects.equals(storeGradeRuleReq.getModificationMethod(), TieringModificationMethodEnum.MANUAL_UPLOAD.getKey())) {
                log.error("Cannot modify from system calculation to manual upload");
                throw new BusinessException("Cannot modify from system calculation to manual upload");
            }
        }
        return oldStoreGradeRule;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Boolean> updateStoreGradeRule(StoreGradeRuleReq storeGradeRuleReq) {
        try {
            log.info("Start updating store grade rule, request parameters: {}", storeGradeRuleReq);

            if (storeGradeRuleReq == null || storeGradeRuleReq.getId() == null) {
                log.error("Store grade rule request object is empty or ID is empty");
                throw new RuntimeException("Store grade rule request object is empty or ID is empty");
            }

            // 查询现有记录
            StoreGradeRule existingRule = this.getById(storeGradeRuleReq.getId());
            if (existingRule == null) {
                log.error("Store grade rule with ID {} not found", storeGradeRuleReq.getId());
                throw new RuntimeException("Store grade rule with ID " + storeGradeRuleReq.getId() + " not found");
            }
            StoreGradeRule newStoreGradeRule = new StoreGradeRule();

            // 更新基本属性
            BeanUtils.copyProperties(storeGradeRuleReq, newStoreGradeRule);
            newStoreGradeRule.setId(existingRule.getId());
            newStoreGradeRule.setApplicationTime(existingRule.getApplicationTime());
            newStoreGradeRule.setRulesStatus(0);
            //newStoreGradeRule.setLastCalculationTime(existingRule.getLastCalculationTime());
            newStoreGradeRule.setApproveStatus(0);
            // 如果有零售商信息，更新第一个零售商的信息
            if (Objects.nonNull(storeGradeRuleReq.getRetailerCode())) {
                LambdaQueryWrapper<IntlRmsRetailer> retailerQueryWrapper = Wrappers.lambdaQuery();
                retailerQueryWrapper.eq(IntlRmsRetailer::getCrmCode, storeGradeRuleReq.getRetailerCode());
                List<IntlRmsRetailer> intlRmsRetailers = intlRmsRetailerMapper.selectList(retailerQueryWrapper);
                if (CollectionUtils.isEmpty(intlRmsRetailers)) {
                    log.error("Retailer information is empty");
                    throw new RuntimeException("Retailer information is empty");
                }
                newStoreGradeRule.setRetailerName(intlRmsRetailers.get(0).getRetailerName());
                newStoreGradeRule.setRetailerCode(storeGradeRuleReq.getRetailerCode());
            }

            // 设置更新时间
            existingRule.setLastCalculationTime(LocalDateTime.now());
            CommonApproveLog commonApproveLog = new CommonApproveLog();
            commonApproveLog.setBusinessKey(ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue());
            commonApproveLog.setBusinessId(existingRule.getId().toString());
            commonApproveLog.setFlowStatus(0);
            commonApproveLog.setCreatedAt(LocalDateTime.now());
            commonApproveLog.setUpdatedAt(LocalDateTime.now());
            commonApproveLog.setApplicationTime(LocalDateTime.now());
            commonApproveLog.setOriginBody(JsonUtils.toStr(existingRule));
            commonApproveLog.setTargetBody(JsonUtils.toStr(newStoreGradeRule));
            //TODO fz调用审批接口
            commonApproveLog.setFlowInstId("");
            commonApproveLogMapper.insert(commonApproveLog);

            // 更新记录
            existingRule.setRulesStatus(0);
            existingRule.setApproveStatus(0);
            boolean result = this.updateById(existingRule);

            log.info("Store grade rule update completed, updated record ID: {}", storeGradeRuleReq.getId());
            return new CommonApiResponse<>(result);

        } catch (Exception e) {
            log.error("Failed to update store grade rule", e);
            throw e;
        }
    }

    @Override
    public CommonApiResponse<Integer> check(StoreGradeRuleReq storeGradeRuleReq) {
        if (Objects.isNull(storeGradeRuleReq) || Objects.isNull(storeGradeRuleReq.getRetailerCode())) {
            return CommonApiResponse.failure(500, "Retailer code cannot be empty");
        }
        LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(StoreGradeRule::getRetailerCode, storeGradeRuleReq.getRetailerCode())
                .eq(BaseCountryEntity::getIsDeleted, 0);

        List<StoreGradeRule> ruleList = this.list(queryWrapper);
        return new CommonApiResponse<>(CollectionUtils.isEmpty(ruleList) ? 1 : 2);
    }

    @Override
    public CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> query(RuleQueryReq request) {
        try {
            if (request == null || (request.getRuleId() == null && request.getRuleLogId() == null)) {
                return null;
            }
            LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
            if (request.getRuleId() != null) {
                queryWrapper.eq(CommonApproveLog::getBusinessId, request.getRuleId());
            }
            if (request.getRuleLogId() != null) {
                queryWrapper.eq(CommonApproveLog::getId, request.getRuleLogId());
            }
            queryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                    .orderByDesc(CommonApproveLog::getId);
            List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(queryWrapper);
            if (CollectionUtils.isEmpty(commonApproveLogs)) {
                return null;
            }
            StoreGradeRule storeGradeRule = JsonUtils.toObject(commonApproveLogs.get(0).getTargetBody(), StoreGradeRule.class);
            StoreGradeRuleDetailResp.RuleDetails ruleDetails = buildRuleDetails(storeGradeRule);
            //BeanUtils.copyProperties(storeGradeRule, ruleDetails);
            //TODO fz填充审批记录
            return new CommonApiResponse<>(ruleDetails);
        } catch (Exception e) {
            log.error("Failed to query store grade rule", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    @Override
    public CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> queryDetail(RuleQueryReq request) {
        try {
            log.info("Start querying store grade rule details, request parameters: {}", request);

            if (request == null || request.getRuleId() == null) {
                throw new RuntimeException("Parameter error");
            }

            // 查询规则表填充基础信息
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getId, request.getRuleId());

            StoreGradeRule storeGradeRule = this.getOne(queryWrapper);
            if (storeGradeRule == null) {
                throw new RuntimeException("Corresponding store grade rule not found");
            }

            // 构建规则详情
            StoreGradeRuleDetailResp.RuleDetails ruleDetails = buildRuleDetails(storeGradeRule);

            log.info("Query store grade rule details completed, rule ID: {}", request.getRuleId());
            return new CommonApiResponse<>(ruleDetails);

        } catch (Exception e) {
            log.error("Failed to query store grade rule details", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    @Override
    public CommonApiResponse<StoreGradeRuleDetailResp.RuleDetails> queryDraft(RuleQueryReq request) {
        try {
            log.info("Start querying store grade rule details, request parameters: {}", request);

            if (request == null || request.getRuleId() == null) {
                throw new RuntimeException("Parameter error");
            }

            // 查询规则表填充基础信息
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();
            queryWrapper.eq(StoreGradeRule::getId, request.getRuleId());

            StoreGradeRule storeGradeRule = this.getOne(queryWrapper);
            if (storeGradeRule == null) {
                throw new RuntimeException("Corresponding store grade rule not found");
            }
            if (!Objects.equals(ApprovalStatus.APPROVED.getCode(), storeGradeRule.getApproveStatus())) {
                LambdaQueryWrapper<CommonApproveLog> logQueryWrapper = Wrappers.lambdaQuery();
                logQueryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                        .eq(CommonApproveLog::getBusinessId, request.getRuleId().toString())
                        .orderByDesc(CommonApproveLog::getId);
                List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(logQueryWrapper);
                if (CollectionUtils.isEmpty(commonApproveLogs)) {
                    throw new RuntimeException("Corresponding store grade rule not found");
                }
                storeGradeRule = JsonUtils.toObject(commonApproveLogs.get(0).getTargetBody(), StoreGradeRule.class);
            }

            StoreGradeRuleDetailResp.RuleDetails ruleDetails = buildRuleDetails(storeGradeRule);
            // 构建规则详情

            log.info("Query store grade rule details completed, rule ID: {}", request.getRuleId());
            return new CommonApiResponse<>(ruleDetails);

        } catch (Exception e) {
            log.error("Failed to query store grade rule details", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    @Override
    public CommonApiResponse<Page<RuleModificationLog>> queryApprovalList(RuleQueryReq request) {
        try {
            log.info("Start querying store grade rule approval list, request parameters: {}", request);

            if (request == null || request.getRuleId() == null) {
                throw new RuntimeException("Parameter error");
            }

            // 创建分页对象
            Page<CommonApproveLog> page = new Page<>(request.getPageNum(), request.getPageSize());

            // 查询审批日志表填充修改记录
            LambdaQueryWrapper<CommonApproveLog> logQueryWrapper = Wrappers.lambdaQuery();
            logQueryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                    .eq(CommonApproveLog::getBusinessId, request.getRuleId().toString())
                    .orderByDesc(CommonApproveLog::getCreatedAt);

            Page<CommonApproveLog> approveLogPage = commonApproveLogMapper.selectPage(page, logQueryWrapper);

            // 转换为RuleModificationLog列表
            Page<RuleModificationLog> resultPage = new Page<>(approveLogPage.getCurrent(), approveLogPage.getSize(), approveLogPage.getTotal());
            List<RuleModificationLog> modificationLogs = new ArrayList<>();

            for (CommonApproveLog approveLog : approveLogPage.getRecords()) {
                RuleModificationLog modificationLog = new RuleModificationLog();

                modificationLog.setLogId(approveLog.getId());
                modificationLog.setChangedBy(approveLog.getCreatedBy());
                modificationLog.setApproveStatus(approveLog.getFlowStatus());
                modificationLog.setApproveStatusDesc(ApprovalStatus.getDescByCode(approveLog.getFlowStatus()));
                modificationLog.setApproveId(approveLog.getFlowInstId());
                String changeTime = IntlTimeUtil.parseTimestampToAreaTime(request.getCountryCode(), DateTimeUtil.toTimestampMillis(approveLog.getCreatedAt()));
                modificationLog.setChangedTime(changeTime);

                // 解析原始数据和新数据
                if (StrUtil.isNotBlank(approveLog.getOriginBody())) {
                    StoreGradeRule originRule = JsonUtils.toObject(approveLog.getOriginBody(), StoreGradeRule.class);
                    modificationLog.setOldValues(buildRuleValues(originRule));
                }

                if (StrUtil.isNotBlank(approveLog.getTargetBody())) {
                    StoreGradeRule targetRule = JsonUtils.toObject(approveLog.getTargetBody(), StoreGradeRule.class);
                    modificationLog.setNewValues(buildRuleValues(targetRule));
                }

                modificationLogs.add(modificationLog);
            }

            resultPage.setRecords(modificationLogs);
            log.info("Query store grade rule approval list completed, rule ID: {}", request.getRuleId());
            return new CommonApiResponse<>(resultPage);

        } catch (Exception e) {
            log.error("Failed to query store grade rule approval list", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    /**
     * 构建规则详情
     *
     * @param storeGradeRule 门店等级规则实体
     * @return 规则详情
     */
    private StoreGradeRuleDetailResp.RuleDetails buildRuleDetails(StoreGradeRule storeGradeRule) {
        StoreGradeRuleDetailResp.RuleDetails ruleDetails = new StoreGradeRuleDetailResp.RuleDetails();

        // 设置基本信息
        ruleDetails.setId(storeGradeRule.getId());
        ruleDetails.setChannelType(storeGradeRule.getChannelType());
        ruleDetails.setChannelTypeDesc(ChannelTypeEnum.getDescByCode(storeGradeRule.getChannelType()));
        ruleDetails.setModificationMethod(storeGradeRule.getMethod()); // TODO: 根据业务逻辑设置修改方式
        ruleDetails.setModificationMethodDesc(TieringModificationMethodEnum.getDescByCode(storeGradeRule.getMethod()));
        ruleDetails.setRuleStatus(storeGradeRule.getRulesStatus());
        ruleDetails.setRuleStatusDesc(RuleStatusEnum.getDescByCode(storeGradeRule.getRulesStatus()));
        ruleDetails.setApprovalStatus(storeGradeRule.getApproveStatus());
        ruleDetails.setApprovalStatusDesc(ApprovalStatus.getDescByCode(storeGradeRule.getApproveStatus()));

        // 设置数量信息
        ruleDetails.setSMinCount(storeGradeRule.getSMinCount() != null ? storeGradeRule.getSMinCount() : null);
        ruleDetails.setAMinCount(storeGradeRule.getAMinCount() != null ? storeGradeRule.getAMinCount() : null);
        ruleDetails.setBMinCount(storeGradeRule.getBMinCount() != null ? storeGradeRule.getBMinCount() : null);
        ruleDetails.setCMinCount(storeGradeRule.getCMinCount() != null ? storeGradeRule.getCMinCount() : null);
        ruleDetails.setDMinCount(storeGradeRule.getDMinCount() != null ? storeGradeRule.getDMinCount() : null);

        // 设置零售商信息
        ruleDetails.setRetailerCode(storeGradeRule.getRetailerCode());
        ruleDetails.setRetailerName(storeGradeRule.getRetailerName());

        Integer method = storeGradeRule.getMethod();
        List<StoreGradeCompleteStatistics> storeGradeCompleteList = new ArrayList<>();
        if (TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey().equals(method)) {
            // 当前逻辑：基于现有门店等级统计
            storeGradeCompleteList = getCurrentGradeStatistics(storeGradeRule);
        } else if (TieringModificationMethodEnum.MANUAL_UPLOAD.getKey().equals(method)) {
            // 基于门店等级关系表统计
            storeGradeCompleteList = getRelationGradeStatistics(storeGradeRule);
        }
        ruleDetails.setStoreGradeCompleteList(storeGradeCompleteList);
        ruleDetails.setFileData(JsonUtils.toObject(storeGradeRule.getFileData(), StoreGradeRuleReq.FileData.class));

        return ruleDetails;
    }

    /**
     * 基于门店等级关系表统计的逻辑
     *
     * @param storeGradeRule 门店等级规则
     * @return 门店等级统计列表
     */
    private List<StoreGradeCompleteStatistics> getRelationGradeStatistics(StoreGradeRule storeGradeRule) {
        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        Integer ruleId = storeGradeRule.getId();

        List<StoreGradeCompleteCount> gradeCompleteCounts = storeGradeRelationMapper.selectStoreGradeCountByRuleId(ruleId, countryCode);

        List<StoreGradeCompleteStatistics> result = new ArrayList<>();

        // 先计算总数
        Integer totalStoreCount = gradeCompleteCounts.stream()
                .mapToInt(count -> count.getCount().intValue())
                .sum();

        for (StoreGradeCompleteCount count : gradeCompleteCounts) {
            String grade = count.getGrade() != null ? count.getGrade() : "EMPTY";
            Integer storeCount = count.getCount().intValue();

            // 计算占总数的百分比
            Double percentage = totalStoreCount > 0 ? (double) storeCount / totalStoreCount : 0.0;

            StoreGradeCompleteStatistics statistics = new StoreGradeCompleteStatistics(
                    grade, storeCount, percentage, 0L
            );

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCount = result.stream()
                .mapToInt(StoreGradeCompleteStatistics::getCount)
                .sum();

        Double totalPercentage = 1.0; // 总计占总数的100%

        StoreGradeCompleteStatistics totalStatistics = new StoreGradeCompleteStatistics(
                "ALL", totalCount, totalPercentage, 0L
        );

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    private List<StoreGradeCompleteStatistics> getCurrentGradeStatistics(StoreGradeRule storeGradeRule) {
        // 从规则中获取渠道类型和渠道代码
        String retailerChannelType = String.valueOf(storeGradeRule.getChannelType());
        String retailerCode = storeGradeRule.getRetailerCode();

        // 从RPC上下文中获取countryCode
        String countryCode = RpcContextUtil.getCurrentAreaId();
        List<StoreGradeCompleteCount> gradeCompleteCounts = storeGradeMapper.selectStoreGradeCompleteCount(countryCode,
                retailerChannelType, retailerCode);

        List<StoreGradeCompleteStatistics> result = new ArrayList<>();

        // 先计算总数
        Integer totalStoreCount = gradeCompleteCounts.stream()
                .mapToInt(count -> count.getCount().intValue())
                .sum();

        for (StoreGradeCompleteCount count : gradeCompleteCounts) {
            String grade = count.getGrade() != null ? count.getGrade() : "EMPTY";
            Integer storeCount = count.getCount().intValue();

            // 计算占总数的百分比
            Double percentage = totalStoreCount > 0 ? (double) storeCount / totalStoreCount : 0.0;

            StoreGradeCompleteStatistics statistics = new StoreGradeCompleteStatistics(
                    grade, storeCount, percentage, 0L
            );

            result.add(statistics);
        }

        // 添加总计统计
        Integer totalCount = result.stream()
                .mapToInt(StoreGradeCompleteStatistics::getCount)
                .sum();

        Double totalPercentage = 1.0; // 总计占总数的100%

        StoreGradeCompleteStatistics totalStatistics = new StoreGradeCompleteStatistics(
                "ALL", totalCount, totalPercentage, 0L
        );

        result.add(0, totalStatistics); // 将总计放在第一位

        return result;
    }

    /**
     * 构建修改日志
     *
     * @param request
     * @return 修改日志列表
     */
    private List<RuleModificationLog> buildModificationLogs(RuleQueryReq request) {
        List<RuleModificationLog> modificationLogs = new ArrayList<>();

        try {
            // 查询审批日志
            LambdaQueryWrapper<CommonApproveLog> logQueryWrapper = Wrappers.lambdaQuery();
            logQueryWrapper.eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                    .eq(CommonApproveLog::getBusinessId, request.getRuleId().toString())
                    .orderByDesc(CommonApproveLog::getCreatedAt);

            List<CommonApproveLog> approveLogs = commonApproveLogMapper.selectList(logQueryWrapper);

            for (CommonApproveLog approveLog : approveLogs) {
                RuleModificationLog modificationLog = new RuleModificationLog();

                modificationLog.setChangedBy(approveLog.getCreatedBy());
                modificationLog.setApproveStatus(approveLog.getFlowStatus());
                modificationLog.setApproveId(approveLog.getFlowInstId());
                //modificationLog.setChangedTime(approveLog.getCreatedAt().atZone(ZoneId.systemDefault()).toInstant().toEpochMilli());

                // 解析原始数据和新数据
                if (StrUtil.isNotBlank(approveLog.getOriginBody())) {
                    StoreGradeRule originRule = JsonUtils.toObject(approveLog.getOriginBody(), StoreGradeRule.class);
                    modificationLog.setOldValues(buildRuleValues(originRule));
                }

                if (StrUtil.isNotBlank(approveLog.getTargetBody())) {
                    StoreGradeRule targetRule = JsonUtils.toObject(approveLog.getTargetBody(), StoreGradeRule.class);
                    modificationLog.setNewValues(buildRuleValues(targetRule));
                }

                modificationLogs.add(modificationLog);
            }

        } catch (Exception e) {
            log.error("Failed to build modification logs", e);
        }

        return modificationLogs;
    }

    /**
     * 构建规则值
     *
     * @param storeGradeRule 门店等级规则实体
     * @return 规则值
     */
    private RuleModificationLog.RuleValues buildRuleValues(StoreGradeRule storeGradeRule) {
        if (storeGradeRule == null) {
            return null;
        }

        RuleModificationLog.RuleValues ruleValues = new RuleModificationLog.RuleValues();
        ruleValues.setSMinCount(storeGradeRule.getSMinCount() != null ? storeGradeRule.getSMinCount() : null);
        ruleValues.setAMinCount(storeGradeRule.getAMinCount() != null ? storeGradeRule.getAMinCount() : null);
        ruleValues.setBMinCount(storeGradeRule.getBMinCount() != null ? storeGradeRule.getBMinCount() : null);
        ruleValues.setCMinCount(storeGradeRule.getCMinCount() != null ? storeGradeRule.getCMinCount() : null);
        ruleValues.setDMinCount(storeGradeRule.getDMinCount() != null ? storeGradeRule.getDMinCount() : null);
        if (Objects.nonNull(storeGradeRule.getFileData())) {
            StoreGradeRuleReq.FileData fileData = JsonUtils.toObject(storeGradeRule.getFileData(), StoreGradeRuleReq.FileData.class);
            ruleValues.setFileData(fileData);
        }
        return ruleValues;
    }

    @Override
    public CommonApiResponse<List<RuleModifyVersionResp>> queryVersionList(RuleQueryReq req) {
        return null;
        /*if (req == null || req.getRuleId() == null) {
            throw new RuntimeException("Parameter error");
        }
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CommonApproveLog::getBusinessId, req.getRuleId())
                .eq(CommonApproveLog::getBusinessKey, ApprovalBusinessKeyEnum.STORE_GRADE_RULE.getValue())
                .orderByDesc(CommonApproveLog::getId);
        List<CommonApproveLog> commonApproveLogs = commonApproveLogMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(commonApproveLogs)) {
            return new CommonApiResponse<>(Lists.newArrayList());
        }
        List<RuleModifyVersionResp> ruleModifyVersionRespList = new ArrayList<>();
        for (CommonApproveLog commonApproveLog : commonApproveLogs) {
            if (Objects.isNull(commonApproveLog.getOriginBody())) {
                continue;
            }
            RuleModifyVersionResp ruleModifyVersionResp = new RuleModifyVersionResp();
            ruleModifyVersionResp.setVersionId(commonApproveLog.getId());
            String versionTemplate = "{} Modification";
            ruleModifyVersionResp.setVersionName(StrUtil.format(versionTemplate, DateUtil.formatDate(commonApproveLog.getApplicationTime())));
            ruleModifyVersionResp.setModifyTime(commonApproveLog.getApplicationTime().getTime());
            ruleModifyVersionRespList.add(ruleModifyVersionResp);
        }
        //TODO fz后续补充查询新建或修改数据
        //TODO fz补充返回值，是否需要标识
        return new CommonApiResponse<>(ruleModifyVersionRespList);*/
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Boolean> deleteStoreGradeRule(Integer id) {
        try {
            log.info("Start deleting store grade rule, rule ID: {}", id);

            if (id == null) {
                log.error("Rule ID is empty");
                throw new RuntimeException("Parameter error");
            }

            // 查询现有记录
            StoreGradeRule existingRule = this.getById(id);
            if (existingRule == null) {
                log.error("Store grade rule with ID {} not found", id);
                throw new RuntimeException("Parameter error");
            }

            // 删除记录
            boolean result = this.removeById(id);

            log.info("Store grade rule deletion completed, deleted record ID: {}", id);
            return new CommonApiResponse<>(result);

        } catch (Exception e) {
            log.error("Failed to delete store grade rule", e);
            throw e;
        }
    }

    /**
     * 流程提交
     */
    @Override
    public SubmitResp submit(StoreGradeRuleReq req) {

        if (ObjectUtil.isEmpty(req) || req.getId() == null || StringUtils.isBlank(req.getCountryCode())) {
            throw new BusinessException("The request data is incorrect");
        }
        SubmitResp resp = new SubmitResp();
        String countryCode = req.getCountryCode();
        Map<String, Object> approvers = getApprovers(countryCode);
        setNationalRetailManager(approvers, req);
        StoreGradeRuleFormDataReq formDataReq = getFormDataReq(req);
        if (StringUtils.isBlank(req.getFlowInstId())) {
            String businessKey = UUID.randomUUID().toString().replace("-", "");
            String procInst = createProcInst(approvers, formDataReq, businessKey);
            resp.setFlowInstId(businessKey);
            resp.setRequestApprovalBody(procInst);
        } else {
            String submit = bpmService.submit(req.getFlowInstId(), getAccount(), formDataReq, approvers);
            resp.setFlowInstId(req.getFlowInstId());
            resp.setRequestApprovalBody(submit);
        }
        return resp;
    }

    @Override
    public SubmitResp anewSubmit(StoreGradeRuleReq req) {
        String countryCode = req.getCountryCode();
        Map<String, Object> approvers = getApprovers(countryCode);
        setNationalRetailManager(approvers, req);
        StoreGradeRuleFormDataReq formDataReq = getFormDataReq(req);
        bpmService.submit(req.getFlowInstId(), getAccount(), formDataReq, approvers);
        SubmitResp resp = new SubmitResp();
        return resp;
    }

    /**
     * 设置零售经理参数
     */
    private void setNationalRetailManager(Map<String, Object> approvers, StoreGradeRuleReq req) {
        Object retailManager = approvers.get("retailManager");
        if (ObjectUtil.isNotEmpty(retailManager) && retailManager instanceof List) {
            List<String> retailManagerList = (List<String>) retailManager;
            if (!retailManagerList.isEmpty()) {
                req.setNationalRetailManager(String.join(",", retailManagerList));
            }
        }
    }

    /**
     * 创建流程实例
     * @param approvers
     * @param formDataReq
     */
    private String createProcInst(Map<String, Object> approvers, StoreGradeRuleFormDataReq formDataReq,
                                  String businessKey) {
        //发起流程实例
        String storeGradeRule = bpmService.create(formDataReq, BpmApproveBusinessCodeEnum.STORE_GRADE_RULE,
                getAccount(), businessKey, approvers);
        log.info("发起流程实例成功，流程实例ID：{}", storeGradeRule);
        return storeGradeRule;
    }

    /**
     * 获取当前登录人信息
     * @return
     */
    private String getAccount() {
        String email = RpcContextUtil.getCurrentEmail();
        if (StringUtils.isBlank(email)) {
            throw new BusinessException("The current user is not logged in");
        }
        String emailPrefix = email != null && email.contains("@") ? email.substring(0, email.indexOf("@")) : null;
        if (StringUtils.isBlank(emailPrefix)) {
            throw new BusinessException("The current emailPrefix is not logged in");
        }
        return emailPrefix;
    }


    /**
     * 封装表单数据
     * @param req
     * @return
     */
    private StoreGradeRuleFormDataReq getFormDataReq(StoreGradeRuleReq req) {
        StoreGradeRuleFormDataReq formDataReq = new StoreGradeRuleFormDataReq();
        if (req.getRetailer() != null) {
            formDataReq.setRetailerName(req.getRetailer().getName());
            formDataReq.setRetailerCode(req.getRetailer().getCode());
        }
        formDataReq.setManager(req.getNationalRetailManager());
        formDataReq.setChannelType(ChannelTypeEnum.fromKey(req.getChannelType()).getValue());
        formDataReq.setCountry(req.getCountryName());
        formDataReq.setStoreGradeRule(String.format("%s-%s", req.getCountryName(), formDataReq.getChannelType()));
        formDataReq.setApplicationURL(getApplicationURL(req.getId()));
        if (req.getModificationMethod().equals(TieringModificationMethodEnum.SYSTEM_CALCULATION.getKey())) {
            formDataReq.setFlag("1");

            // 根据最小值设置容量范围
            if (req.getSMinCount() != null) {
                formDataReq.setCapaS("≥" + req.getSMinCount());
            }

            if (req.getAMinCount() != null) {
                // 这里需要根据业务逻辑确定最大值
                formDataReq.setCapaA(req.getAMinCount() + "-" + getAMaxCount(req));
            }

            if (req.getBMinCount() != null) {
                formDataReq.setCapaB(req.getBMinCount() + "-" + getBMaxCount(req));
            }

            if (req.getCMinCount() != null) {
                formDataReq.setCapaC(req.getCMinCount() + "-" + getCMaxCount(req));
            }

            if (req.getDMinCount() != null) {
                formDataReq.setCapaD("≤" + getDMaxCount(req));
            }

            formDataReq.setFileLink(req.getFileLink());
        } else if (req.getModificationMethod().equals(TieringModificationMethodEnum.MANUAL_UPLOAD.getKey())) {
            formDataReq.setFlag("3");
            StoreGradeRuleReq.FileData fileData = req.getFileData();
            StoreGradeRuleFormDataReq.UploadObject uploadObject = new StoreGradeRuleFormDataReq.UploadObject();
            uploadObject.setFileName(fileData.getFileName());
            uploadObject.setOriginFileName(fileData.getFileName());
            uploadObject.setUri(fileData.getUrl());
            List<StoreGradeRuleFormDataReq.UploadObject> files = new ArrayList<>();
            files.add(uploadObject);
            formDataReq.setFile(files);
        }

        return formDataReq;
    }

    /**
     * 获取规则申请链接
     * @param ruleId
     * @return
     */
    public String getApplicationURL(Integer ruleId) {
        String routingConfiguration = storeGradeRuleConfig.getConfiguration();
        StringBuilder builder = new StringBuilder();
        builder.append(routingConfiguration)
                .append(RouteConstants.MAIN_GLOBAL_POSITION_PATROL)
                .append(RouteConstants.URL_SEPARATOR)
                .append(RouteConstants.STORE_TIERING)
                .append(RouteConstants.URL_SEPARATOR)
                .append(RouteConstants.DETAIL)
                .append(RouteConstants.QUESTION_MARK)
                .append(RouteConstants.PARAM_ID)
                .append(RouteConstants.EQUALS_SIGN)
                .append(ruleId);
        return builder.toString();
    }

    // 辅助方法，根据业务逻辑确定各等级的最大值
    private Integer getAMaxCount(StoreGradeRuleReq req) {
        return req.getSMinCount() != null ? req.getSMinCount() - 1 : null;
    }

    private Integer getBMaxCount(StoreGradeRuleReq req) {
        return req.getAMinCount() != null ? req.getAMinCount() - 1 : null;
    }

    private Integer getCMaxCount(StoreGradeRuleReq req) {
        return req.getBMinCount() != null ? req.getBMinCount() - 1 : null;
    }

    private Integer getDMaxCount(StoreGradeRuleReq req) {
        return req.getCMinCount() != null ? req.getCMinCount() - 1 : null;
    }

    /**
     * 获取审批人
     * @param countryCode
     * @return
     */
    private Map<String, Object> getApprovers(String countryCode) {
        Map<Integer, Object> approvers = new HashMap<>();
        // 零售经理岗位id
        Integer retailManagerCode = PositionEnum.ORGANREGION_RETAIL_MANAGER.getCode();
        // 拓展经理岗位id
        Integer developmentManager = PositionEnum.ORGANREGION_DEVELOPMENT_MANAGER.getCode();
        List<Integer> positionIds = new ArrayList<>();
        positionIds.add(retailManagerCode);
        positionIds.add(developmentManager);
        Map<Integer, List<Long>> midMap = new HashMap<>();
        for (Integer positionId : positionIds) {
            GetParentOrganPositionUserReq req = new GetParentOrganPositionUserReq();
            req.setOrganCode(countryCode);
            req.setPositionId(positionId);
            req.setManageChannelList(Arrays.asList(27));
            Result<List<UserPosition>> result = userProvider.getParentOrganPositionUser(req);
            if (result == null || result.getCode() != 0) {
                throw new RuntimeException("get appFirstCategoryList error");
            }
            if (!CollectionUtil.isEmpty(result.getData())) {
                List<Long> miIds = result.getData().stream().map(UserPosition::getMiId).collect(Collectors.toList());
                midMap.put(positionId, miIds);
            }
        }
        List<Long> midList = new ArrayList<>();
        midMap.forEach((k, v) -> {
            if (v != null) {
                midList.addAll(v);
            }
        });
        if (!CollectionUtil.isEmpty(midList)) {
            SearchUserSensitiveInfoRequest request = new SearchUserSensitiveInfoRequest();
            request.setMiIdList(midList);
            Result<List<UserSensitiveInfoResp>> listResult = userAdminProvider.searchUserSensitiveInfo(request);

            if (listResult == null || listResult.getCode() != 0) {
                throw new RuntimeException("get appFirstCategoryList error");
            }
            if (!CollectionUtil.isEmpty(listResult.getData())) {
                // 将用户信息按miId分组，一个miId可能对应多个岗位(positionId)
                Map<Long, UserSensitiveInfoResp> userMap = listResult.getData().stream()
                        .collect(Collectors.toMap(UserSensitiveInfoResp::getMiId, user -> user));

                // 遍历midMap，构建approvers映射
                midMap.forEach((positionId, miIds) -> {
                    if (miIds != null) {
                        List<String> emailPrefixes = new ArrayList<>();
                        for (Long miId : miIds) {
                            UserSensitiveInfoResp user = userMap.get(miId);
                            if (user != null && StringUtils.isNotBlank(user.getEmail())) {
                                String emailPrefix = user.getEmail();
                                if (emailPrefix.contains("@")) {
                                    emailPrefix = emailPrefix.substring(0, emailPrefix.indexOf("@"));
                                }
                                if (emailPrefixes.size() < 4) {
                                    emailPrefixes.add(emailPrefix);
                                }
                            }
                        }
                        approvers.put(positionId, emailPrefixes);
                    }
                });
            }
        }
        Map<String, Object> rsp = new HashMap<>();
        // 当审批人不存在时传递空字符串，流程会自动跳过该审批人节点
        Object developmentManagerUser =
                approvers.get(developmentManager) == null ? "" : approvers.get(developmentManager);
        Object retailManagerCodeUser =
                approvers.get(developmentManager) == null ? "" : approvers.get(retailManagerCode);
        rsp.put(CommonConstant.DEVELOPMENT_MANAGER, developmentManagerUser);
        rsp.put(CommonConstant.DEVELOPMENT_MANAGER_CARBON_COPY, developmentManagerUser);
        rsp.put(CommonConstant.RETAIL_MANAGER, retailManagerCodeUser);
        rsp.put(CommonConstant.RETAIL_MANAGER_CARBON_COPY, retailManagerCodeUser);

        List<String> headquartersPosition = getHeadquartersPosition(countryCode);
        headquartersPosition = CollectionUtils.isEmpty(headquartersPosition) ? new ArrayList<>() : headquartersPosition;
        rsp.put(CommonConstant.HEADQUARTERS_POSITION, headquartersPosition);
        rsp.put(CommonConstant.HEADQUARTERS_POSITION_CARBON_COPY, headquartersPosition);

        return rsp;
    }

    /**
     * 获取总部阵地审批人
     *
     * @param countryCode
     * @return
     */
    private List<String> getHeadquartersPosition(String countryCode) {
        List<String> headquartersPosition = new ArrayList<>();
        CountryRoleAdminReq req = new CountryRoleAdminReq();
        req.setCountry(countryCode);
        req.setRole("BP");
        Result<List<CountryRoleAdminResp>> countryRoleAdmins = channelCommonProvider.getCountryRoleAdmins(req);
        if (countryRoleAdmins == null || countryRoleAdmins.getCode() != 0) {
            throw new RuntimeException("The acquisition of headquarters position personnel is wrong");
        }
        if (CollectionUtils.isEmpty(countryRoleAdmins.getData())) {
            throw new RuntimeException("No personnel from the headquarters position were found");
        }
            for (CountryRoleAdminResp countryRoleAdminResp : countryRoleAdmins.getData()) {
                if (headquartersPosition.size() < 4) {
                    headquartersPosition.add(countryRoleAdminResp.getAdmin());
                }
            }

        return headquartersPosition;
    }

    /**
     * 流程撤回
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<Boolean> recall(StoreGradeRuleRecallReq req) {
        try {
            if (req.getRuleId() == null) {
                throw new BusinessException("The request data is incorrect");
            }
            StoreGradeRule existingRule = this.getById(req.getRuleId());
            if (existingRule == null) {
                throw new BusinessException("The store level rule for the corresponding ID was not found");
            }

            if (!existingRule.getApproveStatus().equals(ApprovalStatus.IN_APPROVAL.getCode())) {
                throw new BusinessException("The current store tier rule status is not allowed to be withdrawn");
            }

            CommonApproveReq commonApproveReq = new CommonApproveReq();
            commonApproveReq.setBusinessId(existingRule.getId().toString());
            commonApproveReq.setFlowStatus(ApprovalStatus.IN_APPROVAL.getCode());
            CommonApproveLog commonApproveResp = getCommonApproveResp(commonApproveReq);

            if (commonApproveResp == null) {
                throw new BusinessException("The current approval record status does not allow retraction");
            }
            recallProcInst(req, commonApproveResp.getFlowInstId());
            commonApproveResp.setFlowStatus(ApprovalStatus.DRAFT.getCode());
            commonApproveLogMapper.updateById(commonApproveResp);
            existingRule.setApproveStatus(ApprovalStatus.DRAFT.getCode());
            this.updateById(existingRule);
            if (!Objects.equals(existingRule.getRulesStatus(), RuleStatusEnum.IN_EFFECT.getCode())) {
                existingRule.setRulesStatus(RuleStatusEnum.NOT_IN_EFFECT.getCode());
            }
            existingRule.setApproveStatus(ApprovalStatus.DRAFT.getCode());
            this.updateById(existingRule);

            LambdaUpdateWrapper<StoreGradeRelation> updateWrapper = Wrappers.lambdaUpdate();
            updateWrapper.eq(StoreGradeRelation::getStoreGradeRuleId, existingRule.getId());
            List<StoreGradeRelation> storeGradeRelations = storeGradeRelationMapper.selectList(updateWrapper);
            if (com.baomidou.mybatisplus.core.toolkit.CollectionUtils.isNotEmpty(storeGradeRelations)) {
                storeGradeRelationMapper.deleteBatchIds(storeGradeRelations);
            }
            return new CommonApiResponse<>(true);
        } catch (BusinessException e) {
            log.error("StoreGradeRuleServiceImpl.recall error", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    /**
     * 流程撤回请求
     * @param req
     */
    private void recallProcInst(StoreGradeRuleRecallReq req, String businessKey) {
        bpmService.recall(businessKey, getAccount(), req.getComment());
        log.info("BPM_RECALL_LOG,businessKey:{}----->user:{}", businessKey, "store_grade_rule");
    }

    /**
     * 根据条件查询审批记录
     * @param
     * @return
     */
    private CommonApproveLog getCommonApproveResp(CommonApproveReq req) {
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getBusinessId()),
                        CommonApproveLog::getBusinessId, req.getBusinessId())
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getBusinessKey()),
                        CommonApproveLog::getBusinessKey, req.getBusinessKey())
                .eq(com.baomidou.mybatisplus.core.toolkit.StringUtils.isNotBlank(req.getFlowInstId()),
                        CommonApproveLog::getFlowInstId, req.getFlowInstId())
                .eq(req != null, CommonApproveLog::getFlowStatus, req.getFlowStatus());
        CommonApproveLog approveLog = this.commonApproveLogMapper.selectOne(queryWrapper);
        return approveLog;
    }

    /**
     * 查看审批记录
     */
    @Override
    public CommonApiResponse<List<CommonApproveHistoryResp>> listProcessLog(CommonApproveHistoryReq req) {
        CommonApproveLog commonApproveResp = commonApproveLogMapper.selectById(req.getRuleLogId());
        if (ObjectUtil.isEmpty(commonApproveResp)) {
            throw new BusinessException("No corresponding approval record found");
        }

        // Redis中没有缓存数据，从数据库查询
        List<CommonApproveHistoryResp> resps = new ArrayList<>();
        List<ApprovalTaskListResp> approvalTaskListResps =
                bpmService.listProcessLog(commonApproveResp.getFlowInstId().toString(), null);
        if (!CollectionUtils.isEmpty(approvalTaskListResps)) {
            for (ApprovalTaskListResp resp : approvalTaskListResps) {
                CommonApproveHistoryResp historyResp = new CommonApproveHistoryResp();
                historyResp.setPhase(resp.getTaskName());
                historyResp.setRuleLogId(req.getRuleLogId());
                if (resp.getAssignee() != null) {
                    historyResp.setApprover(resp.getAssignee().getUserName());
                }
                if (resp.getOperation() != null) {
                    historyResp.setApproveStatus(resp.getOperation().getCode());
                }
                historyResp.setComment(resp.getComment());
                ZonedDateTime endTime = resp.getEndTime();
                if (endTime != null) {
                    historyResp.setApproverTime(endTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
                ZonedDateTime createTime = resp.getCreateTime();
                if (createTime != null) {
                    historyResp.setCreateTime(createTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
                }
                resps.add(historyResp);
            }
        }

        return new CommonApiResponse<>(resps);
    }


    @Override
    public CommonApiResponse<StoreGradeResp> queryStoreGrade(StoreGradeReq req) {
        StoreGradeResp resp = new StoreGradeResp();
        if (ObjectUtil.isNotEmpty(req) && StringUtils.isNotBlank(req.getRetailerCode()) && req.getKapa() != null) {
            StoreGradeRule storeGradeRule =
                    this.lambdaQuery().eq(StoreGradeRule::getRetailerCode, req.getRetailerCode())
                            .eq(StoreGradeRule::getRulesStatus, RuleStatusEnum.IN_EFFECT.getCode()).one();
            if (storeGradeRule != null) {
                Long kapa = Long.valueOf(req.getKapa());
                String grade = determineGradeByKapa(kapa, storeGradeRule);
                resp.setStoreGrade(grade);
                resp.setStoreGradeKey(StoreGradeEnum.fromValue(grade).getKey());
                resp.setRetailerCode(req.getRetailerCode());
            }
        }
        return new CommonApiResponse<>(resp);
    }

    @Override
    public SubmitResp batchUpdateStoreGrade(StoreGradeRuleReq req) {
        try {
            StoreGradeRuleReq.FileData fileData = req.getFileData();

            SubmitResp resp = new SubmitResp();
            List<StoreGradeBatchData> dataList = parseExcelFile(fileData);
            checkDataList(dataList, req);
            Map<String, Object> approvers = getApprovers(req.getCountryCode());
            setNationalRetailManager(approvers, req);
            StoreGradeRuleFormDataReq formDataReq = getFormDataReq(req);
            if (StringUtils.isBlank(req.getFlowInstId())) {
                String businessKey = UUID.randomUUID().toString().replace("-", "");
                String procInst = createProcInst(approvers, formDataReq, businessKey);
                resp.setFlowInstId(businessKey);
                resp.setRequestApprovalBody(procInst);
            } else {
                String submit = bpmService.submit(req.getFlowInstId(), getAccount(), formDataReq, approvers);
                resp.setFlowInstId(req.getFlowInstId());
                resp.setRequestApprovalBody(submit);
            }

            List<StoreGradeRelation> storeGradeRelations = new ArrayList<>();
            for (StoreGradeBatchData data : dataList) {
                StoreGradeRelation storeGradeRelation = new StoreGradeRelation();
                storeGradeRelation.setCountryCode(req.getCountryCode());
                storeGradeRelation.setStoreCode(data.getStoreCode());
                storeGradeRelation.setStoreGradeRuleId(req.getId());
                storeGradeRelation.setStoreGrade(data.getStoreTiering());
                storeGradeRelations.add(storeGradeRelation);
            }
            if (!CollectionUtils.isEmpty(storeGradeRelations)) {
                this.storeGradeRelationMapper.insertBatch(storeGradeRelations);
            }

            return resp;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 解析Excel文件为List数据
     *
     */
    private List<StoreGradeBatchData> parseExcelFile(StoreGradeRuleReq.FileData data) throws Exception {
        OkHttpClient client = new OkHttpClient();
        Request request = new Request.Builder().url(data.getUrl()).build();
        Response response = client.newCall(request).execute();
        List<StoreGradeBatchData> dataList = new ArrayList<>();
        if (response.isSuccessful()) {
            try (InputStream inputStream = response.body().byteStream();
                 Workbook workbook = new XSSFWorkbook(inputStream)) {

                // 遍历所有Sheet
                int numberOfSheets = workbook.getNumberOfSheets();
                for (int i = 0; i < numberOfSheets; i++) {
                    Sheet sheet = workbook.getSheetAt(i);
                    List<StoreGradeBatchData> sheetDataList = processSheet(sheet);
                    dataList.addAll(sheetDataList);
                }
            }
        }
        return dataList;
    }


    /**
     * 处理单个Sheet的数据
     *
     * @param sheet Excel工作表
     * @return 转换后的StoreGradeBatchData列表
     */
    private List<StoreGradeBatchData> processSheet(Sheet sheet) {
        List<StoreGradeBatchData> list = new ArrayList<>();

        // 转换Sheet为Map列表
        List<Map<String, String>> dataList = convertSheetToMapList(sheet);
        if (CollectionUtils.isEmpty(dataList)) {
            return list;
        }
        // 转换为StoreGradeBatchData对象
        dataList.forEach(data -> {
            StoreGradeBatchData storeGradeBatchData = new StoreGradeBatchData();
            storeGradeBatchData.setStoreCode(data.get("Store Code"));
            storeGradeBatchData.setRetailerCode(data.get("Retailer Code"));
            storeGradeBatchData.setStoreTiering(data.get("Store Tiering"));
            list.add(storeGradeBatchData);
        });

        return list;
    }

    // 更通用的转换方法
    private List<Map<String, String>> convertSheetToMapList(Sheet sheet) {
        List<Map<String, String>> dataList = new ArrayList<>();

        // 获取表头
        Row headerRow = sheet.getRow(0);
        List<String> headers = new ArrayList<>();
        if (headerRow != null) {
            for (Cell cell : headerRow) {
                headers.add(getCellValue(cell));
            }
        }

        // 处理数据行
        for (Row row : sheet) {
            if (row.getRowNum() == 0) {
                // 跳过表头
                continue;
            }

            Map<String, String> rowData = new HashMap<>();
            for (int i = 0; i < headers.size(); i++) {
                String columnName = headers.get(i);
                String cellValue = getCellValue(row.getCell(i));
                if (StringUtils.isBlank(cellValue)) {
                    throw new BusinessException("line " + row.getRowNum() + 1 + "has data that is empty");
                }
                rowData.put(columnName, cellValue);
            }

            dataList.add(rowData);
        }

        return dataList;
    }

    private String getCellValue(Cell cell) {
        if (cell == null) {
            return "";
        }
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }

    /**
     * 判断数据是否合法
     */
    private void checkDataList(List<StoreGradeBatchData> dataList, StoreGradeRuleReq req) {
        if (CollectionUtil.isEmpty(dataList)) {
            throw new BusinessException("No data found in the Excel file");
        }
        dataList.forEach(data -> {
            StoreGradeEnum.fromValue(data.getStoreTiering());
        });
        String firstRetailerCode = dataList.get(0).getRetailerCode();
        if (StringUtils.isNotBlank(req.getRetailerCode())) {
            boolean allSameRetailer = dataList.stream()
                    .allMatch(data -> firstRetailerCode.equals(data.getRetailerCode()));
            if (!allSameRetailer) {
                throw new BusinessException("The retailer code in the Excel file is not consistent");
            }
            String retailerCode = dataList.get(0).getRetailerCode();
            if (!retailerCode.equals(req.getRetailerCode())) {
                throw new BusinessException("The retailer in the Excel file is different from the one you selected");
            }
            // 查询当前零售商编码下审批状态为进行中或审批通过的数据集合
            List<StoreGradeRule> existingRules = this.lambdaQuery()
                    .eq(StoreGradeRule::getRetailerCode, req.getRetailerCode())
                    .in(StoreGradeRule::getRulesStatus, Arrays.asList(ApprovalStatus.IN_APPROVAL.getCode(),
                            ApprovalStatus.APPROVED.getCode()))
                    .list();
            if (CollectionUtil.isNotEmpty(existingRules)) {
                throw new BusinessException("This Retailer is already included in other rules");
            }
            LambdaQueryWrapper<IntlRmsRetailer> queryRetailerWrapper = Wrappers.lambdaQuery();
            queryRetailerWrapper.eq(IntlRmsRetailer::getName, req.getRetailerCode());
            IntlRmsRetailer retailer = intlRmsRetailerMapper.selectOne(queryRetailerWrapper);
            if (retailer.getRetailerChannelType() == null) {
                throw new BusinessException("This Retailer's Channel Type is empty in RMS, " +
                        "please maintain it in RMS-retailer");
            }

            if (!retailer.getRetailerChannelType().equals(req.getChannelType())) {
                throw new BusinessException("This Retailer's Channel Type is not consistent with the Channel Type " +
                        "selected in the rule");
            }
        }

        // 获取dataList中的storeCode集合
        List<String> storeCodes = dataList.stream()
                .map(StoreGradeBatchData::getStoreCode)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (CollectionUtil.isNotEmpty(storeCodes)) {
            // 根据storeCode集合查询门店信息
            LambdaQueryWrapper<IntlRmsStore> queryStoreWrapper = Wrappers.lambdaQuery();
            queryStoreWrapper.in(IntlRmsStore::getCrssCode, storeCodes);
            List<IntlRmsStore> stores = intlRmsStoreMapper.selectList(queryStoreWrapper);

            // 检查是否有未找到的门店
            if (stores.size() != storeCodes.size()) {
                throw new BusinessException("Some store codes in the Excel file could not be found");
            }

            // 检查所有门店的retailerId是否与请求中的retailerCode对应的retailer一致
            for (IntlRmsStore store : stores) {
                if (!req.getRetailerCode().equals(store.getRetailerIdName())) {
                    throw new BusinessException("The stores in the Excel file does not belong to the retailer you selected");
                }
            }
        }
    }




    /**
     * 获取单元格值为字符串
     *
     * @param cell 单元格
     * @return 字符串值
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                    return DateUtil.format(cell.getDateCellValue(), "yyyy-MM-dd");
                } else {
                    // 对于数值类型，转换为整数字符串（避免科学计数法）
                    return String.valueOf((long) cell.getNumericCellValue());
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                return cell.getCellFormula();
            default:
                return "";
        }
    }
    @Override
    public CommonApiResponse<String> getFileTemplate(FileTemplateReq req) {
        String url = "";
        if (req.getType().equals(FileTemplateEnum.STORE_GRADE_RULE_TEMPLATE.getCode())) {
            url = storeGradeRuleConfig.getStoreGradeRule();
        }
        return new CommonApiResponse<>(url);
    }

    @Override
    public CommonApiResponse<List<RetailerQueryResp>> queryRetailerList(RetailerQueryReq request) {
        String countryId = "";
        if (StringUtils.isNotBlank(request.getCountryCode())) {
            IntlRmsCountryTimezone countryByCode = getCountryByCode(request.getCountryCode());
            if (countryByCode == null) {
                throw new BusinessException("Country code is not found");
            }
            countryId = countryByCode.getCountryId();
        }

        List<RetailerQueryResp> resps = new ArrayList<>();
        LambdaQueryWrapper<IntlRmsRetailer> queryRetailerWrapper = Wrappers.lambdaQuery();
        queryRetailerWrapper.eq(StringUtils.isNotBlank(countryId), IntlRmsRetailer::getCountryId, countryId);
        queryRetailerWrapper.eq(request.getChannelType() != null, IntlRmsRetailer::getRetailerChannelType,
                request.getChannelType());
        if (StringUtils.isNotBlank(request.getSearch())) {
            queryRetailerWrapper.nested(wrapper -> wrapper
                    .like(IntlRmsRetailer::getName, request.getSearch())
                    .or()
                    .like(IntlRmsRetailer::getRetailerName, request.getSearch()));
        }
        queryRetailerWrapper.last("LIMIT 50");
        List<IntlRmsRetailer> retailers = intlRmsRetailerMapper.selectList(queryRetailerWrapper);
        if (CollectionUtil.isEmpty(retailers)) {
            return new CommonApiResponse<>(resps);
        }
        for (IntlRmsRetailer retailer : retailers) {
            RetailerQueryResp resp = new RetailerQueryResp();
            resp.setRetailerCode(retailer.getName());
            resp.setCrmCode(retailer.getCrmCode());
            resp.setRetailerName(retailer.getRetailerName());
            resp.setCountryId(retailer.getCountryId());
            resp.setCountryName(retailer.getCountryName());
            resp.setCountryCode(retailer.getCountryCode());
            resp.setChannelType(retailer.getRetailerChannelType());
            resps.add(resp);
        }

        return new CommonApiResponse<>(resps);
    }

    @Override
    public CommonApiResponse<UploadManuallyRuleDetailsResp> getUploadManuallyRuleDetails(Long ruleId) {

        UploadManuallyRuleDetailsResp detailsResp = new UploadManuallyRuleDetailsResp();
        StoreGradeRule rule = baseMapper.selectById(ruleId);
        if (rule == null) {
            throw new BusinessException("Invalid ruleId: " + ruleId);
        }
        DetailsTopmostResp topmostResp = new DetailsTopmostResp();
        topmostResp.setChannelType(String.valueOf(rule.getChannelType()));
        StoreGradeRuleReq.FileData fileData = JsonUtils.toObject(rule.getFileData(), StoreGradeRuleReq.FileData.class);
        topmostResp.setExcelFile(Objects.nonNull(fileData) ? fileData.getUrl() : null);
        topmostResp.setRetailerCode(rule.getRetailerCode());
        topmostResp.setRetailerName(rule.getRetailerName());
        topmostResp.setId(ruleId);
        // 除了生效中，别的状态都为未生效
        String rulesCurrentStatus = rule.getRulesStatus().equals(RuleStatusEnum.IN_EFFECT.getCode()) ? RuleStatusEnum.IN_EFFECT.getValue() : "Not In Effect";
        topmostResp.setRulesCurrentStatus(rulesCurrentStatus);
        detailsResp.setDetailsTopmostResp(topmostResp);

        List<DetailsBelowResp> detailsBelowRespList = new ArrayList<>();
        LambdaQueryWrapper<CommonApproveLog> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(CommonApproveLog::getBusinessId, ruleId);
        queryWrapper.orderByAsc(CommonApproveLog::getId);
        List<CommonApproveLog> logs = commonApproveLogMapper.selectList(queryWrapper);
        String createBy = rule.getCreateBy();
        LocalDateTime applicationTime = rule.getApplicationTime();
        if (CollectionUtil.isEmpty(logs)) {
            DetailsBelowResp belowResp = new DetailsBelowResp();
            belowResp.setChangedBy(createBy);
            belowResp.setChangedTime(applicationTime);
            belowResp.setNewValues(Objects.nonNull(fileData) ? fileData.getUrl() : null);
            detailsBelowRespList.add(belowResp);
        } else {
            String oldValues = null;
            for (CommonApproveLog log : logs) {
                DetailsBelowResp belowResp = new DetailsBelowResp();
                belowResp.setChangedBy(createBy);
                belowResp.setChangedTime(applicationTime);
                String originBody = log.getOriginBody();
                if (StrUtil.isNotBlank(originBody)) {
                    Map<String, Object> originMap = JacksonUtil.parseObj(originBody, Map.class);
                    String url = (String) originMap.get("fileUrl");
                    belowResp.setOldValues(oldValues);
                    belowResp.setNewValues(url);
                    oldValues = url;
                }
                detailsBelowRespList.add(belowResp);
            }
        }
        detailsResp.setDetailsBelowRespList(detailsBelowRespList);

        return new CommonApiResponse<>(detailsResp);
    }

    @Override
    public CommonApiResponse<String> exportStoreGradeRule(StoreGradeExportReq request) {
        if (request.getRuleId() != null) {
            StoreGradeRule gradeRule = this.getById(request.getRuleId());
            if (gradeRule == null) {
                throw new BusinessException("The current record rule ID is incorrect");
            }
            request.setRetailerCode(gradeRule.getRetailerCode());
        }
        if (StringUtils.isNotBlank(request.getCountryCode())) {
            IntlRmsCountryTimezone countryByCode = getCountryByCode(request.getCountryCode());
            if (countryByCode == null) {
                throw new BusinessException("Country code is not found");
            }
            request.setCountryId(countryByCode.getCountryId());
        }
        Page<IntlRmsStore> page = new Page<>(1, 10);
        Page<IntlRmsStore> rmsStorePage = getRmsStorePage(page, request);
        if (CollectionUtils.isEmpty(rmsStorePage.getRecords())) {
            return CommonApiResponse.failure(1, "No store data found");
        }

        TriggerJobRequestDTO jobReq = new TriggerJobRequestDTO();
        String account = RpcContextUtil.getCurrentAccount();
        BusinessException.when(org.junit.platform.commons.util.StringUtils.isBlank(account), "Unable to acquire the current user");
        String taskName = String.format("导出商店等级%s", DateFormatUtils.format(System.currentTimeMillis(), "yyyyMMddHHmmss"));
        jobReq.setTaskName(taskName);
        jobReq.setTaskParam(JacksonUtil.toStr(request));
        jobReq.setTaskDesc("导出商店等级");
        jobReq.setJobKey(NrJobConst.STORE_GRADE_RULE_EXPORT_JOB_KEY);
        jobReq.setOwner(account);
        String exportTask = createExportTask(jobReq);
        return new CommonApiResponse<>(exportTask);
    }

    /**
     * 获取国家信息
     *
     * @param countryCode
     * @return
     */
    private IntlRmsCountryTimezone getCountryByCode(String countryCode) {
        LambdaQueryWrapper<IntlRmsCountryTimezone> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(IntlRmsCountryTimezone::getCountryCode, countryCode);
        return intlRmsCountryTimezoneMapper.selectOne(queryWrapper);
    }

    /**
     * 创建定时任务
     *
     * @param request
     * @return
     */
    private String createExportTask(TriggerJobRequestDTO request) {
        request.setProjectId(projectId);
        // request.setProjectName(projectName);
        request.setProjectName(NrJobConst.PROJECT_NAME);
        log.info("NrJobGatewayImpl.createExportTask request: {}", request);

        Result<String> result = triggerJobWithLogging(request);

        validateResult(result);

        return result.getData();
    }

    /**
     * 触发任务
     *
     * @param request
     * @return
     */
    private Result<String> triggerJobWithLogging(TriggerJobRequestDTO request) {
        try {
            long start = System.currentTimeMillis();
            Result<String> result = nrJobService.triggerJob(request);
            long duration = System.currentTimeMillis() - start;
            log.info("NrJobGatewayImpl.createExportTask call triggerJob finished, cost time: {}ms, result: {}", duration, result);
            return result;
        } catch (Exception e) {
            log.error("NrJobGatewayImpl.createExportTask call nrJob error", e);
            throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "Failed to create task via task center");
        }
    }

    private void validateResult(Result<String> result) {
        if (result.getCode() != GeneralCodes.OK.getCode()) {
            throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), result.getMessage());
        }

        String status = getStatusFromResult(result);
        handleStatusError(status);
    }

    private String getStatusFromResult(Result<String> result) {
        if (result.getAttachments() != null) {
            return result.getAttachments().get("status");
        }
        return null;
    }

    private void handleStatusError(String status) {
        if (status == null) {
            return;
        }

        switch (status) {
            case NrJobConst.TASK_NUM_LIMIT_EXCEEDED_CODE:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "The number of export tasks exceeds the limit");
            case NrJobConst.TASK_IS_RUNNING_ERROR_CIDE:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "The task is in progress, please try again later");
            default:
                throw new BusinessException(ErrorCodeEnums.EXTCALL_EXCEPTION.getErrorCode(), "Calling the task center to create a task failed");
        }
    }


    /**
     * 商店等级导出异步回调
     */
    @NrJob(NrJobConst.STORE_GRADE_RULE_EXPORT_JOB_HANDLER)
    public Result<HandleMsg> exportHandler() {
        String param = JobHelper.getJobParam();
        log.info("exportHandler param:{}", param);
        StoreGradeExportReq exportReq = JacksonUtil.parseObj(param, StoreGradeExportReq.class);


        File file = null;
        List<String> retailerCodes = new ArrayList<>();
        if (StringUtils.isBlank(exportReq.getRetailerCode())) {
            List<StoreGradeRule> gradeRules = this.lambdaQuery().eq(StoreGradeRule::getRulesStatus, RuleStatusEnum.IN_EFFECT.getCode()).list();
            retailerCodes = gradeRules.stream().map(StoreGradeRule::getRetailerCode).filter(Objects::nonNull).collect(Collectors.toList());
        }

        try {
            file = File.createTempFile("StoreGrade", ExcelTypeEnum.XLSX.getValue());
            Page<IntlRmsStore> page = new Page<>(1, 500);
            ExcelWriter excelWriter = null;
            WriteSheet writeSheet = null;
            boolean writerInitialized = false;

            try {
                do {
                    Page<IntlRmsStore> rmsStorePage = getRmsStorePage(page, exportReq);
                    List<IntlRmsStore> records = rmsStorePage.getRecords();
                    if (!CollectionUtils.isEmpty(records)) {
                        List<StoreGradeExprot> gradeExprotList = getGradeExprotList(records, retailerCodes);
                        if (!writerInitialized) {
                            excelWriter = EasyExcelFactory.write(file, StoreGradeExprot.class).build();
                            writeSheet = EasyExcelFactory.writerSheet("StoreGrade").build();
                            writerInitialized = true;
                        }
                        if (excelWriter == null) {
                            throw new BusinessException("Excel写入器未正确初始化");
                        }
                        excelWriter.write(gradeExprotList, writeSheet);
                        log.debug("成功写入{}条数据", gradeExprotList.size());
                    }

                    page.setCurrent(page.getCurrent() + 1);
                } while (page.hasNext());

            } finally {
                if (excelWriter != null) {
                    excelWriter.finish();
                    log.info("Excel文件写入完成");
                }
            }

            // 文件上传
            String objectName = "INSPECTION_FILE_NAME_PREFIX/" + UUID.randomUUID() + "/" + "StoreGrade.xlsx";
            FdsUploadResult upload = fdsService.upload(objectName, file, true);
            HandleMsg resp = new HandleMsg();
            resp.setFileUrl(upload.getUrl());
            String fileUrl = JacksonUtil.toStr(resp);
            JobHelper.handleSuccess(fileUrl);
            log.info("exportHandler url:{}", fileUrl);
            return Result.success(resp);

        } catch (Exception e) {
            log.error("导出异常 {}", e.getMessage(), e);
            JobHelper.handleFail(e.getMessage());
            return null;
        } finally {
            // 清理临时文件
            if (file != null && file.exists()) {
                file.delete();
            }
        }
    }

    /**
     * 转化为导出对象
     *
     * @param intlRmsStores
     * @param retailerCodes
     * @return
     */
    private List<StoreGradeExprot> getGradeExprotList(List<IntlRmsStore> intlRmsStores, List<String> retailerCodes) {
        List<StoreGradeExprot> storeGradeExprotList = new ArrayList<>();

        // 提前获取所有门店的capa数据，避免在循环中重复调用
        List<String> storeCodes = intlRmsStores.stream()
                .map(IntlRmsStore::getCrssCode)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        Map<String, Integer> capaMap = getStoreCapa(storeCodes);

        for (IntlRmsStore intlRmsStore : intlRmsStores) {
            StoreGradeExprot storeGradeExprot = new StoreGradeExprot();
            String tieringComplete = intlRmsStore.getGradeCalFlag() == 0 ? "N" : "Y";
            storeGradeExprot.setTieringComplete(tieringComplete);
            storeGradeExprot.setStoreTiering(intlRmsStore.getGradeName());
            storeGradeExprot.setCountry(intlRmsStore.getCountryIdName());
            storeGradeExprot.setProvince(intlRmsStore.getProvinceLabel());
            storeGradeExprot.setCity(intlRmsStore.getCityIdName());
            storeGradeExprot.setStoreName(intlRmsStore.getName());
            storeGradeExprot.setStoreCode(intlRmsStore.getCrssCode());
            storeGradeExprot.setRetailerName(intlRmsStore.getRetailerName());
            storeGradeExprot.setRetailerCode(intlRmsStore.getRetailerIdName());
            storeGradeExprot.setChannelType(intlRmsStore.getChannelTypeName());
            Date modifiedOn = intlRmsStore.getModifiedOn();
            if (modifiedOn != null) {
                storeGradeExprot.setChangedDate(DateUtil.toLocalDateTime(intlRmsStore.getModifiedOn()));
            }

            // 使用预先获取的capaMap
            storeGradeExprot.setStoreCAPA(capaMap.get(intlRmsStore.getCrssCode()));

            if (intlRmsStore.getGradeCalFlag() == 0) {
                if (!retailerCodes.contains(intlRmsStore.getRetailerIdName())) {
                    storeGradeExprot.setFailureReason("No Corresponding Rule");
                } else if (StringUtils.isBlank(storeGradeExprot.getChannelType())) {
                    storeGradeExprot.setFailureReason("No Channel Type");
                } else if (storeGradeExprot.getStoreCAPA() == null || storeGradeExprot.getStoreCAPA() == 0) {
                    storeGradeExprot.setFailureReason("No Store CAPA");
                } else if (StringUtils.isBlank(storeGradeExprot.getRetailerCode())) {
                    storeGradeExprot.setFailureReason("No Retailer");
                }
            }
            storeGradeExprotList.add(storeGradeExprot);
        }
        return storeGradeExprotList;
    }

    /**
     * 根据门店编码获取门店capa
     *
     * @param storeCodes
     * @return
     */
    public Map<String, Integer> getStoreCapa(List<String> storeCodes) {
        Map<String, Integer> capaMap = new HashMap<>();
        String []  filters = new String[]{DomainEnum.EXTENSION.getName(), DomainEnum.MERGE.getName(),
                DomainEnum.BASE.getName(), DomainEnum.CATEGORY.getName(), DomainEnum.SERVICE.getName()};
        OrgResponse orgResponse = mainDataRpc.selectStoreByOrgIds(storeCodes, filters);

        if (orgResponse != null && CollectionUtil.isNotEmpty(orgResponse.getOrgList())) {
            orgResponse.getOrgList().forEach(orgDataDto -> {
                OrgExtension orgExtension = orgDataDto.getOrgExtension();
                if (orgExtension != null) {
                    Integer capaMonth = orgExtension.getCapaMonth();
                    String orgId = orgExtension.getOrgId();
                    if (StringUtils.isNotBlank(orgId)) {
                        capaMap.put(orgId, capaMonth);
                    }
                }
            });
        }

        return capaMap;
    }

    /**
     * 根据条件分页返回数据
     *
     * @param page
     * @param exportReq
     * @return
     */
    private Page<IntlRmsStore> getRmsStorePage(Page<IntlRmsStore> page, StoreGradeExportReq exportReq) {
        LambdaQueryWrapper<IntlRmsStore> storeQueryWrapper = Wrappers.lambdaQuery();
        storeQueryWrapper.eq(StringUtils.isNotBlank(exportReq.getCountryId()), IntlRmsStore::getCountryId,
                exportReq.getCountryId());
        storeQueryWrapper.eq(StringUtils.isNotBlank(exportReq.getRetailerCode()), IntlRmsStore::getRetailerIdName,
                exportReq.getRetailerCode());
        storeQueryWrapper.isNotNull(IntlRmsStore::getCrssCode);
        Page<IntlRmsStore> intlRmsStorePage = intlRmsStoreMapper.selectPage(page, storeQueryWrapper);
        return intlRmsStorePage;
    }

    /**
     * 根据kapa值和等级规则确定门店等级
     * 按照S->A->B->C->D的顺序从上到下判断
     *
     * @param kapa 实际容量值
     * @param rule 等级规则
     * @return 对应的等级
     */
    private String determineGradeByKapa(Long kapa, StoreGradeRule rule) {
        // S等级判断
        if (rule.getSMinCount() != null && kapa >= rule.getSMinCount()) {
            return "S";
        }
        // A等级判断
        if (rule.getAMinCount() != null && kapa >= rule.getAMinCount()) {
            return "A";
        }
        // B等级判断
        if (rule.getBMinCount() != null && kapa >= rule.getBMinCount()) {
            return "B";
        }
        // C等级判断
        if (rule.getCMinCount() != null && kapa >= rule.getCMinCount()) {
            return "C";
        }
        // D等级判断（默认等级）
        if (rule.getDMinCount() != null && kapa >= rule.getDMinCount()) {
            return "D";
        }

        // 如果都不满足，返回最低等级D
        return "D";
    }


    /**
     * 分页查询门店等级规则
     *
     * @param request 分页查询请求
     * @return 分页查询结果
     */
    @Override
    public CommonApiResponse<StoreGradeRulePageResp> pageQuery(StoreGradeRulePageQueryReq request) {
        try {
            log.info("Start paging to query store level rules and request parameters: {}", request);

            if (request == null) {
                log.error("Paging query request object is empty");
                throw new RuntimeException("Incorrect parameters");
            }

            // 构建查询条件
            LambdaQueryWrapper<StoreGradeRule> queryWrapper = Wrappers.lambdaQuery();

            // 规则状态查询（数组）
            if (request.getRuleStatus() != null && !request.getRuleStatus().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getRulesStatus, request.getRuleStatus());
            }

            // 审批状态查询（数组）
            if (request.getApprovalStatus() != null && !request.getApprovalStatus().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getApproveStatus, request.getApprovalStatus());
            }

            // 渠道类型查询（数组）
            if (request.getChannelType() != null && !request.getChannelType().isEmpty()) {
                queryWrapper.in(StoreGradeRule::getChannelType, request.getChannelType());
            }

            // 零售商名称或编码模糊搜索
            if (StrUtil.isNotBlank(request.getRetailerNameCode())) {
                queryWrapper.and(qw -> qw
                        .like(StoreGradeRule::getRetailerName, request.getRetailerNameCode())
                        .or()
                        .like(StoreGradeRule::getRetailerCode, request.getRetailerNameCode())
                );
            }

            // 申请日期区间查询
            if (request.getApplicationDate() != null) {
                if (Objects.nonNull(request.getApplicationDate().getStart())) {
                    queryWrapper.ge(StoreGradeRule::getApplicationTime, DateTimeUtil.timestampToLocalDateTime(request.getApplicationDate().getStart()));
                }
                if (Objects.nonNull(request.getApplicationDate().getEnd())) {
                    queryWrapper.le(StoreGradeRule::getApplicationTime, DateTimeUtil.timestampToLocalDateTime(request.getApplicationDate().getEnd()));
                }
            }

            // 审批日期区间查询
            if (request.getApprovedDate() != null) {
                if (Objects.nonNull(request.getApprovedDate().getStart())) {
                    queryWrapper.ge(StoreGradeRule::getApprovedTime, DateTimeUtil.timestampToLocalDateTime(request.getApprovedDate().getStart()));
                }
                if (Objects.nonNull(request.getApprovedDate().getEnd())) {
                    queryWrapper.le(StoreGradeRule::getApprovedTime, DateTimeUtil.timestampToLocalDateTime(request.getApprovedDate().getEnd()));
                }
            }

            // 按申请时间倒序排列
            queryWrapper.orderByDesc(StoreGradeRule::getApplicationTime);

            // 执行分页查询
            Page<StoreGradeRule> page = new Page<>(request.getPageNum(), request.getPageSize());
            Page<StoreGradeRule> resultPage = this.page(page, queryWrapper);

            // 构建响应对象
            StoreGradeRulePageResp response = new StoreGradeRulePageResp();
            response.setPageNum((int) resultPage.getCurrent());
            response.setPageSize((int) resultPage.getSize());
            response.setTotal(resultPage.getTotal());
            response.setTotalPages((int) resultPage.getPages());

            // 转换数据列表
            List<StoreGradeRulePageResp.StoreGradeRuleItem> itemList = resultPage.getRecords().stream()
                    .map(this::convertToPageItem)
                    .collect(Collectors.toList());
            response.setList(itemList);

            log.info("Paging query store grade rules completed, total records: {}", resultPage.getTotal());
            return new CommonApiResponse<>(response);

        } catch (Exception e) {
            log.error("Failed to paging query store grade rules", e);
            return CommonApiResponse.failure(500, e.getMessage());
        }
    }

    /**
     * 将实体对象转换为分页响应项
     *
     * @param entity 实体对象
     * @return 分页响应项
     */
    private StoreGradeRulePageResp.StoreGradeRuleItem convertToPageItem(StoreGradeRule entity) {
        StoreGradeRulePageResp.StoreGradeRuleItem item = new StoreGradeRulePageResp.StoreGradeRuleItem();

        // 复制基本属性
        item.setId(entity.getId());
        item.setChannelType(entity.getChannelType());
        item.setMethod(entity.getMethod() != null ? entity.getMethod() : null); // 兼容处理
        item.setRetailerName(entity.getRetailerName());
        item.setRetailerCode(entity.getRetailerCode());
        item.setSMinCount(entity.getSMinCount() != null ? entity.getSMinCount() : null);
        item.setAMinCount(entity.getAMinCount() != null ? entity.getAMinCount() : null);
        item.setBMinCount(entity.getBMinCount() != null ? entity.getBMinCount() : null);
        item.setCMinCount(entity.getCMinCount() != null ? entity.getCMinCount() : null);
        item.setDMinCount(entity.getDMinCount() != null ? entity.getDMinCount() : null);

        // 设置规则状态
        item.setRuleStatus(entity.getRulesStatus());
        item.setRuleStatusDesc(RuleStatusEnum.getDescByCode(entity.getRulesStatus()));

        // 设置审批状态
        item.setApprovalStatus(entity.getApproveStatus());
        item.setApprovalStatusDesc(ApprovalStatus.getDescByCode(entity.getApproveStatus()));

        // 格式化时间
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'");
        if (entity.getApplicationTime() != null) {
            item.setApplicationTime(entity.getApplicationTime().format(formatter));
        }
        if (entity.getApprovedTime() != null) {
            item.setApprovedTime(entity.getApprovedTime().format(formatter));
        }

        return item;
    }

    /**
     * 转换规则状态
     *
     * @param rulesStatus 规则状态字符串
     * @return 规则状态数字
     */
    private Integer convertRuleStatus(String rulesStatus) {
        if (StrUtil.isBlank(rulesStatus)) {
            return 0;
        }
        switch (rulesStatus.toUpperCase()) {
            case "IN EFFECT":
                return 1;
            case "NOT IN EFFECT":
                return 0;
            default:
                return 0;
        }
    }

    /**
     * 获取规则状态描述
     *
     * @param ruleStatus 规则状态数字
     * @return 规则状态描述
     */
    private String getRuleStatusDesc(Integer ruleStatus) {
        if (ruleStatus == null) {
            return "Unknown";
        }
        switch (ruleStatus) {
            case 1:
                return "In Effect";
            case 0:
                return "Not In Effect";
            default:
                return "Unknown";
        }
    }

    /**
     * 转换审批状态
     *
     * @param approveStatus 审批状态字符串
     * @return 审批状态数字
     */
    private Integer convertApprovalStatus(String approveStatus) {
        if (StrUtil.isBlank(approveStatus)) {
            return 0;
        }
        switch (approveStatus.toUpperCase()) {
            case "APPROVED":
                return 1;
            case "IN APPROVAL":
                return 0;
            case "REJECTED":
                return 2;
            default:
                return 0;
        }
    }

    /**
     * 获取审批状态描述
     *
     * @param approvalStatus 审批状态数字
     * @return 审批状态描述
     */
    private String getApprovalStatusDesc(Integer approvalStatus) {
        if (approvalStatus == null) {
            return "Unknown";
        }
        switch (approvalStatus) {
            case 1:
                return "Approved";
            case 0:
                return "In Approval";
            case 2:
                return "Rejected";
            default:
                return "Unknown";
        }
    }
}
