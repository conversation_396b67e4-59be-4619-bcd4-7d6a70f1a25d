package com.mi.info.intl.retail.org.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.dto.RuleConfigWithPendingInspectionDTO;
import com.mi.info.intl.retail.org.domain.enums.RuleStatusEnum;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.domain.util.BeanConverter;
import com.mi.info.intl.retail.org.infra.entity.RuleConfig;
import com.mi.info.intl.retail.org.infra.mapper.RuleConfigMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.RuleConfigReadMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * 规则配置Repository实现类
 */
@Slf4j
@Repository
public class RuleConfigRepositoryImpl implements RuleConfigRepository {

    @Resource
    private RuleConfigMapper ruleConfigMapper;

    @Autowired
    private RuleConfigReadMapper ruleConfigReadMapper;

    @Override
    public boolean save(RuleConfigDomain ruleConfigDomain) {
        RuleConfig ruleConfig = BeanConverter.INSTANCE.convertToEntity(ruleConfigDomain);
        return ruleConfigMapper.insert(ruleConfig) > 0;
    }

    @Override
    public boolean update(RuleConfigDomain ruleConfigDomain) {
        RuleConfig ruleConfig = BeanConverter.INSTANCE.convertToEntity(ruleConfigDomain);
        return ruleConfigMapper.updateById(ruleConfig) > 0;
    }

    @Override
    public RuleConfigDomain getById(Long id) {
        RuleConfig ruleConfig = ruleConfigReadMapper.selectById(id);
        return BeanConverter.INSTANCE.convertToDomain(ruleConfig);
    }

    @Override
    public RuleConfigDomain getByRuleCode(String ruleCode) {
        LambdaQueryWrapper<RuleConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RuleConfig::getRuleCode, ruleCode);
        RuleConfig ruleConfig = ruleConfigReadMapper.selectOne(queryWrapper);
        return BeanConverter.INSTANCE.convertToDomain(ruleConfig);
    }

    @Override
    public List<RuleConfigDomain> getByRuleCodeList(List<String> ruleCodes) {
        if (CollectionUtils.isEmpty(ruleCodes)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<RuleConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(RuleConfig::getRuleCode, ruleCodes);
        List<RuleConfig> ruleConfigs = ruleConfigReadMapper.selectList(queryWrapper);
        return ruleConfigs.stream().map(BeanConverter.INSTANCE::convertToDomain).collect(Collectors.toList());
    }

    @Override
    public RuleConfigDomain getByTaskBatchId(Long taskBatchId) {
        if (taskBatchId == null) {
            log.warn("查询规则配置时taskBatchId为空");
            return null;
        }
        
        LambdaQueryWrapper<RuleConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(RuleConfig::getTaskBatchId, taskBatchId);
        RuleConfig ruleConfig = ruleConfigReadMapper.selectOne(queryWrapper);
        
        if (ruleConfig == null) {
            log.info("未找到对应的规则配置，taskBatchId: {}", taskBatchId);
            return null;
        }
        
        return BeanConverter.INSTANCE.convertToDomain(ruleConfig);
    }

    @Override
    public List<RuleConfigDomain> findValidRuleConfigs(String country, String region) {
        // 构建查询条件
        LambdaQueryWrapper<RuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        // 国家条件
        if (country != null && !country.isEmpty()) {
            queryWrapper.eq(RuleConfig::getCountry, country);
        }
        
        // 区域条件
        if (region != null && !region.isEmpty()) {
            queryWrapper.eq(RuleConfig::getRegion, region);
        }
        
        // 只查询激活状态的规则
        queryWrapper.eq(RuleConfig::getRuleStatus, RuleStatusEnum.ACTIVE.getCode());
        
        // 查询数据库
        List<RuleConfig> entities = ruleConfigMapper.selectList(queryWrapper);
        
        // 转换为领域对象
        return entities.stream().map(entity -> {
            RuleConfigDomain domain = new RuleConfigDomain();
            BeanUtils.copyProperties(entity, domain);
            return domain;
        }).collect(Collectors.toList());
    }

    @Override
    public List<RuleConfigDomain> findActiveRulesByCountries(List<String> countries) {
        return Collections.emptyList();
    }

    @Override
    public List<RuleConfigWithPendingInspectionDTO> getActiveRulesWithPendingInspections(List<String> countries) {
        log.info("查询国家列表中有效的规则配置和未下发的巡检记录，countries: {}", countries);

        // 调用Mapper查询
        return ruleConfigReadMapper.selectActiveRulesWithPendingInspections(
                countries,
                RuleStatusEnum.ACTIVE.getCode(),
                InspectionStatusEnum.NOT_ISSUED.getCode()
        );
    }

    @Override
    public List<RuleConfigDomain> getByTaskDefineIds(List<Long> taskDefineIds) {
        if (CollectionUtils.isEmpty(taskDefineIds)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<RuleConfig> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(RuleConfig::getTaskDefId, taskDefineIds);
        List<RuleConfig> ruleConfigs = ruleConfigReadMapper.selectList(queryWrapper);
        return ruleConfigs.stream().map(BeanConverter.INSTANCE::convertToDomain).collect(Collectors.toList());
    }

    /**
     * 实体转领域对象
     */
    private RuleConfigDomain convertToDomain(RuleConfig entity) {
        if (entity == null) {
            return null;
        }
        RuleConfigDomain domain = new RuleConfigDomain();
        BeanUtils.copyProperties(entity, domain);
        return domain;
    }

    /**
     * 领域对象转实体
     */
    private RuleConfig convertToEntity(RuleConfigDomain domain) {
        if (domain == null) {
            return null;
        }
        RuleConfig entity = new RuleConfig();
        BeanUtils.copyProperties(domain, entity);
        return entity;
    }
    
    @Override
    public List<RuleConfigDomain> findValidRuleConfigsByAreaId(String areaId) {
        if (areaId == null || areaId.isEmpty()) {
            log.warn("查询规则配置时areaId为空");
            return java.util.Collections.emptyList();
        }
        
        // 构建查询条件
        LambdaQueryWrapper<RuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        // 区域条件
        queryWrapper.eq(RuleConfig::getRegion, areaId);
        
        // 只查询激活状态的规则
        queryWrapper.eq(RuleConfig::getRuleStatus, RuleStatusEnum.ACTIVE.getCode());
        
        // 查询数据库
        List<RuleConfig> entities = ruleConfigReadMapper.selectList(queryWrapper);
        
        // 转换为领域对象
        return entities.stream()
                .map(BeanConverter.INSTANCE::convertToDomain)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<RuleConfigDomain> findValidRuleConfigsByCountry(String country, String taskType) {
        if (country == null || country.isEmpty()) {
            log.warn("查询规则配置时country为空");
            return java.util.Collections.emptyList();
        }
        
        // 构建查询条件
        LambdaQueryWrapper<RuleConfig> queryWrapper = new LambdaQueryWrapper<>();
        
        // 国家条件
        queryWrapper.eq(RuleConfig::getCountry, country);
        
        // 任务类型条件
        if (taskType != null && !taskType.isEmpty()) {
            queryWrapper.eq(RuleConfig::getTaskType, taskType);
        }
        
        // 只查询激活状态的规则
        queryWrapper.eq(RuleConfig::getRuleStatus, RuleStatusEnum.ACTIVE.getCode());
        
        // 查询数据库
        List<RuleConfig> entities = ruleConfigReadMapper.selectList(queryWrapper);
        
        // 转换为领域对象
        return entities.stream()
                .map(BeanConverter.INSTANCE::convertToDomain)
                .collect(Collectors.toList());
    }
}