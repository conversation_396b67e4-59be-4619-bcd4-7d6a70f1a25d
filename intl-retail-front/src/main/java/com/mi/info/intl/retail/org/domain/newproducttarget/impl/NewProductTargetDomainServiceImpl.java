package com.mi.info.intl.retail.org.domain.newproducttarget.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.core.exception.RetailRunTimeException;
import com.mi.info.intl.retail.core.utils.EasyExcelUtil;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduTargetService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetStatisticsDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.ProductLineDto;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.CycleTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.GetUserInfoRespForAuth;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProdcutTargetMetaReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProdcutTargetMetaResp;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetAddReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetImportReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetItem;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectResp;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetUpdateReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums.GetUserInfoRespForAuthTheGlobal;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums.NewProductStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.enums.NewProductTargetTypeEnum;
import com.mi.info.intl.retail.ldu.infra.mapper.IntlLduTargetMapper;
import com.mi.info.intl.retail.ldu.infra.repository.IProductQueryService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.RmsSyncDbManager;
import com.mi.info.intl.retail.org.domain.newproducttarget.NewProductTargetDomainService;
import com.mi.info.intl.retail.org.domain.newproducttarget.listener.NewProductTargetImportListener;
import com.mi.info.intl.retail.org.domain.util.FileUtil;
import com.mi.info.intl.retail.org.infra.entity.IntlNewProductTarget;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.IntlNewProductTargetMapper;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import com.xiaomi.cnzone.storems.common.exception.BusinessException;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.validation.ConstraintViolation;
import javax.validation.Valid;
import javax.validation.Validator;
import java.io.File;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * @author: chuang
 * @since: 2025/8/1
 */
@Slf4j
@Service
public class NewProductTargetDomainServiceImpl implements NewProductTargetDomainService {
    
    @Autowired
    IntlNewProductTargetMapper intlNewProductTargetMapper;
    
    @Resource
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;
    
    //    @NacosValue(value = "${intelTemple-url.newProductTarget:}", autoRefreshed = true)
    //    private String targetUploadUrl;
    @Resource
    private EasyExcelUtil easyExcelUtil;
    
    @Resource
    private FdsService fdsService;
    
    @Resource
    private IntlLduTargetMapper intlLduTargetMapper;
    
    @Resource
    private IntlLduTargetService intlLduTargetService;
    
    @Autowired
    private IProductQueryService productLineQueryService;
    
    @Autowired
    RmsSyncDbManager rmsSyncDbManager;
    
    @DubboReference(group = "${organization.dubbo-group}", interfaceClass = UserProvider.class, check = false, timeout = 5000)
    private UserProvider userProvider;
    
    @Resource
    private Validator validator;
    
    /**
     * 主要的业务逻辑会在这里
     */
    @Override
    public Page<NewProductTargetItem> listNewProductTarget(NewProductTargetReq request) {
        log.info("NewProductTargetDomainServiceImpl#listNewProductTarget request:{}", RetailJsonUtil.toJson(request));
        Page<IntlNewProductTarget> page = new Page<>(request.getPageNum(), request.getPageSize());
        Page<NewProductTargetItem> resultPage = new Page<>(request.getPageNum(), request.getPageSize());
        LambdaQueryWrapper<IntlNewProductTarget> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(!CollUtil.isEmpty(request.getProductLines()), IntlNewProductTarget::getProductLine,
                request.getProductLines());
        //        queryWrapper.in(!CollUtil.isEmpty(request.getCategoryCode()), IntlNewProductTarget::getCategory,
        //                request.getCategoryCode());
        //        queryWrapper.in(!CollUtil.isEmpty(request.getSeriess()), IntlNewProductTarget::getSeries, request.getSeriess());
        queryWrapper.in(!CollUtil.isEmpty(request.getProjects()), IntlNewProductTarget::getProject,
                request.getProjects());
        queryWrapper.eq(StringUtils.isNotBlank(request.getStatus()), IntlNewProductTarget::getStatus,
                request.getStatus());
        queryWrapper.eq(StringUtils.isNotBlank(request.getTargetType()), IntlNewProductTarget::getTargetType,
                request.getTargetType());
        queryWrapper.in(!CollUtil.isEmpty(request.getCountryCode()), IntlNewProductTarget::getCountry,
                request.getCountryCode());
        queryWrapper.orderByDesc(IntlNewProductTarget::getCreatedTime);
        page.setSearchCount(request.isSearchCount());
        page = intlNewProductTargetMapper.selectPage(page, queryWrapper);
        List<NewProductTargetItem> newProductTargetItemList = Convert.toList(NewProductTargetItem.class,
                page.getRecords());
        for (NewProductTargetItem sub : newProductTargetItemList) {
            String areaId = sub.getCountry();
            sub.setStatusDesc(NewProductStatusEnum.getName(sub.getStatus()));
            sub.setTargetTypeDesc(NewProductTargetTypeEnum.getNameByCode(sub.getTargetType()));
            sub.setStartTimeString(
                    IntlTimeUtil.parseTimestampToAreaTime(areaId, sub.getStartTime(), IntlTimeUtil.FORMATTER_YYYYMMDD));
            sub.setEndTimeString(
                    IntlTimeUtil.parseTimestampToAreaTime(areaId, sub.getEndTime(), IntlTimeUtil.FORMATTER_YYYYMMDD));
        }
        resultPage.setTotal(page.getTotal());
        resultPage.setRecords(newProductTargetItemList);

        return resultPage;
    }
    
    @Override
    public NewProductTargetItem getNewProductTargetItemById(Long id) {
        log.info("NewProductTargetDomainServiceImpl#getNewProductTargetItemById id:{}", RetailJsonUtil.toJson(id));
        IntlNewProductTarget intlNewProductTarget = intlNewProductTargetMapper.selectById(id);
        NewProductTargetItem result = Convert.convert(NewProductTargetItem.class, intlNewProductTarget);
        
        return result;
    }
    
    @Transactional
    public CommonApiResponse<List<String>> addNewProductTarget(@Valid NewProductTargetAddReq req) {
        log.info("NewProductTargetDomainServiceImpl#addNewProductTarget req:{}", RetailJsonUtil.toJson(req));
        validateRequiredFields(req);
        String mid = RpcContext.getContext().getAttachments().get("$upc_miID");
        String mname = RpcContext.getContext().getAttachments().get("$upc_userName");
        //权限判断
        GetUserInfoRespForAuth userInfoAuth = judgeUser();
        if (!GetUserInfoRespForAuthTheGlobal.GLOBAL.getCode().equals(userInfoAuth.getTheGlobal())) {
            throw new BusinessException("Only GLOBAL users can add new product targets");
        }
        CommonApiResponse apiResponse = null;
        apiResponse = new CommonApiResponse(null);
        IntlNewProductTarget addTmp = null;
        List<String> data = new LinkedList<>();
        List<IntlNewProductTarget> addListTmp = new LinkedList<>();
        List<IntlNewProductTarget> canAddListTmp = new LinkedList<>();
        long curr = System.currentTimeMillis();
        LambdaQueryWrapper<IntlRmsCountryTimezone> queryRms = new LambdaQueryWrapper<>();
        queryRms.in(IntlRmsCountryTimezone::getCountryCode, req.getCountryCode());
        List<IntlRmsCountryTimezone> listRms = intlRmsCountryTimezoneMapper.selectList(queryRms);
        Map<String, IntlRmsCountryTimezone> msRms = listRms.stream()
                .collect(Collectors.toMap(IntlRmsCountryTimezone::getCountryCode, x -> x, (o, n) -> o));
        
        IntlRmsCountryTimezone rmsForArea = null;
        ProductLineDto productLineDto = null;
        //拆分-组装数据
        for (String productLine : req.getProductLines()) {
            productLineDto = productLineQueryService.getProductLineById(productLine);
            for (String project : req.getProjects()) {
                for (String country : req.getCountryCode()) {
                    addTmp = new IntlNewProductTarget();
                    addTmp.setStatus(NewProductStatusEnum.DONE.getCode());
                    BeanUtil.copyProperties(req, addTmp);
                    addTmp.setCanModifyStartTime(req.getCanModifyStartTime());
                    addTmp.setCanModifyEndTime(req.getCanModifyEndTime());
                    addTmp.setCategory(req.getCategoryCode());
                    addTmp.setCategoryName(req.getCategoryName());
                    addTmp.setProductLine(productLine);
                    if (productLineDto != null) {
                        addTmp.setProductLineName(productLineDto.getEnName());
                    }
                    
                    addTmp.setProject(project);
                    addTmp.setCountry(country);
                    addTmp.setCreatedTime(curr);
                    addTmp.setCreatedBy(mid);
                    addTmp.setCreatedName(mname);
                    rmsForArea = msRms.get(country);
                    if (rmsForArea != null) {
                        addTmp.setRegion(rmsForArea.getAreaCode());
                        addTmp.setRegionName(rmsForArea.getArea());
                        addTmp.setCountryName(rmsForArea.getCountryName());
                    }
                    addTmp.setStartTime(null);
                    addTmp.setEndTime(null);
                    addListTmp.add(addTmp);
                    
                }
            }
        }
        //查询 看看哪些已经存在
        List<IntlNewProductTarget> exists = intlNewProductTargetMapper.selectListByConditionForExists(addListTmp, null);
        if (exists.size() > 0) { //只要存在1条就不能新增
            for (IntlNewProductTarget e : exists) {
                data.add(String.format("projectLine %s; project: %s; country: %s; already existed", e.getProductLine(),
                        e.getProject(), e.getCountry()));
            }
        } else {
            fillLduStatistics(addListTmp);
            //进行目标类型的扩展,然后批量新增
            for (IntlNewProductTarget sub : addListTmp) {
                sub.setTargetType(NewProductTargetTypeEnum.FIRST.getCode());
                if (sub.getLduPlanCount() == null) {
                    sub.setLduPlanCount(0);
                }
                if (sub.getLduStoreCoverage() == null) {
                    sub.setLduStoreCoverage(0);
                }
                IntlNewProductTarget sub1 = new IntlNewProductTarget();
                BeanUtil.copyProperties(sub, sub1);
                sub1.setTargetType(NewProductTargetTypeEnum.LIFE_CIRCLE.getCode());
                sub1.setStartTime(null);
                sub1.setEndTime(null);
                canAddListTmp.add(sub);
                canAddListTmp.add(sub1);
            }
            intlNewProductTargetMapper.batchInsert(canAddListTmp);
        }
        apiResponse = new CommonApiResponse(data);
        return apiResponse;
    }
    
    /**
     * 填充LDU统计数据到新品目标列表中
     *
     * @param records 新品目标记录列表
     */
    private void fillLduStatistics(List<IntlNewProductTarget> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        
        try {
            // 调用LDU目标服务获取所有统计数据
            CommonApiResponse<List<IntlLduTargetStatisticsDto>> statisticsResponse = intlLduTargetService.getAllStatistics();
            
            if (statisticsResponse == null || statisticsResponse.getCode() != 0 || CollUtil.isEmpty(
                    statisticsResponse.getData())) {
                log.warn("获取LDU统计数据失败或数据为空");
                return;
            }
            
            List<IntlLduTargetStatisticsDto> statisticsList = statisticsResponse.getData();
            
            // 创建统计数据映射，key为 country_project
            Map<String, IntlLduTargetStatisticsDto> statisticsMap = statisticsList.stream().collect(
                    Collectors.toMap(stat -> stat.getCountryCode() + "_" + stat.getProjectCode(), stat -> stat,
                            (existing, replacement) -> existing));
            
            // 遍历新品目标记录，填充LDU统计数据
            for (IntlNewProductTarget item : records) {
                if (item.getCountry() != null && item.getProject() != null) {
                    String key = item.getCountry() + "_" + item.getProject();
                    IntlLduTargetStatisticsDto statistics = statisticsMap.get(key);
                    
                    if (statistics != null) {
                        // 填充LDU计划数（目标样机总数）
                        if (statistics.getTotalTargetSampleOut() != null) {
                            item.setLduPlanCount(statistics.getTotalTargetSampleOut().intValue());
                        }
                        
                        // 填充LDU覆盖门店数（目标覆盖门店总数）
                        if (statistics.getTotalTargetCoveredStores() != null) {
                            item.setLduStoreCoverage(statistics.getTotalTargetCoveredStores().intValue());
                        }
                    }
                }
            }
            
            log.info("成功填充LDU统计数据到{}条新品目标记录", records.size());
            
        } catch (Exception e) {
            log.error("填充LDU统计数据时发生异常", e);
        }
    }
    
    @Override
    @Transactional
    public CommonApiResponse<String> updateNewProductTarget(@Valid NewProductTargetUpdateReq req) {
        log.info("NewProductTargetDomainServiceImpl#updateNewProductTarget req:{}", RetailJsonUtil.toJson(req));
        validateRequiredFields(req);
        CommonApiResponse apiResponse = null;
        apiResponse = new CommonApiResponse("");
        long curr = System.currentTimeMillis();
        IntlNewProductTarget upTmp = null;
        String mid = RpcContext.getContext().getAttachments().get("$upc_miID");
        String mname = RpcContext.getContext().getAttachments().get("$upc_userName");
        //权限判断
        GetUserInfoRespForAuth userInfoAuth = judgeUser();
        if (GetUserInfoRespForAuthTheGlobal.NO_INFO.getCode().equals(userInfoAuth.getTheGlobal())) {
            throw new BusinessException("Unable to query valid user organization information");
        }
        for (NewProductTargetUpdateReq.NewProductTargetUpdateItemReq upSub : req.getItems()) {
            upTmp = Convert.convert(IntlNewProductTarget.class, upSub);
            if (GetUserInfoRespForAuthTheGlobal.NO_GLOBAL.getCode().equals(userInfoAuth.getTheGlobal())) {
                //判断是否有权限修改这条记录
                if (!userInfoAuth.getThePosition().contains(upTmp.getCountry())) {
                    throw new BusinessException(
                            "User does not have permission to modify data for Country: " + upTmp.getCountry());
                }
                // 这里来个判断 如果curr 不在允许修改的时间范围内，直接跳过
                if ((upTmp.getCanModifyStartTime() != null && curr < upTmp.getCanModifyStartTime()) || (
                        upTmp.getCanModifyEndTime() != null && curr > upTmp.getCanModifyEndTime())) {
                    //这里来个英文日志 说明当前时间不在允许修改的时间范围内
                    log.warn("newProductTarget id: {} ;Current time is not within the allowed modification time range",
                            upTmp.getId());
                    throw new BusinessException("Current time is not within the allowed modification time range");
                    //                continue;
                }
                
            }
            upTmp.setProductLine(null);
            upTmp.setProject(null);
            upTmp.setRegion(null);
            upTmp.setRegionName(null);
            upTmp.setCountry(null);
            upTmp.setCountryName(null);
            upTmp.setLduPlanCount(null);
            upTmp.setLduStoreCoverage(null);
            if (NewProductTargetTypeEnum.LIFE_CIRCLE.getCode().equals(upTmp.getTargetType())) {
                upTmp.setStartTime(null);
                upTmp.setEndTime(null);
            }
            upTmp.setTargetType(null);
            upTmp.setUpdatedTime(curr);
            upTmp.setUpdatedBy(mid);
            upTmp.setUpdatedName(mname);
            if (GetUserInfoRespForAuthTheGlobal.NO_GLOBAL.getCode().equals(userInfoAuth.getTheGlobal())) {
                upTmp.setNeedCheckTime("Y");
            }
            upTmp.setStatus(NewProductStatusEnum.MOIDFY.getCode());
            intlNewProductTargetMapper.updateNumData(upTmp);
        }
        return apiResponse;
    }
    
    @Override
    public CommonApiResponse<List<NewProdcutTargetMetaResp>> listMeta(NewProdcutTargetMetaReq req) {
        log.info("NewProductTargetDomainServiceImpl#listMeta req:{}", RetailJsonUtil.toJson(req));
        CommonApiResponse apiResponse = null;
        List<IntlNewProductTarget> resultTmp = intlNewProductTargetMapper.listMeta(req);
        List<NewProdcutTargetMetaResp> result = Convert.toList(NewProdcutTargetMetaResp.class, resultTmp);
        apiResponse = new CommonApiResponse(result);
        return apiResponse;
    }
    
    @Override
    @Transactional
    public CommonApiResponse<String> importNewProdcutTargetMetaResp(NewProductTargetImportReq req) {
        log.info("NewProductTargetDomainServiceImpl#importNewProdcutTargetMetaResp req:{}", RetailJsonUtil.toJson(req));
        if (StringUtils.isBlank(req.getUrl())) {
            log.error("文件URL不能为空");
            throw new RuntimeException("文件URL不能为空");
        }
        GetUserInfoRespForAuth userInfoAuth = judgeUser();
        if (GetUserInfoRespForAuthTheGlobal.NO_INFO.getCode().equals(userInfoAuth.getTheGlobal())) {
            throw new BusinessException("Unable to query valid user organization information");
        }
        CommonApiResponse apiResponse = null;
        File inputFile = null;
        String random = UUID.randomUUID().toString();
        try {
            long curr = System.currentTimeMillis();
            String mid = RpcContext.getContext().getAttachments().get("$upc_miID");
            String mname = RpcContext.getContext().getAttachments().get("$upc_userName");
            
            // 1.读取URL数据
            inputFile = FileUtil.getFileFromUrl(req.getUrl(), random);
            
            //2. 解析Excel文件
            NewProductTargetImportListener importSnBlackListener = new NewProductTargetImportListener(
                    intlNewProductTargetMapper, mid, mname, curr, fdsService, rmsSyncDbManager, userInfoAuth);
            
            // 3.读取Excel文件
            easyExcelUtil.readFromStream(Files.newInputStream(inputFile.toPath()),
                    NewProductTargetImportListener.NewProductTargetImportData.class, importSnBlackListener, 1);
            apiResponse = new CommonApiResponse(importSnBlackListener.getErrorDataFileUrl());
            if (!"ok".equals(importSnBlackListener.getErrorDataFileUrl())) {
                apiResponse.setCode(-1);
            }
            // 4.返回异常数据文件URL
            return apiResponse;
            
        } catch (Exception e) {
            log.error("导入新品目标错误: {}", e.getMessage(), e);
            throw new RuntimeException("导入新品目标错误: " + e.getMessage());
        } finally {
            // 清理临时文件
            if (inputFile != null && inputFile.exists()) {
                inputFile.delete();
            }
        }
    }
    
    @Override
    public List<NewProductTargetProjectResp> listProject(NewProductTargetProjectReq req) {
        log.info("NewProductTargetDomainServiceImpl#listProject req:{}", RetailJsonUtil.toJson(req));
        List<NewProductTargetProjectResp> resultTmp = intlNewProductTargetMapper.listProject(req);
        return resultTmp;
    }
    
    private void validateRequiredFields(Object dto) {
        validatorHandler(validator.validate(dto));
    }
    
    /**
     * 抛出Validator处理的校验
     */
    private <T> void validatorHandler(Set<ConstraintViolation<T>> validate) {
        if (CollUtil.isNotEmpty(validate)) {
            throw new RetailRunTimeException(validate.iterator().next().getMessage());
        }
    }
    
    public GetUserInfoRespForAuth judgeUser() {
        //        UserInfo userContext = UserInfoUtil.getUserContext();
        GetUserInfoRespForAuth getUserInfoRespForAuth = new GetUserInfoRespForAuth();
        String areaId = RequestContextInfo.getAreaId();
        RpcContext.getContext().setAttachment("$area_id", "GLOBAL");
        if (StringUtils.isBlank(areaId)) {
            areaId = RpcContext.getContext().getAttachments().get("$area_id");
            if (StringUtils.isBlank(areaId)) {
                throw new BusinessException("areaId is empty , Unable to query valid user organization information");
            }
        }
        getUserInfoRespForAuth.setTheGlobal(areaId.equalsIgnoreCase(GetUserInfoRespForAuthTheGlobal.GLOBAL.toString())
                ? GetUserInfoRespForAuthTheGlobal.GLOBAL.getCode()
                : GetUserInfoRespForAuthTheGlobal.NO_GLOBAL.getCode());
        if (areaId.equalsIgnoreCase(GetUserInfoRespForAuthTheGlobal.GLOBAL.toString())) {
            getUserInfoRespForAuth.setThePosition(new HashSet<>());
        } else {
            getUserInfoRespForAuth.setThePosition(
                    Arrays.asList(areaId).stream().map(String::trim).collect(Collectors.toSet()));
        }
        
        //        GetUserInfoReq getUserInfoReq = new GetUserInfoReq();
        //        getUserInfoReq.setMiId(userContext.getMiID());
        //        getUserInfoReq.setUserInfoFromDb(true);
        //        getUserInfoReq.setFilterList(Arrays.asList(ApiFilterEnum.EFFECTIVE_POSITION.getCode()));
        //        Result<GetUserInfoResp> userInfoRespResult = userProvider.getUserInfo(getUserInfoReq);
        //        GetUserInfoResp userInfoResp = userInfoRespResult.getData();
        //        if (userInfoResp != null) {
        //            getUserInfoRespForAuth.setMiId(userInfoResp.getMiId());
        //            getUserInfoRespForAuth.setUserName(userInfoResp.getName());
        //            // 检查是否存在 ORGAN_HEADQUARTERS 类型的岗位
        //            boolean isHeadquarters = false;
        //            if (userInfoResp != null && userInfoResp.getPositionList() != null) {
        //                isHeadquarters = userInfoResp.getPositionList().stream().filter(Objects::nonNull) // 过滤掉position为null的情况
        //                        .anyMatch(position -> position.getOrganType() != null
        //                                && OrganTypeEnums.ORGAN_HEADQUARTERS.getCode().equals(position.getOrganType()));
        //            }
        //            getUserInfoRespForAuth.setTheGlobal(isHeadquarters ? GetUserInfoRespForAuthTheGlobal.GLOBAL.getCode()
        //                    : GetUserInfoRespForAuthTheGlobal.NO_GLOBAL.getCode());
        //            getUserInfoRespForAuth.setThePosition(
        //                    CollUtils.mappingToSet(userInfoResp.getPositionList(), Position::getAreaId));
        //        }
        log.info("NewProductTargetDomainServiceImpl#judgeUser getUserInfoRespForAuth:{}",
                RetailJsonUtil.toJson(getUserInfoRespForAuth));
        return getUserInfoRespForAuth;
    }
}