package com.mi.info.intl.retail.org.app.service.material.convert;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationInfoVO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorMap;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.StoreMaterialStatusCoveredEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialInspectionStatus;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.StoreLimitedRangeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.org.domain.IntlStoreMaterialStatusDomain;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.infra.entity.InspectionRecord;
import com.mi.info.intl.retail.org.infra.entity.IntlStoreMaterialStatus;
import com.mi.info.intl.retail.org.infra.entity.RuleConfig;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper
public interface NewProductInspectionConvert {

    NewProductInspectionConvert INSTANCE = Mappers.getMapper(NewProductInspectionConvert.class);

    @Mapping(source = "posmMaterials", target = "posmMaterials", qualifiedByName = "stringToList")
    @Mapping(source = "assignedStore", target = "assignedStore", qualifiedByName = "arrayToList")
    @Mapping(source = "project", target = "project", qualifiedByName = "arrayToList")
    @Mapping(source = "storeType", target = "storeType", qualifiedByName = "arrayToList")
    InspectionTaskConfigurationInfoVO ruleConfigToInspectionTaskConfigurationInfoVO(RuleConfig ruleConfig);

    @Mapping(source = "posmMaterials", target = "posmMaterials", qualifiedByName = "listToString")
    @Mapping(source = "assignedStore", target = "assignedStore", qualifiedByName = "listToArray")
    @Mapping(source = "project", target = "project", qualifiedByName = "listToArray")
    @Mapping(source = "storeType", target = "storeType", qualifiedByName = "listToArray")
    RuleConfig inspectionTaskConfigurationDTOToRuleConfig(InspectionTaskConfigurationDTO dto);

    @Mapping(source = "posmMaterials", target = "posmMaterials", qualifiedByName = "stringToList")
    @Mapping(source = "assignedStore", target = "assignedStore", qualifiedByName = "arrayToList")
    @Mapping(source = "project", target = "project", qualifiedByName = "arrayToList")
    @Mapping(source = "storeType", target = "storeType", qualifiedByName = "arrayToList")
    InspectionTaskConfigurationDTO ruleConfigToInspectionTaskConfigurationDTO(RuleConfig ruleConfig);


    @Named("arrayToList")
    default List<String> arrayToList(String[] value) {
        if (value == null) {
            return new ArrayList<>();
        }
        return Lists.newArrayList(value);
    }

    @Named("listToArray")
    default String[] listToArray(List<String> value) {
        if (CollUtil.isEmpty(value)) {
            return new String[0];
        }
        return value.toArray(new String[0]);
    }

    @Named("stringToList")
    default List<String> stringToList(String value) {
        if (CharSequenceUtil.isBlank(value)) {
            return new ArrayList<>();
        }
        JSONObject jsonObject = JSON.parseObject(value);
        JSONArray posmMaterials = jsonObject.getJSONArray("posmMaterials");
        if (posmMaterials != null) {
            return posmMaterials.toJavaList(String.class);
        }
        return new ArrayList<>();
    }

    @Named("listToString")
    default String listToString(List<String> value) {
        if (CollUtil.isEmpty(value)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("posmMaterials", JSON.toJSON(value));
        return jsonObject.toJSONString();
    }

    @Mapping(source = "taskStatus", target = "taskStatus", qualifiedByName = "mapToTaskStausEnum")
    @Mapping(source = "inspectionStatus", target = "inspectionStatus", qualifiedByName = "mapToMaterialInspectionStatus")
    @Mapping(source = "storeLimitedRange", target = "storeLimitedRange", qualifiedByName = "mapToStoreLimitedRangeEnum")
    MaterialInspectionDomain entityToDomain(InspectionRecord entity);
    List<MaterialInspectionDomain> entityToDomainList(List<InspectionRecord> entity);
    InspectionRecord domainToEntity(MaterialInspectionDomain domain);

    IntlStoreMaterialStatus domainToEntity(IntlStoreMaterialStatusDomain domain);

    @Mapping(source = "covered", target = "covered", qualifiedByName = "mapToCoveredEnum")
    IntlStoreMaterialStatusDomain entityToDomain(IntlStoreMaterialStatus entity);
   
    @Named("mapToTaskStausEnum")
    default TaskStatusEnum mapToTaskStausEnum(Integer value) {
        return I18nDesc.getByCode(TaskStatusEnum.class, value);
    }

    @Named("mapToMaterialInspectionStatus")
    default MaterialInspectionStatus mapToMaterialInspectionStatus(Integer value) {
        return I18nDesc.getByCode(MaterialInspectionStatus.class, value);
    }

    @Named("mapToStoreLimitedRangeEnum")
    default StoreLimitedRangeEnum mapToStoreLimitedRangeEnum(Integer value) {
        return I18nDesc.getByCode(StoreLimitedRangeEnum.class, value);
    }

    @Named("mapToCoveredEnum")
    default StoreMaterialStatusCoveredEnum mapToCoveredEnum(Integer value) {
        return I18nDesc.getByCode(StoreMaterialStatusCoveredEnum.class, value);
    }


    default Integer map(TaskStatusEnum value) {
        return I18nDesc.safeGetCode(value);
    }

    default Integer map(MaterialInspectionStatus value) {
        return I18nDesc.safeGetCode(value);
    }

    default Integer map(StoreLimitedRangeEnum value) {
        return I18nDesc.safeGetCode(value);
    }

    default Integer map(StoreMaterialStatusCoveredEnum value) {
        return I18nDesc.safeGetCode(value);
    }

    default Integer map(Boolean value) {
        if (value == null) {
            return null;
        }
        return value ? 1 : 0;
    }

    default Boolean map(Integer value) {
        if (value == null) {
            return null;
        }
        return value == 1;
    }

    MaterialInspectionRecordSelectorMap mapToSelectorMap(MaterialInspectionRecordSelectorItem record);

    default <T extends Serializable> Map<String, String> map(List<OptionalItem<T>> value) {
        if (value == null || value.isEmpty()) {
            return new HashMap<>();
        }
        return value.stream().collect(Collectors.toMap(e -> String.valueOf(e.getKey()), OptionalItem::getValue));
    }
}
