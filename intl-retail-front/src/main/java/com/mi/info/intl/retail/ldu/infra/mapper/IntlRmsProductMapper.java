package com.mi.info.intl.retail.ldu.infra.mapper;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.constant.DBTypeRoutingKey;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品信息查询接口
 *
 * <AUTHOR>
 * @date 2025/7/11 17:42
 */
@DS(DBTypeRoutingKey.XMSTORE_BE)
@Mapper
public interface IntlRmsProductMapper extends BaseMapper<IntlRmsProduct> {

    /**
     * 根据产品ID列表批量查询产品信息
     *
     * @param productIds 产品ID列表
     * @return 产品信息列表
     */
    List<IntlRmsProduct> selectByProductIds(@Param("productIds") List<Integer> productIds);

}
