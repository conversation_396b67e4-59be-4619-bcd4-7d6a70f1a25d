package com.mi.info.intl.retail.org.app.service.newproducttarget;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ArrayUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.nacos.api.config.annotation.NacosValue;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.mi.info.intl.retail.advice.excel.export.ExportExcelAspect;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.cooperation.downloadcenter.example.JobTriggerHelper;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduReportLogDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduTargetService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduTargetStatisticsDto;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.NewProductTargetService;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProdcutTargetMetaReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProdcutTargetMetaResp;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetAddReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetImportReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetItem;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetProjectResp;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetReq;
import com.mi.info.intl.retail.intlretail.service.api.newproducttarget.dto.NewProductTargetUpdateReq;
import com.mi.info.intl.retail.ldu.dto.IntlLduReportExport;
import com.mi.info.intl.retail.ldu.dto.excel.IntlLduEnReportExport;
import com.mi.info.intl.retail.ldu.enums.ExportExcelEnum;
import com.mi.info.intl.retail.ldu.enums.LanguageEnum;
import com.mi.info.intl.retail.ldu.enums.ResultCodeEnum;
import com.mi.info.intl.retail.ldu.infra.entity.IntlLduTarget;
import com.mi.info.intl.retail.ldu.util.ConstantMessageTemplate;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.newproducttarget.NewProductTargetDomainService;
import com.xiaomi.cnzone.proretail.newcommon.util.RetailJsonUtil;
import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.keycenter.okhttp3.OkHttpClient;
import com.xiaomi.keycenter.okhttp3.Request;
import com.xiaomi.keycenter.okhttp3.Response;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.dubbo.rpc.RpcContext;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Map;
import java.util.stream.Collectors;
import javax.validation.Valid;

/**
 * 新品目标实现
 *
 * @author: chuang
 * @since: 2025/8/1
 */
@Slf4j
@Service
@DubboService(group = "${material.dubbo.group:}", interfaceClass = NewProductTargetService.class)
@ApiModule(value = "新品目标实现实现", apiInterface = NewProductTargetService.class)
public class NewProductTargetServiceImpl implements NewProductTargetService {
    
    @Resource
    private NewProductTargetDomainService newProductTargetDomainService;
    
    @Resource
    private IntlLduTargetService intlLduTargetService;
    
    /**
     * 这层 不会有复杂的业务逻辑 - 这一层不会包含复杂的业务逻辑，而是对领域层进行协调，对业务逻辑进行编排;
     */
    @Resource
    private FdsService fdsService;
    
    @Resource
    JobTriggerHelper jobTriggerHelper;
    
    @NacosValue(value = "${intelTemple-url.newProductTarget:}", autoRefreshed = true)
    private String targetUploadUrl;
    
    @Override
    public PageResponse<NewProductTargetItem> listNewProductTarget(NewProductTargetReq request) {
        log.info("NewProductTargetServiceImpl#listNewProductTarget request:{}", RetailJsonUtil.toJson(request));
        //判断到底是list查询,还是用于修改的
        Page<NewProductTargetItem> resultPage = new Page<>();
        
        if (request.getId() == null) {
            resultPage = newProductTargetDomainService.listNewProductTarget(request);
        } else {
            NewProductTargetItem item = newProductTargetDomainService.getNewProductTargetItemById(request.getId());
            if (item != null) {
                NewProductTargetReq req = new NewProductTargetReq();
                //                req.setCategoryCode(Arrays.asList(item.getCategory()));
                //                req.setSeriess(Arrays.asList(item.getSeries()));
                req.setProductLines(Arrays.asList(item.getProductLine()));
                req.setCountryCode(Arrays.asList(item.getCountry()));
                resultPage = newProductTargetDomainService.listNewProductTarget(req);
            }
        }
        
        // 获取LDU统计数据并填充到结果中
        //        fillLduStatistics(resultPage.getRecords());
        
        return new PageResponse<>(resultPage.getTotal(), request.getPageNum(), request.getPageSize(),
                resultPage.getRecords());
    }
    
    /**
     * 填充LDU统计数据到新品目标列表中
     *
     * @param records 新品目标记录列表
     */
    private void fillLduStatistics(List<NewProductTargetItem> records) {
        if (CollUtil.isEmpty(records)) {
            return;
        }
        
        try {
            // 调用LDU目标服务获取所有统计数据
            CommonApiResponse<List<IntlLduTargetStatisticsDto>> statisticsResponse = intlLduTargetService.getAllStatistics();
            
            if (statisticsResponse == null || statisticsResponse.getCode() != 0 || CollUtil.isEmpty(
                    statisticsResponse.getData())) {
                log.warn("获取LDU统计数据失败或数据为空");
                return;
            }
            
            List<IntlLduTargetStatisticsDto> statisticsList = statisticsResponse.getData();
            
            // 创建统计数据映射，key为 country_project
            Map<String, IntlLduTargetStatisticsDto> statisticsMap = statisticsList.stream().collect(
                    Collectors.toMap(stat -> stat.getCountryCode() + "_" + stat.getProjectCode(), stat -> stat,
                            (existing, replacement) -> existing));
            
            // 遍历新品目标记录，填充LDU统计数据
            for (NewProductTargetItem item : records) {
                if (item.getCountry() != null && item.getProject() != null) {
                    String key = item.getCountry() + "_" + item.getProject();
                    IntlLduTargetStatisticsDto statistics = statisticsMap.get(key);
                    
                    if (statistics != null) {
                        // 填充LDU计划数（目标样机总数）
                        if (statistics.getTotalTargetSampleOut() != null) {
                            item.setLduPlanCount(statistics.getTotalTargetSampleOut().intValue());
                        }
                        
                        // 填充LDU覆盖门店数（目标覆盖门店总数）
                        if (statistics.getTotalTargetCoveredStores() != null) {
                            item.setLduStoreCoverage(statistics.getTotalTargetCoveredStores().intValue());
                        }
                    }
                }
            }
            
            log.info("成功填充LDU统计数据到{}条新品目标记录", records.size());
            
        } catch (Exception e) {
            log.error("填充LDU统计数据时发生异常", e);
        }
    }
    
    @Override
    public CommonApiResponse<PageResponse<NewProductTargetItem>> listNewProductTargetGateWay(NewProductTargetReq req) {
        log.info("NewProductTargetServiceImpl#listNewProductTargetGateWay req:{}", RetailJsonUtil.toJson(req));
        return new CommonApiResponse<>(listNewProductTarget(req));
    }
    
    @Override
    public CommonResponse<String> listNewProductTargetExport(NewProductTargetReq query) {
        log.info("NewProductTargetServiceImpl#listNewProductTargetExport query:{};context:{}",
                RetailJsonUtil.toJson(query), JSONUtil.toJsonStr(RpcContext.getContext()));
        String upcAccount = RpcContext.getContext().getAttachment("$upc_account");
        
        String language = RpcContext.getContext().getAttachment("$language");
        
        File tempFile = null;
        ExcelWriter excelWriter = null;
        try {
            tempFile = File.createTempFile(ExportExcelEnum.NEW_PRODUCT_TARGET_REPORT.getExcelEnName(), ".xlsx");
            
            if (LanguageEnum.EN_US.getCode().equals(language)) {
                excelWriter = EasyExcel.write(tempFile, NewProductTargetItem.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            } else {
                excelWriter = EasyExcel.write(tempFile, NewProductTargetItem.class)
                        .registerWriteHandler(new ExportExcelAspect.AdaptiveWidthStyleStrategy()).build();
            }
            
            WriteSheet writeSheet = EasyExcel.writerSheet(ExportExcelEnum.NEW_PRODUCT_TARGET_REPORT.getExcelEnName())
                    .build();
            
            long pageSize = 500L;
            long currentPage = 1L;
            boolean hasNext = true;
            boolean searchCount = true;
            Long pageTotal = null;
            while (hasNext) {
                query.setPageNum(currentPage);
                query.setPageSize(pageSize);
                
                query.setSearchCount(searchCount);
                if (searchCount) {
                    searchCount = false;
                }
                PageResponse<NewProductTargetItem> pageCommonResponse = this.listNewProductTarget(query);
                if (pageTotal == null) {
                    pageTotal = pageCommonResponse.getTotalCount();
                }
                List<NewProductTargetItem> records = pageCommonResponse.getList();
                if (CollUtil.isEmpty(records)) {
                    hasNext = false;
                    continue;
                }
                //                if (LanguageEnum.EN_US.getCode().equals(language)) {
                //                    List<IntlLduEnReportExport> intlLduEnReportExports = ComponentLocator.getConverter()
                //                            .convertList(records, IntlLduEnReportExport.class);
                //                    excelWriter.write(intlLduEnReportExports, writeSheet);
                //                } else {
                //                    List<IntlLduReportExport> intlLduReportExports = ComponentLocator.getConverter()
                //                            .convertList(records, IntlLduReportExport.class);
                //                    excelWriter.write(intlLduReportExports, writeSheet);
                //                }
                excelWriter.write(records, writeSheet);
                hasNext = currentPage * pageSize < pageTotal;
                currentPage++;
            }
            
            if (excelWriter != null) {
                excelWriter.finish();
            }
            
            if (currentPage == 1L) {
                return CommonResponse.failure(ResultCodeEnum.DATA_MISS.getCode(), ResultCodeEnum.DATA_MISS.getEnMsg());
            }
            
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            
            FdsUploadResult upload = fdsService.upload(
                    ExportExcelEnum.NEW_PRODUCT_TARGET_REPORT.getExcelEnName() + timestamp + ".xlsx", tempFile, true);
            log.info("成功上传新品目标导出数据到FDS,url为:{}", upload.getUrl());
            //            return nrJobForExcelExport(upload.getUrl(), upcAccount);
            return jobTriggerHelper.triggerCommonExportJob(upcAccount, upload.getUrl(), "新品目标导出",
                    "新品目标的导出的描述");
        } catch (Exception e) {
            log.error("LDU Reporting Statistics List Export Exception: {}", e);
            return CommonResponse.failure(ResultCodeEnum.SYSTEM_ERROR.getCode(),
                    ResultCodeEnum.SYSTEM_ERROR.getEnMsg());
        } finally {
            if (excelWriter != null) {
                excelWriter.finish();
            }
            if (tempFile != null && tempFile.exists()) {
                tempFile.delete();
            }
        }
    }
    
    
    @Override
    public CommonApiResponse<List<String>> addNewProductTarget(@Valid NewProductTargetAddReq req) {
        log.info("NewProductTargetServiceImpl#addNewProductTarget req:{}", RetailJsonUtil.toJson(req));
        return newProductTargetDomainService.addNewProductTarget(req);
    }
    
    @Override
    public CommonApiResponse<String> updateNewProductTarget(NewProductTargetUpdateReq req) {
        log.info("NewProductTargetServiceImpl#updateNewProductTarget req:{}", RetailJsonUtil.toJson(req));
        return newProductTargetDomainService.updateNewProductTarget(req);
    }
    
    @Override
    public CommonApiResponse<List<NewProdcutTargetMetaResp>> listMeta(NewProdcutTargetMetaReq req) {
        log.info("NewProductTargetServiceImpl#listMeta req:{}", RetailJsonUtil.toJson(req));
        return newProductTargetDomainService.listMeta(req);
    }
    
    @Override
    public CommonApiResponse<String> importNewProdcutTargetMetaResp(@Valid NewProductTargetImportReq req) {
        log.info("NewProductTargetServiceImpl#importNewProdcutTargetMetaResp req:{}", RetailJsonUtil.toJson(req));
        RpcContext.getContext().setAttachment("$area_id", "GLOBAL");
        return newProductTargetDomainService.importNewProdcutTargetMetaResp(req);
    }
    
    @Override
    public CommonApiResponse<String> downLoadNewProductTargetTempalte(NewProductTargetImportReq query) {
        return new CommonApiResponse<>(targetUploadUrl);
    }
    
    @Override
    public CommonApiResponse<List<NewProductTargetProjectResp>> listProject(NewProductTargetProjectReq req) {
        log.info("NewProductTargetServiceImpl#listProject req:{}", RetailJsonUtil.toJson(req));
        return new CommonApiResponse<>(newProductTargetDomainService.listProject(req));
    }
    
}