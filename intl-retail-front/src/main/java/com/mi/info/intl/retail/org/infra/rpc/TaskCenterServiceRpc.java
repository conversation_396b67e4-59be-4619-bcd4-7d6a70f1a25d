package com.mi.info.intl.retail.org.infra.rpc;

import com.mi.info.intl.retail.core.utils.IntlRetailAssert;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterChangeExecutorReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterFinishReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterNoNeedCompleteReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterPushTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterTaskReq;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.xiaomi.cnzone.brain.platform.api.model.req.PushTaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.TaskExecutorReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.TaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.ProretailOuterEventReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.admin.ResetUserCurrentTaskEventStatusReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.CreateInstanceReq;
import com.xiaomi.cnzone.brain.platform.api.model.req.app.NoNeedCompleteTaskReq;
import com.xiaomi.cnzone.brain.platform.api.model.resp.app.CreateInstanceResp;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformAppProvider;
import com.xiaomi.cnzone.brain.platform.api.provider.BrainPlatformOuterProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Component
public class TaskCenterServiceRpc {

    @DubboReference(group = "${dubbo-group.consumer.center:}", check = false, timeout = 20000, retries = 0)
    private BrainPlatformAppProvider brainPlatformAppProvider;

    @DubboReference(group = "${dubbo-group.consumer.center:}", check = false, timeout = 20000, retries = 0)
    private BrainPlatformOuterProvider brainPlatformOuterProvider;

    /**
     * 完成用户当前任务动作- 使用TaskCenterFinishReq 完成任务
     */
    public void outerTaskFinish(TaskCenterFinishReq finishReq) {
        try {
            log.info("outerTaskFinish request:{}", finishReq);
            // 将TaskCenterFinishReq转换为ProretailOuterEventReq
            ProretailOuterEventReq outerEventReq = new ProretailOuterEventReq();
            outerEventReq.setTaskBatchId(finishReq.getTaskBatchId());
            outerEventReq.setOperatorMid(finishReq.getMid());
            outerEventReq.setMid(finishReq.getMid());
            outerEventReq.setOrgId(finishReq.getOrgId());
            outerEventReq.setRetailAppSign(finishReq.getRetailAppSign());
            outerEventReq.setRetailTenantId(finishReq.getRetailTenantId());
            outerEventReq.setTaskInstanceId(finishReq.getTaskInstanceId());

            // 调用原始的outerTaskFinish方法
            outerTaskFinish(outerEventReq);
        } catch (Exception e) {
            log.error("outerTaskFinish error", e);
            throw new RuntimeException("完成用户当前任务动作失败", e);
        }
    }

    /**
     * 完成用户当前任务动作
     */
    public void outerTaskFinish(ProretailOuterEventReq outerEventReq) {
        try {
            log.info("outerTaskFinish request:{}", outerEventReq);
            Result<String> taskResult = brainPlatformOuterProvider.outerTaskFinish(outerEventReq);
            this.checkResult(taskResult);
        } catch (Exception e) {
            log.error("outerTaskFinish error", e);
            throw new RuntimeException("完成用户当前任务动作失败", e);
        }
    }

    /**
     * 无需完成任务接口- 使用TaskCenterNoNeedCompleteReq
     */
    public void noNeedCompleteTask(TaskCenterNoNeedCompleteReq noNeedCompleteReq) {
        try {
            log.info("noNeedCompleteTask request:{}", noNeedCompleteReq);
            // 将TaskCenterNoNeedCompleteReq转换为NoNeedCompleteTaskReq
            NoNeedCompleteTaskReq noNeedCompleteTaskReq = new NoNeedCompleteTaskReq();
            noNeedCompleteTaskReq.setTaskBatchId(noNeedCompleteReq.getTaskBatchId());
            noNeedCompleteTaskReq.setType(noNeedCompleteReq.getType());
            noNeedCompleteTaskReq.setMid(noNeedCompleteReq.getMid());
            noNeedCompleteTaskReq.setOrgId(noNeedCompleteReq.getOrgId());

            // 调用原始的outerTaskFinish方法
            noNeedCompleteTask(noNeedCompleteTaskReq);
        } catch (Exception e) {
            log.error("noNeedCompleteTask error", e);
            throw new RuntimeException("完成用户当前任务动作失败", e);
        }
    }

    /**
     * 无需完成任务接口
     */
    public void noNeedCompleteTask(NoNeedCompleteTaskReq noNeedCompleteTaskReq) {
        try {
            log.info("noNeedCompleteTask request:{}", noNeedCompleteTaskReq);
            Result<Void> noNeedCompleteResult = brainPlatformAppProvider.noNeedCompleteTask(noNeedCompleteTaskReq);
            this.checkResult(noNeedCompleteResult);
        } catch (Exception e) {
            log.error("noNeedCompleteTask error", e);
            throw new RuntimeException("无需完成任务动作失败", e);
        }
    }

    private void checkResult(Result<?> result) {
//        if (result.getCode() != 0) {
//            throw new RuntimeException(result.getMessage());
//        }
    }

    /**
     * 下发任务
     */
    public CreateInstanceResp createInstance(CreateInstanceReq createInstanceReq) {
        try {
            log.info("createInstance request:{}", createInstanceReq);
            Result<CreateInstanceResp> result = brainPlatformOuterProvider.createInstance(createInstanceReq);
            this.checkResult(result);
            return result.getData();
        } catch (Exception e) {
            log.error("createInstance error", e);
            throw new RuntimeException("下发任务失败", e);
        }
    }

    /**
     * 任务提醒 - 使用TaskCenterPushTaskReq
     */
    public void pushTask(TaskCenterPushTaskReq taskCenterPushTaskReq) {
        try {
            log.info("pushTask with TaskCenterPushTaskReq: {}", taskCenterPushTaskReq);

            // 将TaskCenterPushTaskReq转换为PushTaskReq
            PushTaskReq pushTaskReq = new PushTaskReq();
            pushTaskReq.setTaskBatchId(taskCenterPushTaskReq.getTaskBatchId());

            // 转换OrgAndMid列表
            if (!CollectionUtils.isEmpty(taskCenterPushTaskReq.getList())) {
                List<PushTaskReq.OrgAndMid> orgAndMidList = new ArrayList<>();
                for (TaskCenterPushTaskReq.OrgAndMid item : taskCenterPushTaskReq.getList()) {
                    PushTaskReq.OrgAndMid orgAndMid = new PushTaskReq.OrgAndMid();
                    orgAndMid.setOrgId(item.getOrgId());
                    orgAndMid.setMid(item.getMid());
                    orgAndMidList.add(orgAndMid);
                }
                pushTaskReq.setList(orgAndMidList);
            }

            // 调用原始的pushTask方法
            pushTask(pushTaskReq);
        } catch (Exception e) {
            log.error("pushTask with TaskCenterPushTaskReq error", e);
            throw new RuntimeException("任务提醒失败", e);
        }
    }

    /**
     * 任务提醒
     */
    public void pushTask(PushTaskReq pushtaskReq) {
        try {
            log.info("pushTask request:{}", pushtaskReq);
            Result<Void> pushTaskResult = brainPlatformOuterProvider.pushTask(pushtaskReq);
            this.checkResult(pushTaskResult);
        } catch (Exception e) {
            log.error("pushTask error", e);
            throw new RuntimeException("任务提醒失败", e);
        }
    }

    /**
     * 任务未完成接口 - 使用TaskCenterTaskReq -重置任务
     */
    public void reloadTaskStatus(TaskCenterTaskReq taskcenterReq) {
        try {
            // 创建TaskReq对象
            TaskReq taskReq = new TaskReq();
            taskReq.setMid(taskcenterReq.getMid());
            taskReq.setOrgId(taskcenterReq.getOrgId());
            taskReq.setTaskBatchId(taskcenterReq.getTaskBatchId());

            // 调用原始的reloadTaskStatus方法
            reloadTaskStatus(taskReq);
        } catch (Exception e) {
            log.error("reloadTaskStatus error", e);
            throw new RuntimeException("任务未完成失败", e);
        }
    }

    /**
     * 任务未完成接口
     */
    public void reloadTaskStatus(TaskReq taskReq) {
        try {
            log.info("reloadTaskStatus request:{}", taskReq);
            Result<Void> taskResult = brainPlatformOuterProvider.reloadTaskStatus(taskReq);
            this.checkResult(taskResult);
        } catch (Exception e) {
            log.error("reloadTaskStatus error", e);
            throw new RuntimeException("任务未完成失败", e);
        }
    }

    /**
     * 变更执行人 - 使用TaskCenterChangeExecutorReq
     */
    public void changeExecutor(TaskCenterChangeExecutorReq taskReq) {
        try {
            log.info("changeExecutor request:{}", taskReq);
            TaskExecutorReq taskExecutorReq = new TaskExecutorReq();
            taskExecutorReq.setMid(taskReq.getMid());
            taskExecutorReq.setNewMid(taskReq.getNewMid());
            taskExecutorReq.setOrgId(taskReq.getPositionCode());
            taskExecutorReq.setTaskBatchId(taskReq.getTaskBatchId());
            // 调用原始的changeExecutor方法
            changeExecutor(taskExecutorReq);
        } catch (Exception e) {
            log.error("changeExecutor error", e);
            throw new RuntimeException("变更执行人失败", e);
        }
    }

    /**
     * 变更执行人
     */
    public void changeExecutor(TaskExecutorReq taskExecutorReq) {
        try {
            log.info("changeExecutor request:{}", taskExecutorReq);
            Result<Void> pushTaskResult = brainPlatformAppProvider.changeExecutor(taskExecutorReq);
            this.checkResult(pushTaskResult);
        } catch (Exception e) {
            log.error("changeExecutor error", e);
            throw new RuntimeException("变更执行人失败", e);
        }
    }

    /**
     * 下发阵地巡检任务
     *
     * @param orgAndMidList 组织和人员列表
     * @param validRuleConfig 有效的规则配置
     * @return 返回任务中心返回的taskBatchId
     */
    public Long pushPositionInspectionTask(List<CreateInstanceReq.OrgAndMid> orgAndMidList,
                                           RuleConfigDomain validRuleConfig) {
        try {

            Instant utcInstant = Instant.now().truncatedTo(ChronoUnit.MILLIS);
            CreateInstanceReq req = new CreateInstanceReq();
            req.setTaskDefinitionId(validRuleConfig.getTaskDefId());
            req.setBusinessTypeName("PositionInspection");
            // 开始时间 = 配置的开始时间 + 3小时(3 * 60 * 60 * 1000毫秒)
            req.setSugStartTimeStamp(utcInstant.toEpochMilli());
            req.setSugEndTimeStamp(validRuleConfig.getTaskEndTime());
            req.setDeadlineStamp(validRuleConfig.getTaskEndTime());
            req.setBusinessId(validRuleConfig.getRuleCode());
            req.setCreateType(2);
            req.setTitle("阵地巡检");
            req.setRetailTenantId("2");
            req.setRetailAppSign("CHANNEL_RETAIL");

            log.info("pushOrgAndMidListToTaskCenter request: {}", orgAndMidList);
            IntlRetailAssert.notEmpty(orgAndMidList, "orgAndMidList is empty");
            // 构造CreateInstanceReq，只设置OrgAndMidLists
            req.setOrgAndMidLists(orgAndMidList);
            // 只下发OrgAndMid，不设置其他字段
            CreateInstanceResp resp = this.createInstance(req);
            log.info("pushOrgAndMidListToTaskCenter response: {}", resp);
            if (resp != null && resp.getTaskBatchId() != null) {
                return resp.getTaskBatchId();
            }
        } catch (Exception e) {
            log.error("pushPositionInspectionTask error", e);
            throw new RuntimeException("下发阵地任务失败", e);
        }
        return null;
    }

    public void resetUserCurrentTaskEventStatus(MaterialInspectionDomain domain) {
        ResetUserCurrentTaskEventStatusReq req = new ResetUserCurrentTaskEventStatusReq();
        req.setMid(domain.getVerifierMiId());
        req.setOrgId(domain.getBusinessCode());
        req.setBusinessTypeId(domain.getBusinessType());
        req.setRetailTenantId("2");
        req.setRetailAppSign("CHANNEL_RETAIL");
        brainPlatformOuterProvider.resetUserCurrentTaskEventStatus(req);
    }
}
