package com.mi.info.intl.retail.org.domain.util;

import com.mi.info.intl.retail.intlretail.service.api.material.enums.TargetTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionHistoryItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItemDTO;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionSelectorItemList;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.I18nDesc;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.PositionCommonItemList;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.infra.entity.InspectionHistory;
import com.mi.info.intl.retail.org.infra.entity.RuleConfig;
import com.xiaomi.cnzone.maindataapi.model.dto.common.ConfigKV2;
import com.xiaomi.cnzone.maindataapi.model.dto.store.ListPositionInfoResponse;
import com.xiaomi.cnzone.maindataapi.model.dto.store.PositionListResponse;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Mapper
public interface BeanConverter {
    BeanConverter INSTANCE = Mappers.getMapper(BeanConverter.class);

    PositionSelectorItemList map(PositionCommonItemList commonItemList);

    default PositionCommonItemList map(CommonConfigDTO2 configDTO2, String language) {
        PositionCommonItemList commonItemList = new PositionCommonItemList();
        commonItemList.setPositionCategory(mapConfigKV2(configDTO2.getPositionCategory(), language));
        commonItemList.setPositionType(mapConfigKV2(configDTO2.getPositionType(), language));
        commonItemList.setPositionLocation(mapConfigKV2(configDTO2.getPositionLocation(), language));
        commonItemList.setDisplayStandardization(mapConfigKV2(configDTO2.getDisplayCapacityExpansionStatus(), language));
        return commonItemList;
    }

    default List<OptionalItem<String>> mapConfigKV2(List<ConfigKV2> kv2List, String language) {
        return kv2List.stream().map(kv2 -> new OptionalItem<>(mapKey(kv2.getKey()), mapValue(kv2, language)))
                .collect(Collectors.toList());
    }

    default String mapKey(Object value) {
        return Objects.toString(value, null);
    }

    default String mapValue(ConfigKV2 kv2, String language) {
        return StringUtils.startsWith(language, "zh") ? kv2.getValueCN() : kv2.getValueEN();
    }

    InspectionHistory map(InspectionHistoryDomain inspectionHistoryDomain);

    List<InspectionHistoryDomain> map2InspectionHistoryDomainList(List<InspectionHistory> inspectionHistoryList);

    List<InspectionHistory> map2InspectionHistoryList(List<InspectionHistoryDomain> inspectionHistoryDomainList);

    default PositionInspectionHistoryItem map2InspectionHistoryItem(InspectionHistoryDomain inspectionHistoryDomain) {
        if (inspectionHistoryDomain != null) {
            PositionInspectionHistoryItem inspectionHistoryItem = map2InspectionHistoryItemInner(inspectionHistoryDomain);
            inspectionHistoryItem.setOperationTypeDesc(I18nDesc.safeGetDesc(inspectionHistoryDomain.getOperationType()));
            inspectionHistoryItem.setDisapproveReasonDesc(I18nDesc.getDescByCodeOfEmpty(DisapproveReasonEnum.class,
                    inspectionHistoryDomain.getDisapproveReason()));
            return inspectionHistoryItem;
        }
        return null;
    }

    @org.mapstruct.Mapping(source = "operatorName", target = "operator")
    PositionInspectionHistoryItem map2InspectionHistoryItemInner(InspectionHistoryDomain inspectionHistoryDomain);

    @org.mapstruct.Mapping(target = "allowPhotoFromGallery", qualifiedByName = "integerToBoolean")
    @org.mapstruct.Mapping(target = "taskType", qualifiedByName = "mapToTaskTypeEnum")
    @org.mapstruct.Mapping(target = "targetType", qualifiedByName = "mapToTargetTypeEnum")
    RuleConfigDomain convertToDomain(RuleConfig entity);

    @org.mapstruct.Mapping(target = "allowPhotoFromGallery", qualifiedByName = "booleanToInteger")
    RuleConfig convertToEntity(RuleConfigDomain domain);

    PageResponse<PositionInspectionItemDTO> map(PageResponse<PositionInspectionItem> pageResponse);

    default Integer map2Integer(I18nDesc i18nDesc) {
        return i18nDesc == null ? null : i18nDesc.getCode();
    }

    ListPositionInfoResponse.PositionInfo.FurnitureDisplay mapToPositionInfoFurnitureDisplay(PositionListResponse.FurnitureDisplay furnitureDisplay);

    /**
     * Boolean 转 Integer 转换方法
     * true -> 1, false -> 0, null -> null
     */
    @org.mapstruct.Named("booleanToInteger")
    default Integer booleanToInteger(Boolean value) {
        if (value == null) {
            return null;
        }
        return value ? 1 : 0;
    }

    /**
     * Integer 转 Boolean 转换方法
     * 1 -> true, 0 -> false, null -> null
     */
    @org.mapstruct.Named("integerToBoolean")
    default Boolean integerToBoolean(Integer value) {
        if (value == null) {
            return Boolean.TRUE;
        }
        return value == 1;
    }

    @Named("mapToTaskTypeEnum")
    default TaskTypeEnum mapToTaskTypeEnum(Integer taskType) {
        if (taskType == null) {
            return null;
        }
        return Arrays.stream(TaskTypeEnum.values()).filter(e -> e.getCode().equals(taskType)).findFirst().orElse(null);
    }

    @Named("mapToTargetTypeEnum")
    default TargetTypeEnum mapToTargetTypeEnum(Integer targetType) {
        if (targetType == null) {
            return null;
        }
        return Arrays.stream(TargetTypeEnum.values()).filter(e -> e.getCode().equals(targetType.toString())).findFirst()
                .orElse(null);
    }

    default Integer map(TaskTypeEnum taskType) {
        return taskType != null ? taskType.getCode() : null;
    }

    default String map(TargetTypeEnum targetType) {
        return targetType != null ? targetType.getCode() : null;
    }
}
