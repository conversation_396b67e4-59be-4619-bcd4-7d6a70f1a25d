package com.mi.info.intl.retail.org.domain.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.mi.info.intl.retail.core.exception.RetailRunTimeException;
import com.xiaomi.cnzone.commons.utils.StringUtils;
import lombok.experimental.UtilityClass;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.rpc.RpcContext;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.function.Function;
import javax.servlet.http.HttpServletRequest;

@Slf4j
@UtilityClass
public class ContextUtil {

    public String getLang() {
        String language = RpcContext.getContext().getAttachment("$language");
        if (StringUtils.isEmpty(language)) {
            language = getHeader("X-Retail-Language");
        }
        if (StringUtils.isEmpty(language)) {
            log.error("language is empty, use default: en-US");
            language = "en-US";
        }
        return language;
    }

    public String getArea() {
        String areaId = RpcContext.getContext().getAttachment("$area_id");
        if (StringUtils.isEmpty(areaId)) {
            areaId = getHeader("X-Retail-Locale");
        }
        if (StringUtils.isEmpty(areaId)) {
            log.error("areaId is empty, use default: SG");
            areaId = "SG";
        }
        return areaId;
    }

    public Optional<HttpServletRequest> getRequest() {
        return Optional.ofNullable(RequestContextHolder.getRequestAttributes())
                .filter(ServletRequestAttributes.class::isInstance).map(ServletRequestAttributes.class::cast)
                .map(ServletRequestAttributes::getRequest);
    }

    public String getHeader(String key) {
        return getRequest().map(r -> r.getHeader(key)).orElse(null);
    }

    public Long getMiID() {
        String miID = RpcContext.getContext().getAttachment("$upc_miID");
        if (!StringUtils.isEmpty(miID)) {
            return Long.valueOf(miID);
        }
        throw new RetailRunTimeException("Unsupported context, miID not found");
    }

    public String getUserName() {
        String userName = RpcContext.getContext().getAttachment("$upc_userName");
        if (!StringUtils.isEmpty(userName)) {
            return userName;
        }
        throw new RetailRunTimeException("Unsupported context, userName not found");
    }

    public <T, R> List<Map<String, Object>> getNode(List<T> list, Function<T, R> code, Function<T, R> name) {
        List<Map<String, Object>> nodeList = new ArrayList<>();
        if (CollUtil.isEmpty(list)) {
            return nodeList;
        }
        for (T t : list) {
            Map<String, Object> map = new HashMap<>();
            try {
                map.put("code", code.apply(t));
                map.put("name", name.apply(t));
                nodeList.add(map);
            } catch (Exception e) {
                log.error("getNode error: {}", e.getMessage(), e);
            }
        }
        return nodeList;
    }
}
