package com.mi.info.intl.retail.org.domain.util;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsCountryTimezoneReadMapper;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * 国家时区工具类
 */
@Slf4j
@Component
public class CountryTimeUtil {

    @Autowired
    private IntlRmsCountryTimezoneReadMapper countryTimezoneReadMapper;

    /**
     * 获取当前时间为凌晨0点的国家列表
     *
     * @return 当前时间为凌晨0点的国家代码列表
     */
    public List<String> getCountriesAtMidnight(int regularTime) {
        // 获取当前UTC时间
        Instant now = Instant.now();
        ZonedDateTime utcNow = now.atZone(ZoneId.of("UTC"));

        log.info("当前UTC时间: {}", utcNow);

        List<String> countriesInRange = new ArrayList<>();

        // 从数据库查询所有有效的国家时区信息
        LambdaQueryWrapper<IntlRmsCountryTimezone> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlRmsCountryTimezone::getStateCode, 0); // 假设stateCode=0表示有效
        List<IntlRmsCountryTimezone> countryTimezones = countryTimezoneReadMapper.selectList(queryWrapper);

        log.info("查询到{}个国家时区信息", countryTimezones.size());


        for (IntlRmsCountryTimezone countryTimezone : countryTimezones) {
            try {
                // 优先使用标准时区代码计算当地时间
                String offsetDateTimeStr = IntlTimeUtil.getOffsetDateTimeByCountryCode(countryTimezone.getCountryCode());
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
                //如果offsetDateTimeStr为空则直接跳过 输出warn日志
                if (offsetDateTimeStr == null) {
                    log.warn("无法获取国家{}的时区信息", countryTimezone.getCountryCode());
                    continue;
                }
                LocalDateTime localDateTime = LocalDateTime.parse(offsetDateTimeStr, formatter);
                if (offsetDateTimeStr != null) {
                    int hour = localDateTime.getHour();
                    if (hour == regularTime) {  // 修正为检查0点
                        log.info("国家: {},  小时: {}", countryTimezone.getCountryCode(), hour);
                        countriesInRange.add(countryTimezone.getCountryCode());
                    }
                } else {
                    log.warn("无法获取国家{}的时区信息", countryTimezone.getCountryCode());
                }

            } catch (Exception e) {
                log.error("计算国家时间异常: {}, 错误: {}", countryTimezone.getCountryCode(), e.getMessage(), e);
            }
        }

        return countriesInRange;
    }

    /**
     * 解析时区偏移量
     * <p>
     * 注意：数据库中的偏移量是相对于UTC的分钟数
     * 正值表示比UTC早（东区），负值表示比UTC晚（西区）
     * 例如：中国是UTC+8，存储为+480（分钟）
     *
     * @param bias 偏移量字符串，格式如 "+480"、"-330" 等
     * @return 偏移分钟数
     */
    private int parseOffsetMinutes(String bias) {
        if (bias == null || bias.isEmpty()) {
            return 0;
        }

        try {
            // 移除可能的前缀符号
            String biasValue = bias;
            int sign = 1;

            if (bias.startsWith("+")) {
                biasValue = bias.substring(1);
                // 注意：数据库中+表示东区，比UTC早，所以是正值
                sign = 1;
            } else if (bias.startsWith("-")) {
                biasValue = bias.substring(1);
                // 注意：数据库中-表示西区，比UTC晚，所以是负值
                sign = -1;
            }

            return sign * Integer.parseInt(biasValue);
        } catch (NumberFormatException e) {
            log.error("解析时区偏移量异常: {}", bias, e);
            return 0;
        }
    }
} 