package com.mi.info.intl.retail.ldu.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/7/11 17:45
 */
public interface IntlRmsProductService extends IService<IntlRmsProduct> {

    /**
     * 批量保存，根据goodsId判断是否已存在记录，存在则更新，不存在则新增
     *
     * @param products 商品信息列表
     */
    void batchSaveOrUpdate(List<IntlRmsProduct> products);

    /**
     * 根据商品id查询商品信息
     *
     * @param goodsIds 商品id列表
     * @return 商品信息列表
     */
    List<IntlRmsProduct> queryByGoodsIdList(List<String> goodsIds);

    /**
     * 根据code69查询商品信息
     *
     * @param code69List code69列表
     * @return 商品信息列表
     */
    List<IntlRmsProduct> queryByCode69List(List<String> code69List);

    /**
     * 根据sku查询商品信息
     * @param skuList sku列表
     * @return 商品信息列表
     */
    List<IntlRmsProduct> queryBySkuList(List<String> skuList);
}
