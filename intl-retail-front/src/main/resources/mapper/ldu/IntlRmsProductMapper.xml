<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.ldu.infra.mapper.IntlRmsProductMapper">

    <!-- 根据产品ID列表批量查询产品信息 -->
    <select id="selectByProductIds" resultType="com.mi.info.intl.retail.ldu.infra.entity.IntlRmsProduct">
        SELECT *
        FROM intl_rms_product
        WHERE id IN
        <foreach collection="productIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>



</mapper>