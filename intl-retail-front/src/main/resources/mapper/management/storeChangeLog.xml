<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.management.infra.mapper.StoreChangeLogMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.management.infra.entity.StoreChangeLog">
        <id property="id" column="id"/>
        <result property="storeName" column="store_name"/>
        <result property="storeCode" column="store_code"/>
        <result property="positionChangeId" column="position_change_id"/>
        <result property="rmsStoreCode" column="rms_store_code"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="storeClass" column="store_class"/>
        <result property="workableType" column="workable_type"/>
        <result property="hasPc" column="has_pc"/>
        <result property="hasSr" column="has_sr"/>
        <result property="storeStatus" column="store_status"/>
        <result property="changeReason" column="change_reason"/>
        <result property="recordCreateTime" column="record_create_time"/>
        <result property="storeGrade" column="store_grade"/>
        <result property="storeType" column="store_type"/>
        <result property="storeChannelType" column="store_channel_type"/>
        <result property="hasFront" column="has_front"/>
        <result property="hasPos" column="has_pos"/>
        <result property="remainingXiaomiStore" column="remaining_xiaomi_store"/>
        <result property="effectiveStatus" column="effective_status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,store_name,store_code,position_change_id,rms_store_code,effective_time,
        store_class,workable_type,has_pc,has_sr,store_status,change_reason,record_create_time,store_grade,store_type,store_channel_type,has_front,has_pos,remaining_xiaomi_store,effective_status
    </sql>
</mapper>
