<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.management.infra.mapper.PositionChangeLogMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.management.infra.entity.PositionChangeLog">
        <id property="id" column="id"/>
        <result property="positionName" column="position_name"/>
        <result property="positionCode" column="position_code"/>
        <result property="storeCode" column="store_code"/>
        <result property="rmsPositionCode" column="rms_position_code"/>
        <result property="effectiveTime" column="effective_time"/>
        <result property="positionClass" column="position_class"/>
        <result property="workableType" column="workable_type"/>
        <result property="positionType" column="position_type"/>
        <result property="hasPc" column="has_pc"/>
        <result property="hasSr" column="has_sr"/>
        <result property="positionStatus" column="position_status"/>
        <result property="changeType" column="change_type"/>
        <result property="changeReason" column="change_reason"/>
        <result property="logCreateTime" column="log_create_time"/>
        <result property="effectiveStatus" column="effective_status"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,position_name,position_code,store_code,rms_position_code,effective_time,
        position_class,workable_type,position_type,has_pc,has_sr,position_status,change_type,change_reason,log_create_time,effective_status
    </sql>
</mapper> 