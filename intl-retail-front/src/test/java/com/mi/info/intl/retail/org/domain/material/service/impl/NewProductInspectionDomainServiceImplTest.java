package com.mi.info.intl.retail.org.domain.material.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.ImmutableList;
import com.google.common.collect.ImmutableMap;
import com.mi.info.intl.retail.cooperation.task.domain.RmsCountryTimezoneService;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionRecordPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordPageDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionRecordSelectorItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionVerifyRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TargetTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialDisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.MaterialInspectionStatus;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.VerifyActionEnum;
import com.mi.info.intl.retail.ldu.dto.LduReportSimple;
import com.mi.info.intl.retail.ldu.infra.repository.LduReportRepository;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.org.domain.MaterialInspectionDomain;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.cnzone.maindataapi.model.dto.common.ConfigKV2;
import com.xiaomi.cnzone.storems.api.api.StoreRelateProvider;
import com.xiaomi.cnzone.storems.api.model.dto.store.CommonConfigDTO2;
import com.xiaomi.cnzone.storems.api.model.req.store.CommonConfigReq;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.AfterEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import static org.mockito.Mockito.doAnswer;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadPoolExecutor;
import javax.validation.Validator;

@ExtendWith(MockitoExtension.class)
class NewProductInspectionDomainServiceImplTest {
    @Mock
    private StoreRelateProvider storeRelateProvider;
    @Mock
    private NewProductInspectionRepository newProductInspectionRepository;
    @Mock
    private RmsCountryTimezoneService rmsCountryTimezoneService;
    @Mock
    private IntlRmsUserService intlRmsUserService;
    @Mock
    private ThreadPoolTaskExecutor batchQueryExecutor;
    @Mock
    private ThreadPoolExecutor pool;
    @Mock
    private IntlFileUploadService fileUploadService;
    @Mock
    private LduReportRepository lduReportRepository;
    @Mock
    private UserProvider userProvider;
    @Mock
    private RuleConfigRepository ruleConfigRepository;
    @Mock
    private Validator validator;
    @Mock
    private TaskCenterServiceRpc taskCenterServiceRpc;
    @InjectMocks
    private NewProductInspectionDomainServiceImpl service;

    private MockedStatic<UserInfoUtil> userInfoUtilMockedStatic;

    private MaterialInspectionVerifyRequest verifyRequest;
    private MaterialInspectionDomain inspectionDomain;
    private RuleConfigDomain ruleConfig;
    private UserInfo userInfo;

    @BeforeEach
    void setUp() {
        verifyRequest = new MaterialInspectionVerifyRequest();
        verifyRequest.setId(1L);
        verifyRequest.setVerifyStatus(VerifyActionEnum.APPROVE.getCode());

        inspectionDomain = new MaterialInspectionDomain();
        inspectionDomain.setId(1L);
        inspectionDomain.setRuleConfigId(100L);
        inspectionDomain.setInspectionStatus(
                MaterialInspectionStatus.VERIFYING);
        inspectionDomain.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);

        ruleConfig = new RuleConfigDomain();
        ruleConfig.setNeedInspection(1); // BoolEnum.YES
        ruleConfig.setTargetType(TargetTypeEnum.FIRST_SALES_PERIOD);

        userInfo = new UserInfo();
        userInfo.setMiID(123L);
        userInfo.setUserName("testUser");

        userInfoUtilMockedStatic = mockStatic(UserInfoUtil.class);
    }

    @AfterEach
    void teardown() {
        if (userInfoUtilMockedStatic != null) {
            userInfoUtilMockedStatic.close();
        }
    }

    private static CommonConfigDTO2 getCommonConfigDTO2() {
        CommonConfigDTO2 dto = new CommonConfigDTO2();
        ConfigKV2 channel = new ConfigKV2();
        channel.setKey("1");
        channel.setValue("渠道1");
        dto.setChannelType(Collections.singletonList(channel));
        ConfigKV2 positionType = new ConfigKV2();
        positionType.setKey("2");
        positionType.setValue("类型2");
        dto.setPositionType(Collections.singletonList(positionType));
        ConfigKV2 storeGrade = new ConfigKV2();
        storeGrade.setKey("3");
        storeGrade.setValue("等级3");
        dto.setStoreGradings(Collections.singletonList(storeGrade));
        return dto;
    }

    @Test
    @DisplayName("获取选择器项列表 - 应返回有效项")
    void testGetSelectorItemList_shouldReturnValidItem() {
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class))).thenReturn(
                Result.success(getCommonConfigDTO2()));
        MaterialInspectionRecordSelectorItem item = service.getSelectorItemList();
        assertNotNull(item);
        assertNotNull(item.getChannel());
        assertFalse(item.getChannel().isEmpty());
        assertEquals("渠道1", item.getChannel().get(0).getKey());
        assertEquals("渠道1", item.getChannel().get(0).getValue());
        assertNotNull(item.getPositionType());
        assertFalse(item.getPositionType().isEmpty());
        assertEquals("2", item.getPositionType().get(0).getKey());
        assertEquals("类型2", item.getPositionType().get(0).getValue());
        assertNotNull(item.getStoreGrade());
        assertFalse(item.getStoreGrade().isEmpty());
        assertEquals("等级3", item.getStoreGrade().get(0).getKey());
        assertEquals("等级3", item.getStoreGrade().get(0).getValue());
        assertNotNull(item.getTaskStatus());
        assertNotNull(item.getTaskType());
        assertNotNull(item.getDisapproveReason());
        assertNotNull(item.getStoreStatus());
        assertNotNull(item.getInspectionStatus());
        assertNotNull(item.getStoreLimitedRange());
        assertNotNull(item.getWhetherTakePhoto());
        assertNotNull(item.getMaterialCovered());
    }

    @Test
    @DisplayName("获取物料巡检记录分页项 - 无记录时应返回空")
    void testGetMaterialInspectionRecordPageItem_shouldReturnEmptyWhenNoRecords() {
        InspectionRecordPageRequest req = new InspectionRecordPageRequest();
        req.setPageNum(1L);
        req.setPageSize(10L);
        Page<InspectionRecordPageItem> page = new Page<>(1, 10, 0);
        page.setRecords(Collections.emptyList());
        when(newProductInspectionRepository.getMaterialInspectionRecordPageItem(
                any(InspectionRecordPageRequest.class))).thenReturn(page);
        PageResponse<MaterialInspectionRecordPageDTO> resp = service.getMaterialInspectionRecordPageItem(req);
        assertNotNull(resp);
        assertEquals(0, resp.getTotalCount());
        assertNotNull(resp.getList());
        assertTrue(resp.getList().isEmpty());
    }

    @Test
    @DisplayName("获取物料巡检记录分页项 - 应正确映射记录")
    void testGetMaterialInspectionRecordPageItem_shouldMapRecords() {
        InspectionRecordPageRequest req = new InspectionRecordPageRequest();
        req.setPageNum(1L);
        req.setPageSize(10L);
        InspectionRecordPageItem record = new InspectionRecordPageItem();
        record.setId(100L);
        record.setTaskName("T");
        record.setPositionName("P");
        record.setPositionCode("PC");
        record.setStoreName("SN");
        record.setStoreCode("SC");
        record.setStoreGrade("SG");
        record.setReportDistance(1.23);
        record.setStoreStatus(1);
        record.setTaskStatus(TaskStatusEnum.COMPLETED.getCode());
        record.setChannel("ch");
        record.setCovered(1);
        record.setStoreLimitedRange(1);
        record.setTaskType(TaskTypeEnum.LDU.getCode());
        record.setOnSaleProjectCode("[\"p1\",\"p2\"]");
        record.setProjectCode("[\"p0\"]");
        record.setCountryCode("CN");
        record.setRegionCode("REG");
        record.setVerifierMiId(1L);
        record.setVerifier("ver");
        record.setCreatedTime(System.currentTimeMillis());
        record.setUploadData(
                "[{\"materialValue\":{\"guid\":\"111\",\"images\":[]},\"lduReportLogId\":1,\"businessStatusCode\":0}," +
                        "{\"materialValue\":{\"guid\":\"222\",\"images\":[]},\"lduReportLogId\":1034,\"businessStatusCode\":0}]");
        Page<InspectionRecordPageItem> page = new Page<>(1, 10, 1);
        page.setRecords(Collections.singletonList(record));
        when(newProductInspectionRepository.getMaterialInspectionRecordPageItem(
                any(InspectionRecordPageRequest.class))).thenReturn(page);
        when(rmsCountryTimezoneService.getCountryAreaMapByCountryCodes(anyList())).thenReturn(
                Collections.singletonMap("CN", "中国"));
        when(intlRmsUserService.getIntlRmsUserByMiIds(anyList())).thenReturn(
                Collections.singletonList(IntlRmsUserDto.builder().miId(1L).englishName("E").domainName("D").build()));
        Map<String, List<String>> map =
                ImmutableMap.of("111", ImmutableList.of("1.jpg"), "222", ImmutableList.of("2.jpg"));
        when(fileUploadService.getUrlsByModuleAndGuids(any(), anyList())).thenReturn(map);
        when(batchQueryExecutor.getThreadPoolExecutor()).thenReturn(pool);
        when(userProvider.listUserInfo(any())).thenReturn(Result.success(Collections.emptyList()));
        LduReportSimple simple = new LduReportSimple();
        simple.setId(1L);
        simple.setSn("ABC");
        simple.setCode69("abc");
        simple.setReportRole("ReportRole");
        when(lduReportRepository.findSnByIds(anyList())).thenReturn(Collections.singletonList(simple));
        doAnswer(invocation -> {
            Runnable task = invocation.getArgument(0);
            task.run(); // 执行任务
            return null;
        }).when(pool).execute(any(Runnable.class));
        when(storeRelateProvider.get3CCommonConfig(any(CommonConfigReq.class))).thenReturn(
                Result.success(getCommonConfigDTO2()));

        PageResponse<MaterialInspectionRecordPageDTO> resp = service.getMaterialInspectionRecordPageItem(req);
        assertNotNull(resp);
        assertEquals(1, resp.getTotalCount());
        assertNotNull(resp.getList());
        assertEquals(1, resp.getList().size());
        MaterialInspectionRecordPageDTO dto = resp.getList().get(0);
        assertEquals(100L, dto.getId());
        assertEquals("中国", dto.getCountry());
        assertNotNull(dto.getStoreStatus());
        assertNotNull(dto.getTaskType());
        assertNotNull(dto.getTaskStatus());
    }

    @Test
    @DisplayName("验证物料巡检 - 成功场景")
    void testVerifyMaterialInspection_Success() {
        when(newProductInspectionRepository.getById(anyLong())).thenReturn(inspectionDomain);
        when(ruleConfigRepository.getById(anyLong())).thenReturn(ruleConfig);
        userInfoUtilMockedStatic.when(UserInfoUtil::getUserContext).thenReturn(userInfo);
        when(newProductInspectionRepository.updateVerifyStatus(any())).thenReturn(1);

        service.verifyMaterialInspection(verifyRequest);

        verify(newProductInspectionRepository, times(1)).updateVerifyStatus(inspectionDomain);
        assertEquals(MaterialInspectionStatus.PASSED, inspectionDomain.getInspectionStatus());
    }

    @Test
    @DisplayName("验证物料巡检 - 拒绝场景")
    void testVerifyMaterialInspection_Rejected() {

        verifyRequest.setVerifyStatus(VerifyActionEnum.DISAPPROVE.getCode());
        verifyRequest.setDisapproveReason(MaterialDisapproveReasonEnum.INCORRECT_PRODUCT.getCode());
        when(newProductInspectionRepository.getById(anyLong())).thenReturn(inspectionDomain);
        when(ruleConfigRepository.getById(anyLong())).thenReturn(ruleConfig);
        when(UserInfoUtil.getUserContext()).thenReturn(userInfo);
        when(newProductInspectionRepository.updateVerifyStatus(any())).thenReturn(1);
        userInfoUtilMockedStatic.when(UserInfoUtil::getUserContext).thenReturn(userInfo);

        service.verifyMaterialInspection(verifyRequest);

        verify(newProductInspectionRepository, times(1)).updateVerifyStatus(inspectionDomain);
        assertEquals(MaterialInspectionStatus.REJECTED, inspectionDomain.getInspectionStatus());
        assertEquals(TaskStatusEnum.NOT_COMPLETED, inspectionDomain.getTaskStatus());
    }

    @Test
    @DisplayName("验证物料巡检 - 拒绝场景(首销期)")
    void testVerifyMaterialInspection_RejectedFirstSalesPeriod() {

        verifyRequest.setVerifyStatus(VerifyActionEnum.DISAPPROVE.getCode());
        verifyRequest.setDisapproveReason(MaterialDisapproveReasonEnum.INCORRECT_PRODUCT.getCode());
        inspectionDomain.setTaskBatchId(1L);
        inspectionDomain.setTaskInstanceId(1L);
        inspectionDomain.setPeriodStartTimeStamp(1L);
        inspectionDomain.setPeriodEndTimeStamp(System.currentTimeMillis() + 3600000);
        when(newProductInspectionRepository.getById(anyLong())).thenReturn(inspectionDomain);
        ruleConfig.setTargetType(TargetTypeEnum.FIRST_SALES_PERIOD);
        when(ruleConfigRepository.getById(anyLong())).thenReturn(ruleConfig);
        when(UserInfoUtil.getUserContext()).thenReturn(userInfo);
        when(newProductInspectionRepository.updateVerifyStatus(any())).thenReturn(1);
        userInfoUtilMockedStatic.when(UserInfoUtil::getUserContext).thenReturn(userInfo);

        service.verifyMaterialInspection(verifyRequest);
        verify(taskCenterServiceRpc, times(1)).reloadTaskStatus(any(TaskCenterTaskReq.class));
    }

}