package com.mi.info.intl.retail.management.service.impl;

import com.mi.info.intl.retail.intlretail.service.api.management.dto.RuleQueryReq;
import com.mi.info.intl.retail.management.mapper.StoreGradeMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRuleMapper;
import com.mi.info.intl.retail.management.mapper.StoreGradeRelationMapper;
import com.mi.info.intl.retail.management.entity.StoreGradeRule;
import com.mi.info.intl.retail.model.ChannelTypeStatistics;
import com.mi.info.intl.retail.model.RetailerChannelGradeCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteCount;
import com.mi.info.intl.retail.model.StoreGradeCompleteStatistics;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * StoreGradeServiceImpl 单元测试
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@ExtendWith(MockitoExtension.class)
class StoreGradeServiceImplTest {

    @Mock
    private StoreGradeMapper storeGradeMapper;

    @Mock
    private StoreGradeRuleMapper storeGradeRuleMapper;

    @Mock
    private StoreGradeRelationMapper storeGradeRelationMapper;

    @InjectMocks
    private StoreGradeServiceImpl storeGradeService;

    @BeforeEach
    void setUp() {
        // 初始化测试环境
    }

    @Test
    void testGetChannelTypeStatistics_WithValidData() {
        // 准备测试数据
        List<RetailerChannelGradeCount> mockData = Arrays.asList(
            new RetailerChannelGradeCount("ONLINE", "1", 10L),
            new RetailerChannelGradeCount("ONLINE", "0", 5L),
            new RetailerChannelGradeCount("OFFLINE", "1", 8L),
            new RetailerChannelGradeCount("OFFLINE", "0", 2L)
        );

        when(storeGradeMapper.selectRetailerChannelGradeCount(any())).thenReturn(mockData);

        // 执行测试
        CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();
        List<ChannelTypeStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + ONLINE + OFFLINE

        // 验证总计统计
        ChannelTypeStatistics totalStats = result.get(0);
        assertEquals("ALL", totalStats.getChannelType());
        assertEquals(18, totalStats.getCompleteCount()); // 10 + 8
        assertEquals(7, totalStats.getNotCompleteCount()); // 5 + 2

        verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(any());
    }

    @Test
    void testGetChannelTypeStatistics_WithEmptyData() {
        when(storeGradeMapper.selectRetailerChannelGradeCount(any())).thenReturn(Arrays.asList());

        CommonResponse<List<ChannelTypeStatistics>> response = storeGradeService.getChannelTypeStatistics();
        List<ChannelTypeStatistics> result = response.getData();

        assertNotNull(result);
        assertEquals(1, result.size()); // 只有 ALL
        assertEquals(0, result.get(0).getCompleteCount());
        assertEquals(0, result.get(0).getNotCompleteCount());

        verify(storeGradeMapper, times(1)).selectRetailerChannelGradeCount(any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithCurrentMethod() {
        // 准备测试数据
        Integer ruleId = 1;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType(111)
            .retailerCode("RETAILER001")
            .method(1)
            .build();

        List<StoreGradeCompleteCount> mockData = Arrays.asList(
            new StoreGradeCompleteCount("S", 10L),
            new StoreGradeCompleteCount("A", 20L)
        );

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);
        when(storeGradeMapper.selectStoreGradeCompleteCount(any(), eq("ONLINE"), eq("RETAILER001"))).thenReturn(mockData);

        // 执行测试
        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + S + A
        assertEquals(30, result.get(0).getCount()); // 总计

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, times(1)).selectStoreGradeCompleteCount(any(), eq("ONLINE"), eq("RETAILER001"));
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithRelationMethod() {
        // 准备测试数据
        Integer ruleId = 1;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType(111)
            .retailerCode("RETAILER001")
            .method(2) // 修改为2，对应MANUAL_UPLOAD，使用关系表统计
            .build();

        List<StoreGradeCompleteCount> mockData = Arrays.asList(
            new StoreGradeCompleteCount("S", 15L),
            new StoreGradeCompleteCount("A", 25L)
        );

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);
        when(storeGradeRelationMapper.selectStoreGradeCountByRuleId(any(), any())).thenReturn(mockData);

        // 执行测试
        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        // 验证结果
        assertNotNull(result);
        assertEquals(3, result.size()); // ALL + S + A
        assertEquals(40, result.get(0).getCount()); // 总计

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeRelationMapper, times(1)).selectStoreGradeCountByRuleId(any(), any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithInvalidRuleId() {
        Integer ruleId = 999;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(null);

        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        assertNotNull(result);
        assertTrue(result.isEmpty());

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, never()).selectStoreGradeCompleteCount(any(), any(), any());
        verify(storeGradeRelationMapper, never()).selectStoreGradeCountByRuleId(any(), any());
    }

    @Test
    void testGetStoreGradeCompleteStatistics_WithUnsupportedMethod() {
        Integer ruleId = 1;
        RuleQueryReq ruleQueryReq = new RuleQueryReq();
        ruleQueryReq.setRuleId(ruleId);
        StoreGradeRule mockRule = StoreGradeRule.builder()
            .channelType(111)
            .retailerCode("RETAILER001")
            .method(99) // 修改为不支持的method值，测试未实现的计算方法逻辑
            .build();

        when(storeGradeRuleMapper.selectById(ruleId)).thenReturn(mockRule);

        CommonResponse<List<StoreGradeCompleteStatistics>> response = storeGradeService.getStoreGradeCompleteStatistics(ruleQueryReq);
        List<StoreGradeCompleteStatistics> result = response.getData();

        assertNotNull(result);
        assertTrue(result.isEmpty()); // 期望返回空列表，因为是不支持的计算方法

        verify(storeGradeRuleMapper, times(1)).selectById(ruleId);
        verify(storeGradeMapper, never()).selectStoreGradeCompleteCount(any(), any(), any());
        verify(storeGradeRelationMapper, never()).selectStoreGradeCountByRuleId(any(), any());
    }
} 