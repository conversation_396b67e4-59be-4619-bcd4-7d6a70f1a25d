package com.mi.info.intl.retail.org.infra.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsPositionMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsPositionReadMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * PositionRepositoryImpl单元测试类
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("阵地信息仓储实现类测试")
class PositionRepositoryImplTest {

    @InjectMocks
    private PositionRepositoryImpl positionRepository;

    @Mock
    private IntlRmsPositionMapper positionMapper;

    @Mock
    private IntlRmsPositionReadMapper positionReadMapper;

    private PositionDomain testPositionDomain;
    private IntlRmsPosition testPositionEntity;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        testPositionDomain = new PositionDomain();
        testPositionDomain.setPositionId("POS001");
        testPositionDomain.setPositionCode("CODE001");
        testPositionDomain.setPositionName("测试阵地");
        testPositionDomain.setPositionStatus("1");
        testPositionDomain.setPositionStatusName("正常");
        testPositionDomain.setPositionType(1);
        testPositionDomain.setPositionTypeName("门店");
        testPositionDomain.setCountry("CN");
        testPositionDomain.setCountryName("中国");
        testPositionDomain.setCityId("BJ");
        testPositionDomain.setCityName("北京");
        testPositionDomain.setAddress("北京市朝阳区");
        testPositionDomain.setStoreId("STORE001");
        testPositionDomain.setStoreName("测试门店");
        testPositionDomain.setLevel(1);
        testPositionDomain.setLevelName("A级");
        testPositionDomain.setRetailerId("RETAILER001");
        testPositionDomain.setRetailerName("测试零售商");
        testPositionDomain.setChannelType(1);
        testPositionDomain.setChannelTypeName("直营");
        testPositionDomain.setOwnerId("OWNER001");
        testPositionDomain.setOwnerName("测试负责人");
        testPositionDomain.setHasPromotion(true);
        testPositionDomain.setIsActive(true);
        testPositionDomain.setCreatedAt(1640995200L);
        testPositionDomain.setUpdatedAt(1640995200L);
        testPositionDomain.setRegion("华北");
        testPositionDomain.setCrpsCode("CRPS001");
        testPositionDomain.setPositionCategory(Arrays.asList(1, 2, 3));
        testPositionDomain.setDisplayCapacityExpansionStatus(1);
        testPositionDomain.setPositionLocation(1);

        testPositionEntity = new IntlRmsPosition();
        testPositionEntity.setId(1);
        testPositionEntity.setPositionId("POS001");
        testPositionEntity.setCode("CODE001");
        testPositionEntity.setName("测试阵地");
        testPositionEntity.setState(1);
        testPositionEntity.setStateName("正常");
        testPositionEntity.setType(1);
        testPositionEntity.setTypeName("门店");
        testPositionEntity.setCountryId("CN");
        testPositionEntity.setCountryName("中国");
        testPositionEntity.setCityId("BJ");
        testPositionEntity.setCityName("北京");
        testPositionEntity.setAddress("北京市朝阳区");
        testPositionEntity.setStoreId("STORE001");
        testPositionEntity.setStoreName("测试门店");
        testPositionEntity.setLevel(1);
        testPositionEntity.setLevelName("A级");
        testPositionEntity.setRetailerId("RETAILER001");
        testPositionEntity.setRetailerName("测试零售商");
        testPositionEntity.setChannelType(1);
        testPositionEntity.setChannelTypeName("直营");
        testPositionEntity.setOwnerId("OWNER001");
        testPositionEntity.setOwnerName("测试负责人");
        testPositionEntity.setIsPromotionStore(1);
        testPositionEntity.setStateCode(1);
        testPositionEntity.setCreatedAt(1640995200L);
        testPositionEntity.setUpdatedAt(1640995200L);
        testPositionEntity.setArea("华北");
        testPositionEntity.setCrpsCode("CRPS001");
        testPositionEntity.setPositionCategory("[1,2,3]");
        testPositionEntity.setDisplayCapacityExpansionStatus(1);
        testPositionEntity.setPositionLocation(1);
    }

    @Test
    @DisplayName("保存阵地信息 - 成功")
    void save_Success() {
        // 准备数据
        when(positionMapper.insert(any(IntlRmsPosition.class))).thenReturn(1);

        // 执行测试
        boolean result = positionRepository.save(testPositionDomain);

        // 验证结果
        assertTrue(result);
        verify(positionMapper).insert(any(IntlRmsPosition.class));
    }

    @Test
    @DisplayName("保存阵地信息 - 失败")
    void save_Failure() {
        // 准备数据
        when(positionMapper.insert(any(IntlRmsPosition.class))).thenReturn(0);

        // 执行测试
        boolean result = positionRepository.save(testPositionDomain);

        // 验证结果
        assertFalse(result);
        verify(positionMapper).insert(any(IntlRmsPosition.class));
    }

    @Test
    @DisplayName("通过ID获取阵地信息 - 存在")
    void getById_Exists() {
        // 准备数据
        when(positionReadMapper.selectById(1)).thenReturn(testPositionEntity);

        // 执行测试
        PositionDomain result = positionRepository.getById(1);

        // 验证结果
        assertNotNull(result);
        assertEquals("POS001", result.getPositionId());
        assertEquals("CODE001", result.getPositionCode());
        assertEquals("测试阵地", result.getPositionName());
        verify(positionReadMapper).selectById(1);
    }

    @Test
    @DisplayName("通过ID获取阵地信息 - 不存在")
    void getById_NotExists() {
        // 准备数据
        when(positionReadMapper.selectById(999)).thenReturn(null);

        // 执行测试
        PositionDomain result = positionRepository.getById(999);

        // 验证结果
        assertNull(result);
        verify(positionReadMapper).selectById(999);
    }

    @Test
    @DisplayName("通过阵地ID获取阵地信息 - 存在")
    void getByPositionId_Exists() {
        // 准备数据
        when(positionReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testPositionEntity);

        // 执行测试
        PositionDomain result = positionRepository.getByPositionId("POS001");

        // 验证结果
        assertNotNull(result);
        assertEquals("POS001", result.getPositionId());
        verify(positionReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过阵地ID获取阵地信息 - 不存在")
    void getByPositionId_NotExists() {
        // 准备数据
        when(positionReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        PositionDomain result = positionRepository.getByPositionId("NONEXISTENT");

        // 验证结果
        assertNull(result);
        verify(positionReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过阵地编码获取阵地信息 - 存在")
    void getByPositionCode_Exists() {
        // 准备数据
        when(positionReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testPositionEntity);

        // 执行测试
        PositionDomain result = positionRepository.getByPositionCode("CODE001");

        // 验证结果
        assertNotNull(result);
        assertEquals("CODE001", result.getPositionCode());
        verify(positionReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过阵地编码获取阵地信息 - 不存在")
    void getByPositionCode_NotExists() {
        // 准备数据
        when(positionReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        PositionDomain result = positionRepository.getByPositionCode("NONEXISTENT");

        // 验证结果
        assertNull(result);
        verify(positionReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过阵地编码列表获取阵地信息 - 成功")
    void getByPositionCodeList_Success() {
        // 准备数据
        List<String> positionCodes = Arrays.asList("CODE001", "CODE002");
        List<IntlRmsPosition> entities = Arrays.asList(testPositionEntity, testPositionEntity);
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<PositionDomain> result = positionRepository.getByPositionCodeList(positionCodes);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("CODE001", result.get(0).getPositionCode());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过阵地编码列表获取阵地信息 - 空列表")
    void getByPositionCodeList_EmptyList() {
        // 准备数据
        List<String> positionCodes = Collections.emptyList();
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        List<PositionDomain> result = positionRepository.getByPositionCodeList(positionCodes);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过编码获取阵地信息 - 存在")
    void getByCode_Exists() {
        // 准备数据
        when(positionReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(testPositionEntity);

        // 执行测试
        PositionDomain result = positionRepository.getByCode("CODE001");

        // 验证结果
        assertNotNull(result);
        assertEquals("CODE001", result.getPositionCode());
        verify(positionReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过编码获取阵地信息 - 不存在")
    void getByCode_NotExists() {
        // 准备数据
        when(positionReadMapper.selectOne(any(LambdaQueryWrapper.class))).thenReturn(null);

        // 执行测试
        PositionDomain result = positionRepository.getByCode("NONEXISTENT");

        // 验证结果
        assertNull(result);
        verify(positionReadMapper).selectOne(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("更新阵地信息 - 成功")
    void update_Success() {
        // 准备数据
        when(positionMapper.updateById(any(IntlRmsPosition.class))).thenReturn(1);

        // 执行测试
        boolean result = positionRepository.update(testPositionDomain);

        // 验证结果
        assertTrue(result);
        verify(positionMapper).updateById(any(IntlRmsPosition.class));
    }

    @Test
    @DisplayName("更新阵地信息 - 失败")
    void update_Failure() {
        // 准备数据
        when(positionMapper.updateById(any(IntlRmsPosition.class))).thenReturn(0);

        // 执行测试
        boolean result = positionRepository.update(testPositionDomain);

        // 验证结果
        assertFalse(result);
        verify(positionMapper).updateById(any(IntlRmsPosition.class));
    }

    @Test
    @DisplayName("通过零售商ID获取阵地列表 - 成功")
    void getByRetailerId_Success() {
        // 准备数据
        List<IntlRmsPosition> entities = Arrays.asList(testPositionEntity, testPositionEntity);
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<PositionDomain> result = positionRepository.getByRetailerId("RETAILER001");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("RETAILER001", result.get(0).getRetailerId());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过零售商ID获取阵地列表 - 空列表")
    void getByRetailerId_EmptyList() {
        // 准备数据
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        List<PositionDomain> result = positionRepository.getByRetailerId("NONEXISTENT");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过门店ID获取阵地列表 - 成功")
    void getByStoreId_Success() {
        // 准备数据
        List<IntlRmsPosition> entities = Arrays.asList(testPositionEntity, testPositionEntity);
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<PositionDomain> result = positionRepository.getByStoreId("STORE001");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("STORE001", result.get(0).getStoreId());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过门店ID获取阵地列表 - 空列表")
    void getByStoreId_EmptyList() {
        // 准备数据
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        List<PositionDomain> result = positionRepository.getByStoreId("NONEXISTENT");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过国家获取阵地列表 - 成功")
    void getByCountry_Success() {
        // 准备数据
        List<IntlRmsPosition> entities = Arrays.asList(testPositionEntity, testPositionEntity);
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<PositionDomain> result = positionRepository.getByCountry("CN");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("CN", result.get(0).getCountry());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过国家获取阵地列表 - 空列表")
    void getByCountry_EmptyList() {
        // 准备数据
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        List<PositionDomain> result = positionRepository.getByCountry("NONEXISTENT");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过城市ID获取阵地列表 - 成功")
    void getByCityId_Success() {
        // 准备数据
        List<IntlRmsPosition> entities = Arrays.asList(testPositionEntity, testPositionEntity);
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<PositionDomain> result = positionRepository.getByCityId("BJ");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("BJ", result.get(0).getCityId());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过城市ID获取阵地列表 - 空列表")
    void getByCityId_EmptyList() {
        // 准备数据
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        List<PositionDomain> result = positionRepository.getByCityId("NONEXISTENT");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过阵地类型获取阵地列表 - 成功")
    void getByPositionType_Success() {
        // 准备数据
        List<IntlRmsPosition> entities = Arrays.asList(testPositionEntity, testPositionEntity);
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(entities);

        // 执行测试
        List<PositionDomain> result = positionRepository.getByPositionType("1");

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.get(0).getPositionType());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    @Test
    @DisplayName("通过阵地类型获取阵地列表 - 空列表")
    void getByPositionType_EmptyList() {
        // 准备数据
        when(positionReadMapper.selectList(any(LambdaQueryWrapper.class))).thenReturn(Collections.emptyList());

        // 执行测试
        List<PositionDomain> result = positionRepository.getByPositionType("999");

        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(positionReadMapper).selectList(any(LambdaQueryWrapper.class));
    }

    // @Test
    // @DisplayName("领域对象转实体对象 - 正常转换")
    // @DisplayName("领域对象转实体对象 - 正常转换")
    void convertToEntity_NormalConversion() {
        // 执行测试
        boolean result = positionRepository.save(testPositionDomain);

        // 验证结果 - 通过mock验证转换是否正确
        verify(positionMapper).insert(argThat(entity -> 
            "POS001".equals(entity.getPositionId()) &&
            "CODE001".equals(entity.getCode()) &&
            "测试阵地".equals(entity.getName()) &&
            Integer.valueOf(1).equals(entity.getState()) &&
            "正常".equals(entity.getStateName()) &&
            Integer.valueOf(1).equals(entity.getType()) &&
            "门店".equals(entity.getTypeName()) &&
            "CN".equals(entity.getCountryId()) &&
            "中国".equals(entity.getCountryName()) &&
            "BJ".equals(entity.getCityId()) &&
            "北京".equals(entity.getCityName()) &&
            "北京市朝阳区".equals(entity.getAddress()) &&
            "STORE001".equals(entity.getStoreId()) &&
            "测试门店".equals(entity.getStoreName()) &&
            Integer.valueOf(1).equals(entity.getLevel()) &&
            "A级".equals(entity.getLevelName()) &&
            "RETAILER001".equals(entity.getRetailerId()) &&
            "测试零售商".equals(entity.getRetailerName()) &&
            Integer.valueOf(1).equals(entity.getChannelType()) &&
            "直营".equals(entity.getChannelTypeName()) &&
            "OWNER001".equals(entity.getOwnerId()) &&
            "测试负责人".equals(entity.getOwnerName()) &&
            Integer.valueOf(1).equals(entity.getIsPromotionStore()) &&
            Integer.valueOf(1).equals(entity.getStateCode()) &&
            Long.valueOf(1640995200L).equals(entity.getCreatedAt()) &&
            Long.valueOf(1640995200L).equals(entity.getUpdatedAt()) &&
            "华北".equals(entity.getArea()) &&
            "CRPS001".equals(entity.getCrpsCode()) &&
            Integer.valueOf(1).equals(entity.getDisplayCapacityExpansionStatus()) &&
            Integer.valueOf(1).equals(entity.getPositionLocation())
        ));
    }

    @Test
    @DisplayName("领域对象转实体对象 - 空值处理")
    void convertToEntity_NullValues() {
        // 准备数据
        PositionDomain domainWithNulls = new PositionDomain();
        domainWithNulls.setPositionId("POS001");
        domainWithNulls.setPositionCode("CODE001");
        domainWithNulls.setPositionName("测试阵地");
        // 其他字段保持null

        when(positionMapper.insert(any(IntlRmsPosition.class))).thenReturn(1);

        // 执行测试
        boolean result = positionRepository.save(domainWithNulls);

        // 验证结果
        assertTrue(result);
        verify(positionMapper).insert(argThat(entity -> 
            "POS001".equals(entity.getPositionId()) &&
            "CODE001".equals(entity.getCode()) &&
            "测试阵地".equals(entity.getName()) &&
            entity.getState() == null &&
            entity.getStateName() == null &&
            entity.getType() == null &&
            entity.getTypeName() == null
        ));
    }

    @Test
    @DisplayName("实体对象转领域对象 - 正常转换")
    void convertToDomain_NormalConversion() {
        // 准备数据
        when(positionReadMapper.selectById(1)).thenReturn(testPositionEntity);

        // 执行测试
        PositionDomain result = positionRepository.getById(1);

        // 验证结果
        assertNotNull(result);
        assertEquals("POS001", result.getPositionId());
        assertEquals("CODE001", result.getPositionCode());
        assertEquals("测试阵地", result.getPositionName());
        assertEquals("1", result.getPositionStatus());
        assertEquals("正常", result.getPositionStatusName());
        assertEquals(1, result.getPositionType());
        assertEquals("门店", result.getPositionTypeName());
        assertEquals("CN", result.getCountry());
        assertEquals("中国", result.getCountryName());
        assertEquals("BJ", result.getCityId());
        assertEquals("北京", result.getCityName());
        assertEquals("北京市朝阳区", result.getAddress());
        assertEquals("STORE001", result.getStoreId());
        assertEquals("测试门店", result.getStoreName());
        assertEquals(1, result.getLevel());
        assertEquals("A级", result.getLevelName());
        assertEquals("RETAILER001", result.getRetailerId());
        assertEquals("测试零售商", result.getRetailerName());
        assertEquals(1, result.getChannelType());
        assertEquals("直营", result.getChannelTypeName());
        assertEquals("OWNER001", result.getOwnerId());
        assertEquals("测试负责人", result.getOwnerName());
        assertTrue(result.getHasPromotion());
        assertTrue(result.getIsActive());
        assertEquals(1640995200L, result.getCreatedAt());
        assertEquals(1640995200L, result.getUpdatedAt());
        assertEquals("华北", result.getRegion());
        assertEquals("CRPS001", result.getCrpsCode());
        assertEquals(Arrays.asList(1, 2, 3), result.getPositionCategory());
        assertEquals(1, result.getDisplayCapacityExpansionStatus());
        assertEquals(1, result.getPositionLocation());
    }

    @Test
    @DisplayName("实体对象转领域对象 - 空值处理")
    void convertToDomain_NullValues() {
        // 准备数据
        IntlRmsPosition entityWithNulls = new IntlRmsPosition();
        entityWithNulls.setId(1);
        entityWithNulls.setPositionId("POS001");
        entityWithNulls.setCode("CODE001");
        entityWithNulls.setName("测试阵地");
        // 其他字段保持null

        when(positionReadMapper.selectById(1)).thenReturn(entityWithNulls);

        // 执行测试
        PositionDomain result = positionRepository.getById(1);

        // 验证结果
        assertNotNull(result);
        assertEquals("POS001", result.getPositionId());
        assertEquals("CODE001", result.getPositionCode());
        assertEquals("测试阵地", result.getPositionName());
        assertNull(result.getPositionStatus());
        assertNull(result.getPositionStatusName());
        assertNull(result.getPositionType());
        assertNull(result.getPositionTypeName());
        assertNull(result.getCountry());
        assertNull(result.getCountryName());
        assertNull(result.getCityId());
        assertNull(result.getCityName());
        assertNull(result.getAddress());
        assertNull(result.getStoreId());
        assertNull(result.getStoreName());
        assertNull(result.getLevel());
        assertNull(result.getLevelName());
        assertNull(result.getRetailerId());
        assertNull(result.getRetailerName());
        assertNull(result.getChannelType());
        assertNull(result.getChannelTypeName());
        assertNull(result.getOwnerId());
        assertNull(result.getOwnerName());
        assertFalse(result.getHasPromotion());
        assertFalse(result.getIsActive());
        assertNull(result.getCreatedAt());
        assertNull(result.getUpdatedAt());
        assertNull(result.getRegion());
        assertNull(result.getCrpsCode());
        assertTrue(result.getPositionCategory().isEmpty());
        assertNull(result.getDisplayCapacityExpansionStatus());
        assertNull(result.getPositionLocation());
    }

    @Test
    @DisplayName("解析阵地品类字符串 - 正常解析")
    void getPositionCategoryList_NormalParse() {
        // 准备数据
        IntlRmsPosition entity = new IntlRmsPosition();
        entity.setPositionCategory("[1,2,3]");
        when(positionReadMapper.selectById(1)).thenReturn(entity);

        // 执行测试
        PositionDomain result = positionRepository.getById(1);

        // 验证结果
        assertNotNull(result);
        assertEquals(Arrays.asList(1, 2, 3), result.getPositionCategory());
    }

    @Test
    @DisplayName("解析阵地品类字符串 - 空字符串")
    void getPositionCategoryList_EmptyString() {
        // 准备数据
        IntlRmsPosition entity = new IntlRmsPosition();
        entity.setPositionCategory("");
        when(positionReadMapper.selectById(1)).thenReturn(entity);

        // 执行测试
        PositionDomain result = positionRepository.getById(1);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getPositionCategory().isEmpty());
    }

    @Test
    @DisplayName("解析阵地品类字符串 - null值")
    void getPositionCategoryList_NullValue() {
        // 准备数据
        IntlRmsPosition entity = new IntlRmsPosition();
        entity.setPositionCategory(null);
        when(positionReadMapper.selectById(1)).thenReturn(entity);

        // 执行测试
        PositionDomain result = positionRepository.getById(1);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getPositionCategory().isEmpty());
    }

    @Test
    @DisplayName("解析阵地品类字符串 - 格式错误")
    void getPositionCategoryList_InvalidFormat() {
        // 准备数据
        IntlRmsPosition entity = new IntlRmsPosition();
        entity.setPositionCategory("invalid json");
        when(positionReadMapper.selectById(1)).thenReturn(entity);

        // 执行测试
        PositionDomain result = positionRepository.getById(1);

        // 验证结果
        assertNotNull(result);
        assertTrue(result.getPositionCategory().isEmpty());
    }
} 