package com.mi.info.intl.retail.org.domain.position.service.impl;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.cooperation.task.dto.dto.IntlRmsUserDto;
import com.mi.info.intl.retail.cooperation.task.inspection.IntlRmsUserService;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.TaskCenterFinishReq;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.ConstructionType;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.DisapproveReasonEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.StoreLimitedRangeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.TaskStatusEnum;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.taskcenter.TaskCenterFinishTaskReq;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.PositionInspectionResponsiblePersonDTO;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.org.domain.InspectionHistoryDomain;
import com.mi.info.intl.retail.org.domain.InspectionRecordDomain;
import com.mi.info.intl.retail.org.domain.PositionCommonItemList;
import com.mi.info.intl.retail.org.domain.PositionDomain;
import com.mi.info.intl.retail.org.domain.enums.OperationType;
import com.mi.info.intl.retail.org.domain.repository.InspectionHistoryRepository;
import com.mi.info.intl.retail.org.domain.repository.InspectionRecordRepository;
import com.mi.info.intl.retail.org.domain.repository.PositionRepository;
import com.mi.info.intl.retail.org.domain.repository.RuleConfigRepository;
import com.mi.info.intl.retail.org.domain.RuleConfigDomain;
import com.mi.info.intl.retail.org.infra.mapper.read.PositionResponsiblePersonMapper;
import com.mi.info.intl.retail.org.infra.rpc.StoreRelateRpc;
import com.mi.info.intl.retail.org.infra.rpc.TaskCenterServiceRpc;
import com.xiaomi.cnzone.maindataapi.api.PositionProvider;
import com.xiaomi.cnzone.maindataapi.model.dto.store.ListPositionInfoResponse;
import com.xiaomi.cnzone.maindataapi.model.dto.store.PositionListResponse;
import com.xiaomi.cnzone.maindataapi.model.req.store.PositionFurniture;
import com.xiaomi.cnzone.storeapi.api.channelbuild.common.ChannelFurnitureProvider;
import com.xiaomi.cnzone.storeapi.api.channelbuild.position.BuildChannelPositionProvider;
import com.xiaomi.cnzone.storeapi.model.channelbuild.business.resp.PositionImageCenterResp;
import com.xiaomi.cnzone.storeapi.model.channelbuild.common.resp.ConfigFurnitureResp;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.InitInspectionByPositionCodeRequest;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

import com.xiaomi.nr.global.dev.base.RequestContextInfo;
import com.xiaomi.youpin.infra.rpc.Result;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;

import static org.mockito.ArgumentMatchers.any;

import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import static org.mockito.Mockito.when;

import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.mockito.Mockito.doNothing;

import com.fasterxml.jackson.core.JsonProcessingException;

import static org.mockito.Mockito.doReturn;

import com.mi.info.intl.retail.intlretail.service.api.position.dto.ImageCenterDto;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PhotoGroup;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.UploadData;

import java.util.Collections;

import com.xiaomi.cnzone.maindataapi.model.dto.store.ListPositionInfoResponse.PositionInfo;
import com.xiaomi.cnzone.storeapi.model.channelbuild.business.resp.ChannelBaseV1Resp;


@ExtendWith(MockitoExtension.class)
public class PositionInspectionDomainServiceImplTest {
    @InjectMocks
    private PositionInspectionDomainServiceImpl service;
    @Mock
    private InspectionRecordRepository inspectionRecordRepository;
    @Mock
    private PositionRepository positionRepository;
    @Mock
    private StoreRelateRpc storeRelateRpc;
    @Mock
    private BuildChannelPositionProvider buildChannelPositionProvider;
    @Mock
    private InspectionHistoryRepository inspectionHistoryRepository;
    @Mock
    private PositionProvider positionProvider;
    @Mock
    private ChannelFurnitureProvider channelFurnitureProvider;
    @Mock
    private IntlFileUploadService fileUploadService;
    @Mock
    private RuleConfigRepository ruleConfigRepository;
    @Mock
    private IntlRmsUserService intlRmsUserService;
    @Mock
    private ObjectMapper objectMapper;
    @Mock
    private TaskCenterServiceRpc taskCenterServiceRpc;
    @Mock
    private PositionResponsiblePersonMapper positionResponsiblePersonMapper;

    private MockedStatic<RequestContextInfo> mockedStaticRequestContextInfo;

    @BeforeEach
    public void setUp() {
        mockedStaticRequestContextInfo = Mockito.mockStatic(RequestContextInfo.class);
    }

    @AfterEach
    public void tearDown() {
        mockedStaticRequestContextInfo.close();
    }

    @Test
    public void testGetPositionInspectionAllDetail_success_with_completed_task() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(305L);

        // 构造巡检记录 - 已完成任务
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(305L);
        record.setPositionCode("CID001971");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
        record.setActionCode("ACTION001");
        record.setUploadData("{\"storeGate\": {\"guid\": \"guid1\", \"name\": \"\", \"images\": [\"url1\", \"url2\"]}, \"positionDisplay\": {\"guid\": \"guid2\", \"name\": \"\", \"images\": [\"url3\", \"url4\"]}}");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("CID001971");
        positionDomain.setPositionName("Test Position");
        positionDomain.setCountryName("India");
        positionDomain.setPositionTypeName("Retail");
        positionDomain.setPositionCategory(Arrays.asList(1, 2));
        positionDomain.setPositionLocation(1);
        positionDomain.setDisplayCapacityExpansionStatus(1);

        // Mock 方法调用
        when(inspectionRecordRepository.getById(305L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001971")).thenReturn(positionDomain);
        when(buildChannelPositionProvider.imageCenter(any())).thenReturn(Result.success(Collections.emptyList()));
        when(storeRelateRpc.get3CCommonPositionItemList(any(), any(), any())).thenReturn(new PositionCommonItemList());

        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse posResponse = new PositionListResponse();
        posResponse.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(posResponse);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        ConfigFurnitureResp resp = new ConfigFurnitureResp();
        resp.setFurnitureId("F001");
        resp.setFurnitureName("FName001");
        resp.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any())).thenReturn(
                Result.success(Collections.singletonList(resp)));
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position", response.getData().getPositionBasicInfo().getPositionName());
        assertEquals("India", response.getData().getPositionBasicInfo().getCountry());
        assertNotNull(response.getData().getPhotoGalleries());
    }

    @Test
    public void testGetPositionInspectionAllDetail_success_with_incomplete_task() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(306L);

        // 构造巡检记录 - 未完成任务
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(306L);
        record.setPositionCode("CID001972");
        record.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
        record.setInspectionStatus(InspectionStatusEnum.NOT_COMPLETED);

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("CID001972");
        positionDomain.setPositionName("Test Position 2");

        // Mock 方法调用
        when(inspectionRecordRepository.getById(306L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001972")).thenReturn(positionDomain);
        when(storeRelateRpc.get3CCommonPositionItemList(any(), any(), any())).thenReturn(new PositionCommonItemList());

        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse posResponse = new PositionListResponse();
        posResponse.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(posResponse);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        ConfigFurnitureResp resp = new ConfigFurnitureResp();
        resp.setFurnitureId("F001");
        resp.setFurnitureName("FName001");
        resp.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any())).thenReturn(
                Result.success(Collections.singletonList(resp)));
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position 2", response.getData().getPositionBasicInfo().getPositionName());
        // 未完成任务不应该有图片数据
        assertNotNull(response.getData().getPhotoGalleries());
    }

    @Test
    public void testGetPositionInspectionAllDetail_invalid_request() {
        // 测试空ID
        PositionInspectionDetailRequest request1 = new PositionInspectionDetailRequest();
        request1.setPositionInspectionId(null);
        CommonApiResponse<PositionInspectionAllDetailDTO> response1 = service.getPositionInspectionAllDetail(request1);
        assertEquals(500, response1.getCode());
        assertEquals("阵地巡检ID不能为空", response1.getMessage());

        // 测试无效ID
        PositionInspectionDetailRequest request2 = new PositionInspectionDetailRequest();
        request2.setPositionInspectionId(0L);
        CommonApiResponse<PositionInspectionAllDetailDTO> response2 = service.getPositionInspectionAllDetail(request2);
        assertEquals(500, response2.getCode());
        assertEquals("阵地巡检ID不能为空", response2.getMessage());
    }

    @Test
    public void testGetPositionInspectionAllDetail_record_not_found() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(999L);

        // Mock 巡检记录不存在
        when(inspectionRecordRepository.getById(999L)).thenReturn(null);

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(500, response.getCode());
        assertEquals("阵地巡检记录不存在", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionAllDetail_position_not_found() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(307L);

        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(307L);
        record.setPositionCode("CID001973");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);

        // Mock 巡检记录存在但阵地信息不存在
        when(inspectionRecordRepository.getById(307L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001973")).thenReturn(null);

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(500, response.getCode());
        assertEquals("阵地信息不存在", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionAllDetail_with_exception() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(308L);

        // Mock 抛出异常
        when(inspectionRecordRepository.getById(308L)).thenThrow(new RuntimeException("Database error"));

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("系统异常"));
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionAllDetail_with_invalid_upload_data() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(309L);

        // 构造巡检记录 - 包含无效的JSON数据
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(309L);
        record.setPositionCode("CID001974");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setUploadData("invalid json data");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("CID001974");
        positionDomain.setPositionName("Test Position 3");

        // Mock 方法调用
        when(inspectionRecordRepository.getById(309L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001974")).thenReturn(positionDomain);
        when(buildChannelPositionProvider.imageCenter(any())).thenReturn(Result.success(Collections.emptyList()));
        when(storeRelateRpc.get3CCommonPositionItemList(any(), any(), any())).thenReturn(new PositionCommonItemList());

        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse posResponse = new PositionListResponse();
        posResponse.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(posResponse);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        ConfigFurnitureResp resp = new ConfigFurnitureResp();
        resp.setFurnitureId("F001");
        resp.setFurnitureName("FName001");
        resp.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any())).thenReturn(
                Result.success(Collections.singletonList(resp)));
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");

        // 执行测试 - 应该能正常处理，不会因为JSON解析失败而抛出异常
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position 3", response.getData().getPositionBasicInfo().getPositionName());
    }

    @Test
    public void testGetPositionInspectionAllDetail_with_build_channel_images() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(310L);

        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(310L);
        record.setPositionCode("CID001975");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setActionCode("ACTION002");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("CID001975");
        positionDomain.setPositionName("Test Position 4");

        // 构造建店图片数据
        List<PositionImageCenterResp> imageCenterRespList = new ArrayList<>();
        PositionImageCenterResp resp = new PositionImageCenterResp();
        resp.setStoreGate(Arrays.asList("url1", "url2"));
        resp.setPositionDisplay(Arrays.asList("url3", "url4"));
        imageCenterRespList.add(resp);

        // Mock 方法调用
        when(inspectionRecordRepository.getById(310L)).thenReturn(record);
        when(positionRepository.getByPositionCode("CID001975")).thenReturn(positionDomain);
        when(buildChannelPositionProvider.imageCenter(any())).thenReturn(Result.success(imageCenterRespList));
        when(storeRelateRpc.get3CCommonPositionItemList(any(), any(), any())).thenReturn(new PositionCommonItemList());

        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse posResponse = new PositionListResponse();
        posResponse.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(posResponse);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        ConfigFurnitureResp resps = new ConfigFurnitureResp();
        resps.setFurnitureId("F001");
        resps.setFurnitureName("FName001");
        resps.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any())).thenReturn(
                Result.success(Collections.singletonList(resps)));
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");

        // 执行测试
        CommonApiResponse<PositionInspectionAllDetailDTO> response = service.getPositionInspectionAllDetail(request);

        // 验证结果
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position 4", response.getData().getPositionBasicInfo().getPositionName());
        assertNotNull(response.getData().getPhotoGalleries());
        assertNotNull(response.getData().getPhotoGalleries().getPositionCreation());
    }

    @Test
    public void testGetSelectorList_success() {
        // 构造请求
        PositionItemListRequest request = new PositionItemListRequest();
        request.setInternationalAreaId("area1");
        request.setBusinessScene("scene1");
        // 构造mock返回
        PositionCommonItemList commonItemList = new PositionCommonItemList();
        commonItemList.setPositionCategory(Collections.singletonList(new OptionalItem<>("cat1", "品类1")));
        commonItemList.setPositionType(Collections.singletonList(new OptionalItem<>("type1", "类型1")));
        commonItemList.setPositionLocation(Collections.singletonList(new OptionalItem<>("loc1", "位置1")));
        commonItemList.setDisplayStandardization(Collections.singletonList(new OptionalItem<>("std1", "标准1")));
        when(storeRelateRpc.get3CCommonPositionItemList(Mockito.anyString(), Mockito.anyString(),
                Mockito.anyString()))
                .thenReturn(commonItemList);
        mockedStaticRequestContextInfo.when(RequestContextInfo::getLanguage).thenReturn("zh-CN");
        PositionSelectorItemList result = service.getSelectorList(request);
        assertNotNull(result);
        assertEquals(1, result.getPositionCategory().size());
        assertEquals("cat1", result.getPositionCategory().get(0).getKey());
        assertEquals("品类1", result.getPositionCategory().get(0).getValue());
        assertEquals(1, result.getPositionType().size());
        assertEquals("type1", result.getPositionType().get(0).getKey());
        assertEquals("类型1", result.getPositionType().get(0).getValue());
        assertEquals(1, result.getPositionLocation().size());
        assertEquals("loc1", result.getPositionLocation().get(0).getKey());
        assertEquals("位置1", result.getPositionLocation().get(0).getValue());
        assertEquals(1, result.getDisplayStandardization().size());
        assertEquals("std1", result.getDisplayStandardization().get(0).getKey());
        assertEquals("标准1", result.getDisplayStandardization().get(0).getValue());
        // 校验枚举下拉项不为空
        assertEquals(TaskStatusEnum.values().length - 1, result.getTaskStatus().size());
        assertEquals(InspectionStatusEnum.values().length, result.getInspectionStatus().size());
        assertEquals(ConstructionType.values().length, result.getPositionConstructionType().size());
        assertEquals(DisapproveReasonEnum.values().length, result.getDisapproveReason().size());
        assertEquals(StoreLimitedRangeEnum.values().length, result.getStoreLimitedRange().size());
    }

    @Test
    public void testGetPositionFurnitureList_old_furniture_success() {
        // 构造请求
        PositionFurnitureRequest request = new PositionFurnitureRequest();
        request.setPositionCode("P001");
        // 构造mock返回
        PositionListResponse.FurnitureDisplay furnitureDisplay = new PositionListResponse.FurnitureDisplay();
        furnitureDisplay.setSlotEndCapTable1(1); // 有库存
        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        extension.setFurnitureDisplay(furnitureDisplay);
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse response = new PositionListResponse();
        response.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(response);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);
        // 调用
        List<OptionalItem<Integer>> result = service.getPositionFurnitureList(request);
        // 断言
        assertNotNull(result);
        assertEquals("1 Slot-End Cap Table", result.get(0).getValue());
    }

    @Test
    public void testGetPositionFurnitureList_new_furniture_success() {
        // 构造请求
        PositionFurnitureRequest request = new PositionFurnitureRequest();
        request.setPositionCode("P001");
        // 构造mock返回
        PositionListResponse.PositionExtra extension = new PositionListResponse.PositionExtra();
        PositionFurniture positionFurniture = new PositionFurniture();
        positionFurniture.setFurnitureCode("F001");
        positionFurniture.setFurnitureCount(3);
        extension.setFurnitureList(Collections.singletonList(positionFurniture));
        PositionListResponse.StorePositionInfo positionInfo = new PositionListResponse.StorePositionInfo();
        positionInfo.setPositionExtra(extension);
        PositionListResponse response = new PositionListResponse();
        response.setList(Collections.singletonList(positionInfo));
        Result<PositionListResponse> rpcResult = Result.success(response);
        when(positionProvider.listStorePosition(any())).thenReturn(rpcResult);

        // 必须mock channelFurnitureProvider.queryFurnitureList
        ConfigFurnitureResp resp = new ConfigFurnitureResp();
        resp.setFurnitureId("F001");
        resp.setFurnitureName("FName001");
        resp.setUniqueFurnitureName("uniqueFName");
        when(channelFurnitureProvider.queryFurnitureList(any()))
                .thenReturn(Result.success(Collections.singletonList(resp)));

        // 调用
        List<OptionalItem<Integer>> result = service.getPositionFurnitureList(request);
        // 断言
        assertEquals(3, result.size());
        assertEquals("uniqueFName", result.get(0).getValue());
    }

    @Test
    public void testOperationHistory_success() {
        // 构造巡检记录
        Long inspectionId = 123L;
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(inspectionId);
        record.setPositionCode("P001");
        // 构造历史记录
        InspectionHistoryDomain history = new InspectionHistoryDomain();
        history.setId(1L);
        history.setInspectionRecordId(inspectionId);
        history.setOperationType(OperationType.SUBMIT);
        history.setRemark("remark");
        history.setDisapproveReason(2);
        history.setOperator("tester");
        history.setOperatorName("tester"); // 添加operatorName字段
        history.setOperationTime(123456789L);
        history.setCreateTime(123456789L);
        List<InspectionHistoryDomain> historyList = Collections.singletonList(history);
        // mock依赖
        when(inspectionRecordRepository.getById(inspectionId)).thenReturn(record);
        when(inspectionHistoryRepository.getByInspectionRecordId(inspectionId)).thenReturn(historyList);
        // 调用
        List<PositionInspectionHistoryItem> result = service.operationHistory(inspectionId);
        // 断言
        assertNotNull(result);
        assertEquals(1, result.size());
        PositionInspectionHistoryItem item = result.get(0);
        assertEquals("tester", item.getOperator());
    }

    @Test
    public void testOperationHistory_not_found() {
        List<PositionInspectionHistoryItem> historyItems = service.operationHistory(1L);
        assertEquals(Collections.emptyList(), historyItems);
    }

    @Test
    public void testGetPositionInspectionDetail_success_with_completed_task() {
        // 构造请求
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(1001L);

        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(1001L);
        record.setPositionCode("P001");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setInspectionStatus(InspectionStatusEnum.TO_BE_VERIFIED);
        record.setUploadData("{\"storeGate\": {\"guid\": \"guid1\", \"name\": \"\", \"images\": [\"url1\"]}}");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("P001");
        positionDomain.setPositionName("Test Position");

        // Mock
        when(inspectionRecordRepository.getById(1001L)).thenReturn(record);
        when(positionRepository.getByPositionCode("P001")).thenReturn(positionDomain);

        // 执行
        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);

        // 验证
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position", response.getData().getStoreInfo().getFrontName());
    }

    @Test
    public void testGetPositionInspectionDetail_invalid_request() {
        // 空ID
        PositionInspectionDetailRequest request1 = new PositionInspectionDetailRequest();
        request1.setPositionInspectionId(null);
        CommonApiResponse<PositionInspectionDetailResponse> response1 = service.getPositionInspectionDetail(request1);
        assertEquals(500, response1.getCode());
        assertEquals("阵地巡检ID不能为空", response1.getMessage());

        // 非法ID
        PositionInspectionDetailRequest request2 = new PositionInspectionDetailRequest();
        request2.setPositionInspectionId(0L);
        CommonApiResponse<PositionInspectionDetailResponse> response2 = service.getPositionInspectionDetail(request2);
        assertEquals(500, response2.getCode());
        assertEquals("阵地巡检ID不能为空", response2.getMessage());
    }

    @Test
    public void testGetPositionInspectionDetail_record_not_found() {
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(2001L);
        when(inspectionRecordRepository.getById(2001L)).thenReturn(null);

        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);
        assertEquals(500, response.getCode());
        assertEquals("阵地巡检记录不存在", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionDetail_position_not_found() {
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(2002L);

        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(2002L);
        record.setPositionCode("P002");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);

        when(inspectionRecordRepository.getById(2002L)).thenReturn(record);
        when(positionRepository.getByPositionCode("P002")).thenReturn(null);

        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);
        assertEquals(500, response.getCode());
        assertEquals("阵地信息不存在", response.getMessage());
        assertNull(response.getData());
    }

    @Test
    public void testGetPositionInspectionDetail_with_invalid_upload_data() {
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(2003L);

        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(2003L);
        record.setPositionCode("P003");
        record.setTaskStatus(TaskStatusEnum.COMPLETED);
        record.setUploadData("invalid json");

        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("P003");
        positionDomain.setPositionName("Test Position 3");

        when(inspectionRecordRepository.getById(2003L)).thenReturn(record);
        when(positionRepository.getByPositionCode("P003")).thenReturn(positionDomain);

        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);
        assertEquals(0, response.getCode());
        assertNotNull(response.getData());
        assertEquals("Test Position 3", response.getData().getStoreInfo().getFrontName());
    }

    @Test
    public void testGetPositionInspectionDetail_with_exception() {
        PositionInspectionDetailRequest request = new PositionInspectionDetailRequest();
        request.setPositionInspectionId(2004L);

        when(inspectionRecordRepository.getById(2004L)).thenThrow(new RuntimeException("DB error"));

        CommonApiResponse<PositionInspectionDetailResponse> response = service.getPositionInspectionDetail(request);
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("系统异常"));
        assertNull(response.getData());
    }

    @Test
    public void testSubmitPositionInspection_success() {
        // 构造请求
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(1L);
        request.setLatitude(10.0);
        request.setLongitude(20.0);
        request.setPositionLatitude(10.0);
        request.setPositionLongitude(20.0);
        request.setOwner("owner1");
        request.setNoNeedToComplete(false);
        request.setIsOffline(false);

        List<String> storeGateImages = Arrays.asList(
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png",
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png",
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png"
        );
        PhotoGroup storeGate = new PhotoGroup();
        storeGate.setGuid("d9922231-3590-41e5-ad92-49239138e4b6");
        storeGate.setName("2.zip");
        storeGate.setImages(storeGateImages);

        List<String> positionDisplayImages = Arrays.asList(
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png",
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png",
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png"
        );
        PhotoGroup positionDisplay = new PhotoGroup();
        positionDisplay.setGuid("7fb9c6c8-75ab-43a2-ab0f-3be96345a749");
        positionDisplay.setName("2.zip");
        positionDisplay.setImages(positionDisplayImages);

        List<String> positionLandingPhotoImages = Arrays.asList(
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png",
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png",
                "https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png"
        );
        PhotoGroup positionLandingPhoto = new PhotoGroup();
        positionLandingPhoto.setGuid("7da73f39-0fdb-4eb4-839b-20f00a50cd4e");
        positionLandingPhoto.setName("2.zip");
        positionLandingPhoto.setImages(positionLandingPhotoImages);

        FurniturePhotoGroup furniture1 = new FurniturePhotoGroup();
        furniture1.setGuid("ba4b04e3-fe1c-4eb5-b520-17b3af813724");
        furniture1.setName("f1");
        furniture1.setImages(Collections.singletonList("https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png"));
        furniture1.setReason(1);


        FurniturePhotoGroup furniture2 = new FurniturePhotoGroup();
        furniture2.setGuid("b3625fc5-c1c4-49d7-bd4a-433c66428f18");
        furniture2.setName("f2");
        furniture2.setImages(Collections.singletonList("https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png"));
        furniture2.setReason(2);
        List<FurniturePhotoGroup> furniturePictures = Arrays.asList(furniture1, furniture2);

        // 设置到request
        request.setStoreGate(storeGate);
        request.setPositionDisplay(positionDisplay);
        request.setPositionLandingPhoto(positionLandingPhoto);
        request.setFurniturePictures(furniturePictures);

        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(1L);
        record.setPositionCode("P001");
        record.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
        record.setInspectionOwnerMiId(123L);
        record.setInspectionOwner("owner1");
        record.setRuleCode("RULE1");
        record.setPositionCreationTime(System.currentTimeMillis());
        record.setUploadData("{\"storeGate\": {\"guid\": \"d9922231-3590-41e5-ad92-49239138e4b6\", \"name\": \"2.zip\", \"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png\", \"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png\", \"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}, \"positionDisplay\": {\"guid\": \"7fb9c6c8-75ab-43a2-ab0f-3be96345a749\", \"name\": \"2.zip\", \"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png\", \"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png\", \"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}, \"furniturePictures\": [{\"guid\": \"ba4b04e3-fe1c-4eb5-b520-17b3af813724\", \"name\": \"f1\", \"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}, {\"guid\": \"b3625fc5-c1c4-49d7-bd4a-433c66428f18\", \"name\": \"f2\", \"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}], \"positionLandingPhoto\": {\"guid\": \"7da73f39-0fdb-4eb4-839b-20f00a50cd4e\", \"name\": \"2.zip\", \"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png\", \"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png\", \"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}}");

        // 构造阵地信息
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("P001");

        // 构造用户
        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        // Mock
        when(inspectionRecordRepository.getById(1L)).thenReturn(record);
        when(positionRepository.getByPositionCode("P001")).thenReturn(positionDomain);
        when(ruleConfigRepository.getByRuleCode(any())).thenReturn(new RuleConfigDomain());
        when(intlRmsUserService.getIntlRmsUserByDomainName(any())).thenReturn(userDto);
        when(inspectionRecordRepository.update(any())).thenReturn(true);
        when(inspectionHistoryRepository.save(any())).thenReturn(true);
        doNothing().when(taskCenterServiceRpc).outerTaskFinish(any(TaskCenterFinishReq.class));

        try {
            doReturn("{\"storeGate\": {\"guid\": \"d9922231-3590-41e5-ad92-49239138e4b6\"," +
                    " \"name\": \"2.zip\", \"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png\"," +
                    " \"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png\"," +
                    " \"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}," +
                    " \"positionDisplay\": {\"guid\": \"7fb9c6c8-75ab-43a2-ab0f-3be96345a749\", " +
                    "\"name\": \"2.zip\", " +
                    "\"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png\", " +
                    "\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png\", " +
                    "\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}, " +
                    "\"furniturePictures\": [{\"guid\": \"ba4b04e3-fe1c-4eb5-b520-17b3af813724\", \"name\": \"f1\", " +
                    "\"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}, " +
                    "{\"guid\": \"b3625fc5-c1c4-49d7-bd4a-433c66428f18\", \"name\": \"f2\"," +
                    " \"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}], " +
                    "\"positionLandingPhoto\": {\"guid\": \"7da73f39-0fdb-4eb4-839b-20f00a50cd4e\", \"name\": \"2.zip\", " +
                    "\"images\": [\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本 (2).png\", " +
                    "\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34 - 副本.png\", " +
                    "\"https://smc-india.staging-cnbj2.mi-fds.com/smc-india/Snipaste_2025-07-15_21-29-34.png\"]}}").when(objectMapper).writeValueAsString(any());
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }

        // 执行
        CommonApiResponse<String> response = service.submitPositionInspection(request);

        // 验证
        assertEquals(0, response.getCode());
    }

    @Test
    public void testSubmitPositionInspection_param_invalid() {
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(null);
        CommonApiResponse<String> response = service.submitPositionInspection(request);
        assertEquals(500, response.getCode());
        assertEquals("The request parameters are incomplete", response.getMessage());
    }

    @Test
    public void testSubmitPositionInspection_record_not_found() {
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(2L);
        when(inspectionRecordRepository.getById(2L)).thenReturn(null);
        CommonApiResponse<String> response = service.submitPositionInspection(request);
        assertEquals(500, response.getCode());
        assertEquals("Position inspection record does not exist", response.getMessage());
    }

    @Test
    public void testSubmitPositionInspection_status_invalid() {
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(3L);

        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(3L);
        record.setTaskStatus(TaskStatusEnum.COMPLETED);

        when(inspectionRecordRepository.getById(3L)).thenReturn(record);
        CommonApiResponse<String> response = service.submitPositionInspection(request);
        assertEquals(500, response.getCode());
        assertEquals("The status of the position inspection record is not incomplete", response.getMessage());
    }

    @Test
    public void testSubmitPositionInspection_location_invalid() {
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(4L);
        request.setLatitude(null);
        request.setLongitude(null);
        request.setPositionLatitude(null);
        request.setPositionLongitude(null);
        // 构造用户
        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setInspectionOwnerMiId(123L);
        record.setInspectionOwner("owner1");
        record.setId(4L);
        record.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);

        when(intlRmsUserService.getIntlRmsUserByDomainName(any())).thenReturn(userDto);
        when(inspectionRecordRepository.getById(4L)).thenReturn(record);
        CommonApiResponse<String> response = service.submitPositionInspection(request);
        assertEquals(500, response.getCode());
        assertEquals("Position inspection location information is incomplete", response.getMessage());
    }

    @Test
    public void testSubmitPositionInspection_position_not_found() {
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(5L);
        request.setLatitude(10.0);
        request.setLongitude(20.0);
        request.setPositionLatitude(10.0);
        request.setPositionLongitude(20.0);
        request.setOwner("owner1");

        // 构造用户
        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setInspectionOwnerMiId(123L);
        record.setInspectionOwner("owner1");
        record.setId(5L);
        record.setPositionCode("P005");
        record.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);

        when(inspectionRecordRepository.getById(5L)).thenReturn(record);
        when(intlRmsUserService.getIntlRmsUserByDomainName(any())).thenReturn(userDto);
        when(positionRepository.getByPositionCode("P005")).thenReturn(null);

        CommonApiResponse<String> response = service.submitPositionInspection(request);
        assertEquals(500, response.getCode());
        assertEquals("Position information does not exist", response.getMessage());
    }

    @Test
    public void testSubmitPositionInspection_update_fail() {
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(6L);
        request.setLatitude(10.0);
        request.setLongitude(20.0);
        request.setPositionLatitude(10.0);
        request.setPositionLongitude(20.0);
        request.setOwner("owner1");
        request.setNoNeedToComplete(false);
        request.setIsOffline(false);

        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setId(6L);
        record.setPositionCode("P006");
        record.setTaskStatus(TaskStatusEnum.NOT_COMPLETED);
        record.setInspectionOwnerMiId(123L);
        record.setInspectionOwner("owner1");
        record.setRuleCode("RULE1");
        record.setPositionCreationTime(System.currentTimeMillis());

        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("P006");

        IntlRmsUserDto userDto = IntlRmsUserDto.builder().miId(123L).build();

        when(inspectionRecordRepository.getById(6L)).thenReturn(record);
        when(positionRepository.getByPositionCode("P006")).thenReturn(positionDomain);
        when(ruleConfigRepository.getByRuleCode(any())).thenReturn(new RuleConfigDomain());
        when(intlRmsUserService.getIntlRmsUserByDomainName(any())).thenReturn(userDto);
        when(inspectionRecordRepository.update(any())).thenReturn(false);

        CommonApiResponse<String> response = service.submitPositionInspection(request);
        assertEquals(500, response.getCode());
        assertEquals("Data save failed", response.getMessage());
    }

    @Test
    public void testSubmitPositionInspection_with_exception() {
        PositionInspectionSubmitRequest request = new PositionInspectionSubmitRequest();
        request.setPositionInspectionId(7L);

        when(inspectionRecordRepository.getById(7L)).thenThrow(new RuntimeException("DB error"));

        CommonApiResponse<String> response = service.submitPositionInspection(request);
        assertEquals(500, response.getCode());
        assertTrue(response.getMessage().contains("System Exception"));
    }

    @Test
    public void testInitInspectionByPositionCode_success() {
        InitInspectionByPositionCodeRequest request = new InitInspectionByPositionCodeRequest();
        request.setCountryId("CN");
        request.setPositionCode("P001");
        request.setActionType(0);
        request.setActionTime(System.currentTimeMillis());

        // mock positionRepository.getByCode
        PositionDomain positionDomain = new PositionDomain();
        positionDomain.setPositionCode("P001");
        // 构造 ListPositionInfoResponse
        ListPositionInfoResponse listPositionInfoResponse = new ListPositionInfoResponse();
        ListPositionInfoResponse.PositionInfo positionInfo = new ListPositionInfoResponse.PositionInfo();
        positionInfo.setPositionCode("P001");
        listPositionInfoResponse.setList(Collections.singletonList(positionInfo));

        // 构造 Result
        Result<ListPositionInfoResponse> positionInfoResult = Result.success(listPositionInfoResponse);
        // 构造 PositionInspectionResponsiblePersonDTO
        PositionInspectionResponsiblePersonDTO responsiblePerson = new PositionInspectionResponsiblePersonDTO();
        responsiblePerson.setUserId("user1");
        responsiblePerson.setUserName("负责人");
        responsiblePerson.setMiId(123L);

        // mock getResponsiblePersonByPositionCode

        String positionCode = "P001";
        // 构造返回的负责人信息
        Map<String, Object> person = new HashMap<>();
        person.put("user_id", "user1");
        person.put("user_name", "负责人");
        person.put("user_title", "Promoter");
        person.put("user_title_code", 1);
        person.put("mi_id", 123L);
        person.put("language_code", "zh");
        person.put("psition_code", positionCode);
        person.put("created_on", System.currentTimeMillis());

        List<Map<String, Object>> results = Collections.singletonList(person);

        // mock positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson
        when(positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson(positionCode)).thenReturn(results);
        // mock positionProvider.listPositionInfo
        when(positionProvider.listPositionInfo(any())).thenReturn(positionInfoResult);
        //when(positionRepository.getByCode("P001")).thenReturn(positionDomain);

        // mock ruleConfigRepository.findValidRuleConfigsByCountry
        RuleConfigDomain ruleConfig = new RuleConfigDomain();
        ruleConfig.setStartTime(12345L);
        ruleConfig.setEndTime(12345L);
        ruleConfig.setReminderDays(2);
//        when(ruleConfigRepository.findValidRuleConfigsByCountry("CN")).thenReturn(Collections.singletonList(ruleConfig));

        // mock inspectionRecordRepository.save
        when(inspectionRecordRepository.save(any())).thenReturn(true);
        //doReturn(responsiblePerson).when(service).getResponsiblePersonByPositionCode("P001");

        // mock其他依赖如有需要...

        String result = service.initInspectionByPositionCode(request);
        assertNotNull(result);
        // 可根据实际返回内容调整
        // assertTrue(result.contains("成功"));
    }

    @Test
    public void testInitInspectionByPositionCode_rule_not_found() {
        InitInspectionByPositionCodeRequest request = new InitInspectionByPositionCodeRequest();
        request.setCountryId("CN");
        request.setPositionCode("P002");
        request.setActionType(0);
        request.setActionTime(System.currentTimeMillis());

        // 构造 ListPositionInfoResponse
        ListPositionInfoResponse listPositionInfoResponse = new ListPositionInfoResponse();
        ListPositionInfoResponse.PositionInfo positionInfo = new ListPositionInfoResponse.PositionInfo();
        positionInfo.setPositionCode("P001");
        listPositionInfoResponse.setList(Collections.singletonList(positionInfo));
        // 构造 Result
        Result<ListPositionInfoResponse> positionInfoResult = Result.success(listPositionInfoResponse);

        // mock positionProvider.listPositionInfo
        when(positionProvider.listPositionInfo(any())).thenReturn(positionInfoResult);

        String result = service.initInspectionByPositionCode(request);
        assertTrue(result.contains("未找到对应的规则配置") || result.contains("不存在"));
    }


    @Test
    public void testInitInspectionByPositionCode_with_exception() {
        InitInspectionByPositionCodeRequest request = new InitInspectionByPositionCodeRequest();
        request.setCountryId("CN");
        request.setPositionCode("P005");
        request.setActionType(0);
        request.setActionTime(System.currentTimeMillis());

        String result = service.initInspectionByPositionCode(request);
        assertTrue(result.contains("异常") || result.contains("error"));
    }

    @Test
    public void testGetImageCenterData_emptyList() {
        List<ImageCenterDto> result = service.getImageCenterData(Collections.emptyList());
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetImageCenterData_nullList() {
        List<ImageCenterDto> result = service.getImageCenterData(null);
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetImageCenterData_not_verification_passed() {
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setInspectionStatus(InspectionStatusEnum.NOT_COMPLETED);
        List<ImageCenterDto> result = service.getImageCenterData(Collections.singletonList(record));
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetImageCenterData_verification_passed_with_all_fields() {
        // 构造巡检记录
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setInspectionStatus(InspectionStatusEnum.VERIFICATION_PASSED);
        record.setPositionConstructionType(ConstructionType.CREATE);
        record.setCreatedOn(123456789L);
        record.setModifiedOn(987654321L);

        // 构造UploadData
        UploadData uploadData = new UploadData();

        // storeGate
        PhotoGroup storeGate = new PhotoGroup();
        storeGate.setImages(Collections.singletonList("url1"));
        storeGate.setGuid("123");
        uploadData.setStoreGate(storeGate);

        // positionLandingPhoto
        PhotoGroup positionLandingPhoto = new PhotoGroup();
        positionLandingPhoto.setImages(Collections.singletonList("url2"));
        storeGate.setGuid("123");
        uploadData.setPositionLandingPhoto(positionLandingPhoto);

        // positionDisplay
        PhotoGroup positionDisplay = new PhotoGroup();
        positionDisplay.setImages(Collections.singletonList("url3"));
        storeGate.setGuid("123");
        uploadData.setPositionDisplay(positionDisplay);

        // furniturePictures
        FurniturePhotoGroup furniture1 = new FurniturePhotoGroup();
        furniture1.setImages(Collections.singletonList("furl1"));
        furniture1.setGuid("123");
        FurniturePhotoGroup furniture2 = new FurniturePhotoGroup();
        furniture2.setImages(Collections.singletonList("furl2"));
        furniture2.setGuid("123");
        furniture2.setReason(5);
        furniture2.setRemark("test");
        uploadData.setFurniturePictures(Arrays.asList(furniture1, furniture2));

        // 序列化为JSON
        String uploadDataJson = "{\"storeGate\":{\"images\":[\"url1\"],\"guid\":123}," +
                "\"positionLandingPhoto\":{\"images\":[\"url2\"],\"guid\":123}," +
                "\"positionDisplay\":{\"images\":[\"url3\"],\"guid\":123}," +
                "\"furniturePictures\":[{\"images\":[\"furl1\"],\"guid\":123}," +
                "{\"images\":[\"furl2\"],\"guid\":123,\"reason\":5,\"remark\":\"test\"}]}";
        record.setUploadData(uploadDataJson);

        List<ImageCenterDto> result = service.getImageCenterData(Collections.singletonList(record));
        assertNotNull(result);
        assertEquals(1, result.size());
        ImageCenterDto dto = result.get(0);
        assertEquals(ConstructionType.CREATE.getStatus(), dto.getPositionConstructionType());
        assertEquals(123456789L, dto.getCreatedOn());
        assertEquals(987654321L, dto.getModifiedOn());
        assertEquals(Collections.singletonList("url1"), dto.getStoreGate());
        assertEquals(Collections.singletonList("url2"), dto.getPositionLandingPhoto());
        assertEquals(Collections.singletonList("url3"), dto.getPositionDisplay());
        assertTrue(dto.getFurniturePictures().contains("furl1"));
        assertTrue(dto.getFurniturePictures().contains("furl2"));
    }

    @Test
    public void testGetImageCenterData_verification_passed_uploadData_null() {
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setInspectionStatus(InspectionStatusEnum.VERIFICATION_PASSED);
        record.setUploadData(null);

        List<ImageCenterDto> result = service.getImageCenterData(Collections.singletonList(record));
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetImageCenterData_verification_passed_uploadData_empty() {
        InspectionRecordDomain record = new InspectionRecordDomain();
        record.setInspectionStatus(InspectionStatusEnum.VERIFICATION_PASSED);
        record.setUploadData("   ");

        List<ImageCenterDto> result = service.getImageCenterData(Collections.singletonList(record));
        assertNotNull(result);
        assertEquals(0, result.size());
    }

    @Test
    public void testCreateInspectionByPositionCode_success() {
        String areaId = "CN";
        String positionCode = "P001";
        String operatorId = "op1";

        // mock positionProvider.listPositionInfo
        PositionInfo positionInfo = new PositionInfo();
        positionInfo.setPositionCode(positionCode);
        positionInfo.setConstructionPhase(7); // 假设1能被PositionStageEnum.getByCode识别
        ListPositionInfoResponse listPositionInfoResponse = new ListPositionInfoResponse();
        listPositionInfoResponse.setList(Collections.singletonList(positionInfo));
        Result<ListPositionInfoResponse> positionInfoResult = Result.success(listPositionInfoResponse);
        when(positionProvider.listPositionInfo(any())).thenReturn(positionInfoResult);

        // mock ruleConfigRepository.findValidRuleConfigsByCountry
        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfig.validatePositionStage(any())).thenReturn(true);
        when(ruleConfig.getRuleCode()).thenReturn("RULE1");
        when(ruleConfig.getStartTime()).thenReturn(System.currentTimeMillis());
        when(ruleConfig.getReminderDays()).thenReturn(1);
        when(ruleConfig.getEndTime()).thenReturn(System.currentTimeMillis() + 10000);
//        when(ruleConfigRepository.findValidRuleConfigsByCountry(areaId)).thenReturn(Collections.singletonList(ruleConfig));

        // mock buildChannelPositionProvider.getLatestByPositionCode
        ChannelBaseV1Resp channelBaseV1Resp = new ChannelBaseV1Resp();
        channelBaseV1Resp.setReportCode("RCODE1");
        Result<ChannelBaseV1Resp> channelResult = Result.success(channelBaseV1Resp);
        when(buildChannelPositionProvider.getLatestByPositionCode(any())).thenReturn(channelResult);

        // mock inspectionRecordRepository.getByBusinessCodeAndConstructionActionCode
        when(inspectionRecordRepository.getByBusinessCodeAndConstructionActionCode(any(), any())).thenReturn(null);


        // 构造返回的负责人信息
        Map<String, Object> person = new HashMap<>();
        person.put("user_id", "user1");
        person.put("user_name", "负责人");
        person.put("user_title", "Promoter");
        person.put("user_title_code", 1);
        person.put("mi_id", 123L);
        person.put("language_code", "zh");
        person.put("psition_code", positionCode);
        person.put("created_on", System.currentTimeMillis());
        List<Map<String, Object>> results = Collections.singletonList(person);
// mock positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson
        when(positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson(positionCode)).thenReturn(results);


        // mock getResponsiblePersonByPositionCode
        PositionInspectionResponsiblePersonDTO responsiblePerson = new PositionInspectionResponsiblePersonDTO();
        responsiblePerson.setUserName("负责人");
        responsiblePerson.setMiId(123L);
        responsiblePerson.setUserId("user1");

        // mock inspectionRecordRepository.save
        when(inspectionRecordRepository.save(any())).thenReturn(true);

        // mock taskCenterServiceRpc.pushPositionInspectionTask
        when(taskCenterServiceRpc.pushPositionInspectionTask(any(), any())).thenReturn(123L);

        // mock ruleConfigRepository.update
        when(ruleConfigRepository.update(any())).thenReturn(true);

        String result = service.createInspectionByPositionCode(areaId, positionCode, operatorId);
        assertNotNull(result);
        assertTrue(result.contains("成功") || result.contains("创建"));
    }

    @Test
    public void testCreateInspectionByPositionCode_positionInfo_not_found() {
        String areaId = "CN";
        String positionCode = "P002";
        String operatorId = "op2";

        // mock positionProvider.listPositionInfo返回空
        Result<ListPositionInfoResponse> positionInfoResult = Result.success(null);
        when(positionProvider.listPositionInfo(any())).thenReturn(positionInfoResult);

        String result = service.createInspectionByPositionCode(areaId, positionCode, operatorId);
        assertTrue(result.contains("阵地信息不存在"));
    }

    @Test
    public void testCreateInspectionByPositionCode_ruleConfig_not_found() {
        String areaId = "CN";
        String positionCode = "P003";
        String operatorId = "op3";

        // mock positionProvider.listPositionInfo
        PositionInfo positionInfo = new PositionInfo();
        positionInfo.setPositionCode(positionCode);
        positionInfo.setConstructionPhase(1);
        ListPositionInfoResponse listPositionInfoResponse = new ListPositionInfoResponse();
        listPositionInfoResponse.setList(Collections.singletonList(positionInfo));
        Result<ListPositionInfoResponse> positionInfoResult = Result.success(listPositionInfoResponse);
        when(positionProvider.listPositionInfo(any())).thenReturn(positionInfoResult);

        // mock ruleConfigRepository.findValidRuleConfigsByCountry返回空
//        when(ruleConfigRepository.findValidRuleConfigsByCountry(areaId)).thenReturn(Collections.emptyList());

        String result = service.createInspectionByPositionCode(areaId, positionCode, operatorId);
        assertTrue(result.contains("规则配置") || result.contains("未找到"));
    }

    @Test
    public void testCreateInspectionByPositionCode_stage_not_found() {
        String areaId = "CN";
        String positionCode = "P004";
        String operatorId = "op4";

        // mock positionProvider.listPositionInfo
        PositionInfo positionInfo = new PositionInfo();
        positionInfo.setPositionCode(positionCode);
        positionInfo.setConstructionPhase(999); // 不存在的阶段
        ListPositionInfoResponse listPositionInfoResponse = new ListPositionInfoResponse();
        listPositionInfoResponse.setList(Collections.singletonList(positionInfo));
        Result<ListPositionInfoResponse> positionInfoResult = Result.success(listPositionInfoResponse);
        when(positionProvider.listPositionInfo(any())).thenReturn(positionInfoResult);

        // mock ruleConfigRepository.findValidRuleConfigsByCountry
        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
//        when(ruleConfigRepository.findValidRuleConfigsByCountry(areaId)).thenReturn(Collections.singletonList(ruleConfig));

        String result = service.createInspectionByPositionCode(areaId, positionCode, operatorId);
        assertTrue(result.contains("阶段不存在"));
    }

    @Test
    public void testCreateInspectionByPositionCode_existsInspectionRecord() {
        String areaId = "CN";
        String positionCode = "P005";
        String operatorId = "op5";

        // mock positionProvider.listPositionInfo
        PositionInfo positionInfo = new PositionInfo();
        positionInfo.setPositionCode(positionCode);
        positionInfo.setConstructionPhase(7);
        ListPositionInfoResponse listPositionInfoResponse = new ListPositionInfoResponse();
        listPositionInfoResponse.setList(Collections.singletonList(positionInfo));
        Result<ListPositionInfoResponse> positionInfoResult = Result.success(listPositionInfoResponse);
        when(positionProvider.listPositionInfo(any())).thenReturn(positionInfoResult);

        // mock ruleConfigRepository.findValidRuleConfigsByCountry
        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfig.validatePositionStage(any())).thenReturn(true);
//        when(ruleConfigRepository.findValidRuleConfigsByCountry(areaId)).thenReturn(Collections.singletonList(ruleConfig));

        // mock buildChannelPositionProvider.getLatestByPositionCode
        ChannelBaseV1Resp channelBaseV1Resp = new ChannelBaseV1Resp();
        channelBaseV1Resp.setReportCode("RCODE1");
        Result<ChannelBaseV1Resp> channelResult = Result.success(channelBaseV1Resp);
        when(buildChannelPositionProvider.getLatestByPositionCode(any())).thenReturn(channelResult);

        // mock inspectionRecordRepository.getByBusinessCodeAndConstructionActionCode返回非空
        InspectionRecordDomain existsRecord = new InspectionRecordDomain();
        when(inspectionRecordRepository.getByBusinessCodeAndConstructionActionCode(any(), any())).thenReturn(existsRecord);

        try {
            service.createInspectionByPositionCode(areaId, positionCode, operatorId);
            assertTrue(true, "应抛出异常");
        } catch (Exception e) {
            assertTrue(e.getMessage().contains("已经存在巡检记录"));
        }
    }

    @Test
    public void testCreateInspectionByPositionCode_save_fail() {
        String areaId = "CN";
        String positionCode = "P006";
        String operatorId = "op6";

        // mock positionProvider.listPositionInfo
        PositionInfo positionInfo = new PositionInfo();
        positionInfo.setPositionCode(positionCode);
        positionInfo.setConstructionPhase(7);
        ListPositionInfoResponse listPositionInfoResponse = new ListPositionInfoResponse();
        listPositionInfoResponse.setList(Collections.singletonList(positionInfo));
        Result<ListPositionInfoResponse> positionInfoResult = Result.success(listPositionInfoResponse);
        when(positionProvider.listPositionInfo(any())).thenReturn(positionInfoResult);

        // mock ruleConfigRepository.findValidRuleConfigsByCountry
        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfig.validatePositionStage(any())).thenReturn(true);
//        when(ruleConfigRepository.findValidRuleConfigsByCountry(areaId)).thenReturn(Collections.singletonList(ruleConfig));

        // mock buildChannelPositionProvider.getLatestByPositionCode
        ChannelBaseV1Resp channelBaseV1Resp = new ChannelBaseV1Resp();
        channelBaseV1Resp.setReportCode("RCODE1");
        Result<ChannelBaseV1Resp> channelResult = Result.success(channelBaseV1Resp);
        when(buildChannelPositionProvider.getLatestByPositionCode(any())).thenReturn(channelResult);

        // mock inspectionRecordRepository.getByBusinessCodeAndConstructionActionCode
        when(inspectionRecordRepository.getByBusinessCodeAndConstructionActionCode(any(), any())).thenReturn(null);

        // mock getResponsiblePersonByPositionCode
        PositionInspectionResponsiblePersonDTO responsiblePerson = new PositionInspectionResponsiblePersonDTO();
        responsiblePerson.setUserName("负责人");
        responsiblePerson.setMiId(123L);

        // mock inspectionRecordRepository.save返回false
        when(inspectionRecordRepository.save(any())).thenReturn(false);

        String result = service.createInspectionByPositionCode(areaId, positionCode, operatorId);
        assertTrue(result.contains("保存阵地巡检记录失败") || result.contains("失败"));
    }

    @Test
    public void testCreateInspectionByPositionCode_with_exception() {
        String areaId = "CN";
        String positionCode = "P007";
        String operatorId = "op7";

        // mock positionProvider.listPositionInfo抛异常
        when(positionProvider.listPositionInfo(any())).thenThrow(new RuntimeException("DB error"));

        String result = service.createInspectionByPositionCode(areaId, positionCode, operatorId);
        assertTrue(result.contains("异常") || result.contains("error"));
    }

    @Test
    public void testGetPositionInspectionResponsiblePerson_success() {
        // 构造参数
        String positionCode = "P001";

        // 构造返回的负责人信息
        Map<String, Object> person = new HashMap<>();
        person.put("user_id", "user1");
        person.put("user_name", "负责人");
        person.put("user_title", "Promoter");
        person.put("user_title_code", 1);
        person.put("mi_id", 123L);
        person.put("language_code", "zh");
        person.put("psition_code", positionCode);
        person.put("created_on", new Date());
        List<Map<String, Object>> results = Collections.singletonList(person);

        // mock positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson
        when(positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson(positionCode)).thenReturn(results);

        // 执行测试
        PositionInspectionResponsiblePersonDTO result = service.getPositionInspectionResponsiblePerson(positionCode);

        // 验证结果
        assertNotNull(result);
        assertEquals("user1", result.getUserId());
        assertEquals("负责人", result.getUserName());
        assertEquals(123L, result.getMiId());
        assertEquals("Promoter", result.getUserTitle());
        assertEquals(1, result.getUserTitleCode());
        assertEquals("zh", result.getLanguageCode());
        assertEquals(positionCode, result.getPositionCode());

        // 验证方法调用
        verify(positionResponsiblePersonMapper).getPositionInspectionResponsiblePerson(positionCode);
    }

    @Test
    public void testGetPositionInspectionResponsiblePerson_empty_positionCode() {
        // 测试空字符串
        PositionInspectionResponsiblePersonDTO result1 = service.getPositionInspectionResponsiblePerson("");
        assertNull(result1);

        // 测试null
        PositionInspectionResponsiblePersonDTO result2 = service.getPositionInspectionResponsiblePerson(null);
        assertNull(result2);

        // 测试空白字符串
        PositionInspectionResponsiblePersonDTO result3 = service.getPositionInspectionResponsiblePerson("   ");
        assertNull(result3);
    }

    @Test
    public void testGetPositionInspectionResponsiblePerson_no_responsible_person() {
        // 构造参数
        String positionCode = "P002";

        // mock positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson返回空
        when(positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson(positionCode)).thenReturn(Collections.emptyList());

        // 执行测试
        PositionInspectionResponsiblePersonDTO result = service.getPositionInspectionResponsiblePerson(positionCode);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(positionResponsiblePersonMapper).getPositionInspectionResponsiblePerson(positionCode);
    }

    @Test
    public void testGetPositionInspectionResponsiblePerson_null_result() {
        // 构造参数
        String positionCode = "P003";

        // mock positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson返回null
        when(positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson(positionCode)).thenReturn(null);

        // 执行测试
        PositionInspectionResponsiblePersonDTO result = service.getPositionInspectionResponsiblePerson(positionCode);

        // 验证结果
        assertNull(result);

        // 验证方法调用
        verify(positionResponsiblePersonMapper).getPositionInspectionResponsiblePerson(positionCode);
    }

    @Test
    public void testGetPositionInspectionResponsiblePerson_multiple_persons() {
        // 构造参数
        String positionCode = "P004";

        // 构造返回的多个负责人信息
        Map<String, Object> person1 = new HashMap<>();
        person1.put("user_id", "user1");
        person1.put("user_name", "负责人1");
        person1.put("user_title", "Promoter");
        person1.put("user_title_code", 1);
        person1.put("mi_id", 123L);
        person1.put("language_code", "zh");
        person1.put("psition_code", positionCode);
        person1.put("created_on", System.currentTimeMillis());

        Map<String, Object> person2 = new HashMap<>();
        person2.put("user_id", "user2");
        person2.put("user_name", "负责人2");
        person2.put("user_title", "Manager");
        person2.put("user_title_code", 2);
        person2.put("mi_id", 456L);
        person2.put("language_code", "en");
        person2.put("psition_code", positionCode);
        person2.put("created_on", System.currentTimeMillis() + 1000);

        List<Map<String, Object>> results = Arrays.asList(person1, person2);

        // mock positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson
        when(positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson(positionCode)).thenReturn(results);

        // 执行测试
        PositionInspectionResponsiblePersonDTO result = service.getPositionInspectionResponsiblePerson(positionCode);

        // 验证结果 - 应该返回第一个负责人
        assertNotNull(result);
        assertEquals("user1", result.getUserId());
        assertEquals("负责人1", result.getUserName());
        assertEquals(123L, result.getMiId());
        assertEquals("Promoter", result.getUserTitle());
        assertEquals(1, result.getUserTitleCode());
        assertEquals("zh", result.getLanguageCode());
        assertEquals(positionCode, result.getPositionCode());

        // 验证方法调用
        verify(positionResponsiblePersonMapper).getPositionInspectionResponsiblePerson(positionCode);
    }

    @Test
    public void testGetPositionInspectionResponsiblePerson_with_missing_fields() {
        // 构造参数
        String positionCode = "P005";

        // 构造返回的负责人信息 - 缺少某些字段
        Map<String, Object> person = new HashMap<>();
        person.put("user_id", "user1");
        person.put("user_name", "负责人");
        // 缺少其他字段
        List<Map<String, Object>> results = Collections.singletonList(person);

        // mock positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson
        when(positionResponsiblePersonMapper.getPositionInspectionResponsiblePerson(positionCode)).thenReturn(results);

        // 执行测试
        PositionInspectionResponsiblePersonDTO result = service.getPositionInspectionResponsiblePerson(positionCode);

        // 验证结果
        assertNull(result);


        // 验证方法调用
        verify(positionResponsiblePersonMapper).getPositionInspectionResponsiblePerson(positionCode);
    }

    @Test
    public void testNoNeedCompleteTask_param_invalid() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(null);
        String result = service.noNeedCompleteTask(req);
        assertEquals("请求参数不完整", result);

        result = service.noNeedCompleteTask(null);
        assertEquals("请求参数不完整", result);
    }

    @Test
    public void testNoNeedCompleteTask_ruleConfig_not_found() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(123L);
        when(ruleConfigRepository.getByTaskBatchId(123L)).thenReturn(null);

        String result = service.noNeedCompleteTask(req);
        assertEquals("非阵地巡检任务，无需处理", result);
    }

    @Test
    public void testNoNeedCompleteTask_positionDomain_not_found() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(123L);
        req.setOrgId("POS001");
        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfigRepository.getByTaskBatchId(123L)).thenReturn(ruleConfig);
        when(positionRepository.getByCode("POS001")).thenReturn(null);

        String result = service.noNeedCompleteTask(req);
        assertEquals("阵地信息不存在", result);
    }

    @Test
    public void testNoNeedCompleteTask_inspectionRecord_not_found() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(123L);
        req.setOrgId("POS001");
        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfigRepository.getByTaskBatchId(123L)).thenReturn(ruleConfig);

        PositionDomain positionDomain = mock(PositionDomain.class);
        when(positionRepository.getByCode("POS001")).thenReturn(positionDomain);
        when(positionDomain.getCrpsCode()).thenReturn("CRPS001");

        String result = service.noNeedCompleteTask(req);
        assertEquals("找不到巡检记录，无需处理", result);
    }

    @Test
    public void testNoNeedCompleteTask_update_fail() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(123L);
        req.setOrgId("POS001");
        req.setMiId(456L);

        RuleConfigDomain ruleConfig = new RuleConfigDomain();
        ruleConfig.setRuleCode("1234");
        when(ruleConfigRepository.getByTaskBatchId(123L)).thenReturn(ruleConfig);

        PositionDomain positionDomain = new PositionDomain();
        when(positionRepository.getByCode("POS001")).thenReturn(positionDomain);
        InspectionRecordDomain inspectionRecord = mock(InspectionRecordDomain.class);
        when(inspectionRecordRepository.getByRuleCodeAndBusinessCode(any(), any())).thenReturn(inspectionRecord);


        // mock update返回false
        when(inspectionRecordRepository.update(any())).thenReturn(false);

        // mock userService
        IntlRmsUserDto userDto = mock(IntlRmsUserDto.class);
        when(userDto.getDomainName()).thenReturn("testUser");
        when(intlRmsUserService.getIntlRmsUserByMiId(456L)).thenReturn(Optional.of(userDto));

        RuntimeException ex = assertThrows(RuntimeException.class, () -> service.noNeedCompleteTask(req));
        assertTrue(ex.getMessage().contains("更新巡检记录状态失败"));
    }

    @Test
    public void testNoNeedCompleteTask_history_save_fail() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(123L);
        req.setOrgId("POS001");
        req.setMiId(456L);

        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfigRepository.getByTaskBatchId(123L)).thenReturn(ruleConfig);

        PositionDomain positionDomain = mock(PositionDomain.class);
        when(positionRepository.getByCode("POS001")).thenReturn(positionDomain);
        when(positionDomain.getCrpsCode()).thenReturn("CRPS001");


        // mock update返回true
        when(inspectionRecordRepository.update(any())).thenReturn(true);

        // mock userService
        IntlRmsUserDto userDto = mock(IntlRmsUserDto.class);
        when(userDto.getDomainName()).thenReturn("testUser");
        when(intlRmsUserService.getIntlRmsUserByMiId(456L)).thenReturn(Optional.of(userDto));

        InspectionRecordDomain inspectionRecord = mock(InspectionRecordDomain.class);
        when(inspectionRecordRepository.getByRuleCodeAndBusinessCode(any(), any())).thenReturn(inspectionRecord);


        // mock history保存失败
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(false);

        RuntimeException ex = assertThrows(RuntimeException.class, () -> service.noNeedCompleteTask(req));
        assertTrue(ex.getMessage().contains("保存巡检历史记录失败"));
    }

    @Test
    public void testNoNeedCompleteTask_success_with_user() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(123L);
        req.setOrgId("POS001");
        req.setMiId(456L);

        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfigRepository.getByTaskBatchId(123L)).thenReturn(ruleConfig);

        PositionDomain positionDomain = mock(PositionDomain.class);
        when(positionRepository.getByCode("POS001")).thenReturn(positionDomain);
        when(positionDomain.getCrpsCode()).thenReturn("CRPS001");

        InspectionRecordDomain inspectionRecord = mock(InspectionRecordDomain.class);
        when(inspectionRecordRepository.getByRuleCodeAndBusinessCode(any(), any())).thenReturn(inspectionRecord);

        // mock update返回true
        when(inspectionRecordRepository.update(any())).thenReturn(true);

        // mock userService
        IntlRmsUserDto userDto = mock(IntlRmsUserDto.class);
        when(userDto.getDomainName()).thenReturn("testUser");
        when(intlRmsUserService.getIntlRmsUserByMiId(456L)).thenReturn(Optional.of(userDto));

        // mock history保存成功
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(true);

        String result = service.noNeedCompleteTask(req);
        assertEquals("成功标记任务为无需完成", result);
    }

    @Test
    public void testNoNeedCompleteTask_success_without_user() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(123L);
        req.setOrgId("POS001");
        req.setMiId(456L);

        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfigRepository.getByTaskBatchId(123L)).thenReturn(ruleConfig);

        PositionDomain positionDomain = mock(PositionDomain.class);
        when(positionRepository.getByCode("POS001")).thenReturn(positionDomain);
        when(positionDomain.getCrpsCode()).thenReturn("CRPS001");

        InspectionRecordDomain inspectionRecord = mock(InspectionRecordDomain.class);
        when(inspectionRecordRepository.getByRuleCodeAndBusinessCode(any(), any())).thenReturn(inspectionRecord);

        // mock update返回true
        when(inspectionRecordRepository.update(any())).thenReturn(true);

        // mock userService返回空
        when(intlRmsUserService.getIntlRmsUserByMiId(456L)).thenReturn(Optional.empty());

        // mock history保存成功
        when(inspectionHistoryRepository.save(any(InspectionHistoryDomain.class))).thenReturn(true);

        String result = service.noNeedCompleteTask(req);
        assertEquals("成功标记任务为无需完成", result);
    }

    @Test
    public void testNoNeedCompleteTask_with_exception() {
        TaskCenterFinishTaskReq req = new TaskCenterFinishTaskReq();
        req.setTaskBatchId(123L);
        req.setOrgId("POS001");
        req.setMiId(456L);

        RuleConfigDomain ruleConfig = mock(RuleConfigDomain.class);
        when(ruleConfigRepository.getByTaskBatchId(123L)).thenReturn(ruleConfig);

        PositionDomain positionDomain = mock(PositionDomain.class);
        when(positionRepository.getByCode("POS001")).thenReturn(positionDomain);
        when(positionDomain.getCrpsCode()).thenReturn("CRPS001");

        InspectionRecordDomain inspectionRecord = mock(InspectionRecordDomain.class);
        when(inspectionRecordRepository.getByRuleCodeAndBusinessCode(any(), any())).thenReturn(inspectionRecord);

        // mock update抛异常
        when(inspectionRecordRepository.update(any())).thenThrow(new RuntimeException("db error"));

        // mock userService
        IntlRmsUserDto userDto = mock(IntlRmsUserDto.class);
        when(userDto.getDomainName()).thenReturn("testUser");
        when(intlRmsUserService.getIntlRmsUserByMiId(456L)).thenReturn(Optional.of(userDto));

        RuntimeException ex = assertThrows(RuntimeException.class, () -> service.noNeedCompleteTask(req));
        assertTrue(ex.getMessage().contains("db error"));
    }

    @Test
    public void outerTaskFinish_success() {
        TaskCenterFinishTaskReq taskCenterFinishTaskReq = mock(TaskCenterFinishTaskReq.class);
        String result = service.outerTaskFinish(taskCenterFinishTaskReq);
        assertEquals("成功完成用户当前任务动作",result);
    }
} 