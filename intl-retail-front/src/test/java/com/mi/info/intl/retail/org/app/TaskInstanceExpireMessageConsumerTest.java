package com.mi.info.intl.retail.org.app;

import cn.hutool.json.JSONUtil;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.TaskInstanceExpireMessageBody;
import com.mi.info.intl.retail.org.domain.repository.NewProductInspectionRepository;
import com.mi.info.intl.retail.org.infra.config.TaskInstanceMqBusinessTypeConfig;
import org.apache.rocketmq.common.message.MessageExt;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import java.nio.charset.StandardCharsets;
import java.util.Collections;

@ExtendWith(MockitoExtension.class)
class TaskInstanceExpireMessageConsumerTest {
    @Mock
    private TaskInstanceMqBusinessTypeConfig taskInstanceMqBusinessTypeConfig;
    @Mock
    private NewProductInspectionRepository newProductInspectionRepository;
    @InjectMocks
    private TaskInstanceExpireMessageConsumer consumer;

    @BeforeEach
    void setUp() {
        // no-op
    }

    @Test
    void onMessage_shouldSkipWhenTagInvalid() {
        MessageExt message = new MessageExt();
        message.setTags("invalid");
        message.setBody("{}".getBytes(StandardCharsets.UTF_8));
        consumer.onMessage(message);
        // 验证未调用 handleTaskInstanceExpired
        verify(newProductInspectionRepository, never()).updateInspectionRecordToExpired(anyList());
    }

    @Test
    void onMessage_shouldSkipWhenBodyBlank() {
        MessageExt message = new MessageExt();
        message.setTags("123");
        message.setBody("".getBytes(StandardCharsets.UTF_8));
        when(taskInstanceMqBusinessTypeConfig.shouldProcessMqMessage(anyLong())).thenReturn(true);
        consumer.onMessage(message);
        verify(newProductInspectionRepository, never()).updateInspectionRecordToExpired(anyList());
    }

    @Test
    void onMessage_shouldSkipWhenBodyParseFail() {
        MessageExt message = new MessageExt();
        message.setTags("123");
        message.setBody("not a json".getBytes(StandardCharsets.UTF_8));
        when(taskInstanceMqBusinessTypeConfig.shouldProcessMqMessage(anyLong())).thenReturn(true);
        consumer.onMessage(message);
        verify(newProductInspectionRepository, never()).updateInspectionRecordToExpired(anyList());
    }

    @Test
    void onMessage_shouldProcessWhenValid() {
        MessageExt message = new MessageExt();
        message.setTags("123");
        TaskInstanceExpireMessageBody.TaskInstanceExpireData data = new TaskInstanceExpireMessageBody.TaskInstanceExpireData();
        data.setId(1L);
        data.setMid(2L);
        data.setOrgId("ORG1");
        TaskInstanceExpireMessageBody body = new TaskInstanceExpireMessageBody();
        body.setData(Collections.singletonList(data));
        String json = JSONUtil.toJsonStr(body);
        message.setBody(json.getBytes(StandardCharsets.UTF_8));
        when(taskInstanceMqBusinessTypeConfig.shouldProcessMqMessage(anyLong())).thenReturn(true);
        consumer.onMessage(message);
        verify(newProductInspectionRepository, times(1)).updateInspectionRecordToExpired(anyList());
    }

    @Test
    void isValidBusinessTypeTag_shouldReturnFalseForBlank() {
        assertFalse(consumerTestProxy().isValidBusinessTypeTag(""));
    }

    @Test
    void isValidBusinessTypeTag_shouldReturnFalseForNonNumber() {
        assertFalse(consumerTestProxy().isValidBusinessTypeTag("abc"));
    }

    @Test
    void isValidBusinessTypeTag_shouldReturnTrueForValid() {
        when(taskInstanceMqBusinessTypeConfig.shouldProcessMqMessage(123L)).thenReturn(true);
        assertTrue(consumerTestProxy().isValidBusinessTypeTag("123"));
    }

    @Test
    void handleTaskInstanceExpired_shouldCallRepository() {
        TaskInstanceExpireMessageBody.TaskInstanceExpireData data = new TaskInstanceExpireMessageBody.TaskInstanceExpireData();
        data.setId(1L);
        data.setMid(2L);
        data.setOrgId("ORG1");
        TaskInstanceExpireMessageBody body = new TaskInstanceExpireMessageBody();
        body.setData(Collections.singletonList(data));
        consumer.handleTaskInstanceExpired(body, "123");
        verify(newProductInspectionRepository, times(1)).updateInspectionRecordToExpired(anyList());
    }

    // 反射代理调用 private 方法
    private TaskInstanceExpireMessageConsumer consumerTestProxy() {
        return Mockito.spy(consumer);
    }
} 