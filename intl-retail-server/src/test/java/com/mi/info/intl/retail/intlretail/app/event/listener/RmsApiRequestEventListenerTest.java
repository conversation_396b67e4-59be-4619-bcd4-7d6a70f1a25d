package com.mi.info.intl.retail.intlretail.app.event.listener;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.mi.info.intl.retail.cooperation.task.config.InspectionConfig;
import com.mi.info.intl.retail.intlretail.app.event.RmsApiRequestEvent;
import com.mi.info.intl.retail.intlretail.infra.mq.RocketMQProducer;
import com.mi.info.intl.retail.intlretail.service.api.mq.dto.RmsAipRequestInfo;
import com.mi.info.intl.retail.intlretail.service.api.proxy.RetailerAppConfigService;
import com.mi.info.intl.retail.intlretail.service.api.proxy.dto.RmsUserBaseDataResponse;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsCountryTimezone;
import com.mi.info.intl.retail.org.infra.entity.IntlRmsPosition;
import com.mi.info.intl.retail.org.infra.mapper.IntlRmsCountryTimezoneMapper;
import com.mi.info.intl.retail.org.infra.mapper.read.IntlRmsPositionReadMapper;
import com.mi.info.intl.retail.user.app.UserService;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import static org.junit.jupiter.api.Assertions.assertEquals;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.oauth2.jwt.Jwt;
import org.springframework.security.oauth2.server.resource.authentication.JwtAuthenticationToken;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.Collections;
import java.util.HashSet;

@ExtendWith(MockitoExtension.class)
class RmsApiRequestEventListenerTest {

    @Mock
    private InspectionConfig inspectionConfig;

    @Mock
    private RocketMQProducer rocketMQProducer;

    @Mock
    private ObjectMapper objectMapper;

    @Mock
    private UserService userService;

    @Mock
    private IntlRmsPositionReadMapper intlRmsPositionReadMapper;

    @Mock
    private IntlRmsCountryTimezoneMapper intlRmsCountryTimezoneMapper;

    @Mock
    private RetailerAppConfigService retailerAppConfigService;

    @Mock
    private Jwt oAuth2AccessToken;

    @InjectMocks
    private RmsApiRequestEventListener rmsApiRequestEventListener;

    private RmsApiRequestEvent event;
    private RmsAipRequestInfo rmsAipRequestInfo;

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(rmsApiRequestEventListener, "topic", "test-topic");
        // 初始化测试数据
        rmsAipRequestInfo = new RmsAipRequestInfo();
        rmsAipRequestInfo.setPath("new_SubmitData");
        rmsAipRequestInfo.setType("StoreCheck");
        rmsAipRequestInfo.setAccount("<EMAIL>");
        rmsAipRequestInfo.setPositionId("test-position-id");
        JwtAuthenticationToken jwtAuthenticationToken = new JwtAuthenticationToken(oAuth2AccessToken);
        event = new RmsApiRequestEvent(this, rmsAipRequestInfo, jwtAuthenticationToken);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_InvalidPath_ShouldReturnEarly() {
        // Given
        rmsAipRequestInfo.setPath("invalid-path");

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verifyNoInteractions(userService, intlRmsPositionReadMapper, intlRmsCountryTimezoneMapper,
                retailerAppConfigService, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_NullType_ShouldReturnEarly() {
        // Given
        rmsAipRequestInfo.setType(null);

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verifyNoInteractions(userService, intlRmsPositionReadMapper, intlRmsCountryTimezoneMapper,
                retailerAppConfigService, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_InvalidActionType_ShouldReturnEarly() {
        // Given
        rmsAipRequestInfo.setType("invalid-action-type");

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verifyNoInteractions(userService, intlRmsPositionReadMapper, intlRmsCountryTimezoneMapper,
                retailerAppConfigService, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_PositionNotFound_ShouldReturnEarly() {
        // Given
        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(null);

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verifyNoInteractions(intlRmsCountryTimezoneMapper, userService, retailerAppConfigService, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_CountryTimezoneNotFound_ShouldReturnEarly() {
        // Given
        IntlRmsPosition position = new IntlRmsPosition();
        position.setCountryId("test-country-id");
        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(position);
        when(intlRmsCountryTimezoneMapper.selectOne(any())).thenReturn(null);

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verify(intlRmsCountryTimezoneMapper).selectOne(any());
        verifyNoInteractions(userService, retailerAppConfigService, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_ValidFlow_ShouldSendMQ() throws JsonProcessingException {
        // Given
        IntlRmsPosition position = new IntlRmsPosition();
        position.setCountryId("test-country-id");
        position.setCode("test-position-code");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("SG");

        IntlRmsUser user = new IntlRmsUser();
        user.setId(1);
        user.setJobId(500900002);
        user.setMiId(12345L);
        user.setCountryName("Singapore");

        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(position);
        when(intlRmsCountryTimezoneMapper.selectOne(any())).thenReturn(countryTimezone);
        when(userService.getUserInfoDomainName(anyString())).thenReturn(user);
        when(objectMapper.writeValueAsString(any())).thenReturn("test-message");
        when(rocketMQProducer.sendMessageWithResult(anyString(), anyString())).thenReturn("test-msg-id");

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verify(intlRmsCountryTimezoneMapper).selectOne(any());
        verify(userService).getUserInfoDomainName("<EMAIL>");
        verify(objectMapper).writeValueAsString(any());
        verify(rocketMQProducer).sendMessageWithResult("test-topic", "test-message");
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_UserNotSupervisor_ShouldNotSendMQ() {
        // Given
        IntlRmsPosition position = new IntlRmsPosition();
        position.setCountryId("test-country-id");
        position.setCode("test-position-code");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("SG");

        IntlRmsUser user = new IntlRmsUser();
        user.setId(1);
        user.setJobId(999999); // 不在SUPERVISOR_JOB_IDS中
        user.setMiId(12345L);
        user.setCountryName("Singapore");

        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(position);
        when(intlRmsCountryTimezoneMapper.selectOne(any())).thenReturn(countryTimezone);
        when(userService.getUserInfoDomainName(anyString())).thenReturn(user);

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verify(intlRmsCountryTimezoneMapper).selectOne(any());
        verify(userService).getUserInfoDomainName("<EMAIL>");
        verifyNoInteractions(objectMapper, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_UserNullId_ShouldNotSendMQ() {
        // Given
        IntlRmsPosition position = new IntlRmsPosition();
        position.setCountryId("test-country-id");
        position.setCode("test-position-code");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("SG");

        IntlRmsUser user = new IntlRmsUser();
        user.setId(null); // 用户ID为空
        user.setJobId(500900002);
        user.setMiId(12345L);
        user.setCountryName("Singapore");

        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(position);
        when(intlRmsCountryTimezoneMapper.selectOne(any())).thenReturn(countryTimezone);
        when(userService.getUserInfoDomainName(anyString())).thenReturn(user);

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verify(intlRmsCountryTimezoneMapper).selectOne(any());
        verify(userService).getUserInfoDomainName("<EMAIL>");
        verifyNoInteractions(objectMapper, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_SalesUploadQtyWithIMEIMenu_ShouldReturnEarly() {
        // Given
        rmsAipRequestInfo.setType("SalesUpload_Qty");

        IntlRmsPosition position = new IntlRmsPosition();
        position.setCountryId("test-country-id");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("ID"); // 不在白名单中

        RmsUserBaseDataResponse userBaseInfo = new RmsUserBaseDataResponse();
        RmsUserBaseDataResponse.MenuInfo menuInfo = new RmsUserBaseDataResponse.MenuInfo();
        menuInfo.setScreenName("SalesUploadIMEI");
        userBaseInfo.setMenu(Collections.singletonList(menuInfo));
        when(oAuth2AccessToken.getTokenValue()).thenReturn("test-token-value");
        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(position);
        when(intlRmsCountryTimezoneMapper.selectOne(any())).thenReturn(countryTimezone);
        when(retailerAppConfigService.requestRmsGetUserMenuInfo(anyString(), anyString())).thenReturn(userBaseInfo);
        when(inspectionConfig.getCountryWhiteSalesUploadQtySet()).thenReturn(
                new HashSet<>(Collections.singletonList("SG")));

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verify(intlRmsCountryTimezoneMapper).selectOne(any());
        verify(retailerAppConfigService).requestRmsGetUserMenuInfo("<EMAIL>", "test-token-value");
        verifyNoInteractions(userService, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_SalesUploadQtyWithoutIMEIMenu_ShouldContinue()
            throws JsonProcessingException {
        // Given
        rmsAipRequestInfo.setType("SalesUpload_Qty");

        IntlRmsPosition position = new IntlRmsPosition();
        position.setCountryId("test-country-id");
        position.setCode("test-position-code");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("SA");

        RmsUserBaseDataResponse userBaseInfo = new RmsUserBaseDataResponse();
        RmsUserBaseDataResponse.MenuInfo menuInfo = new RmsUserBaseDataResponse.MenuInfo();
        menuInfo.setScreenName("SalesUploadIMEI");
        userBaseInfo.setMenu(Collections.singletonList(menuInfo));

        IntlRmsUser user = new IntlRmsUser();
        user.setId(1);
        user.setJobId(500900002);
        user.setMiId(12345L);
        user.setCountryName("Indonesia");

        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(position);
        when(intlRmsCountryTimezoneMapper.selectOne(any())).thenReturn(countryTimezone);
        when(retailerAppConfigService.requestRmsGetUserMenuInfo(anyString(), anyString())).thenReturn(userBaseInfo);
        when(inspectionConfig.getCountryWhiteSalesUploadQtySet()).thenReturn(
                new HashSet<>(Collections.singletonList("SA")));
        when(userService.getUserInfoDomainName(anyString())).thenReturn(user);
        when(objectMapper.writeValueAsString(any())).thenReturn("test-message");
        when(rocketMQProducer.sendMessageWithResult(anyString(), anyString())).thenReturn("test-msg-id");
        when(oAuth2AccessToken.getTokenValue()).thenReturn("test-token-value");

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verify(intlRmsCountryTimezoneMapper).selectOne(any());
        verify(retailerAppConfigService).requestRmsGetUserMenuInfo("<EMAIL>", "test-token-value");
        verify(userService).getUserInfoDomainName("<EMAIL>");
        verify(rocketMQProducer).sendMessageWithResult("test-topic", "test-message");
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_Exception_ShouldLogError() {
        // Given
        when(intlRmsPositionReadMapper.selectOne(any())).thenThrow(new RuntimeException("Database error"));

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verifyNoInteractions(intlRmsCountryTimezoneMapper, userService, retailerAppConfigService, rocketMQProducer);
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_NewCreationIMEIInfoPath_ShouldSetType() throws JsonProcessingException {
        // Given
        rmsAipRequestInfo.setPath("new_CreationIMEIInfo");
        rmsAipRequestInfo.setType(""); // 空类型

        IntlRmsPosition position = new IntlRmsPosition();
        position.setCountryId("test-country-id");
        position.setCode("test-position-code");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("SG");

        IntlRmsUser user = new IntlRmsUser();
        user.setId(1);
        user.setJobId(500900002);
        user.setMiId(12345L);
        user.setCountryName("Singapore");

        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(position);
        when(intlRmsCountryTimezoneMapper.selectOne(any())).thenReturn(countryTimezone);
        when(userService.getUserInfoDomainName(anyString())).thenReturn(user);
        when(objectMapper.writeValueAsString(any())).thenReturn("test-message");
        when(rocketMQProducer.sendMessageWithResult(anyString(), anyString())).thenReturn("test-msg-id");

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        // 验证类型被正确设置
        assertEquals("new_CreationIMEIInfo", event.getRmsAipRequestInfo().getType());
        verify(rocketMQProducer).sendMessageWithResult("test-topic", "test-message");
    }

    @Test
    void testHandleRmsTypeApiRequestEvent_NullToken_ShouldNotCheckIMEIMenu() throws JsonProcessingException {
        // Given
        event = new RmsApiRequestEvent(this, rmsAipRequestInfo, null); // token为null

        IntlRmsPosition position = new IntlRmsPosition();
        position.setCountryId("test-country-id");
        position.setCode("test-position-code");

        IntlRmsCountryTimezone countryTimezone = new IntlRmsCountryTimezone();
        countryTimezone.setCountryCode("SG");

        IntlRmsUser user = new IntlRmsUser();
        user.setId(1);
        user.setJobId(500900002);
        user.setMiId(12345L);
        user.setCountryName("Singapore");

        when(intlRmsPositionReadMapper.selectOne(any())).thenReturn(position);
        when(intlRmsCountryTimezoneMapper.selectOne(any())).thenReturn(countryTimezone);
        when(userService.getUserInfoDomainName(anyString())).thenReturn(user);
        when(objectMapper.writeValueAsString(any())).thenReturn("test-message");
        when(rocketMQProducer.sendMessageWithResult(anyString(), anyString())).thenReturn("test-msg-id");

        // When
        rmsApiRequestEventListener.handleRmsTypeApiRequestEvent(event);

        // Then
        verify(intlRmsPositionReadMapper).selectOne(any());
        verify(intlRmsCountryTimezoneMapper).selectOne(any());
        verify(userService).getUserInfoDomainName("<EMAIL>");
        verifyNoInteractions(retailerAppConfigService); // 不应该调用IMEI菜单检查
        verify(rocketMQProducer).sendMessageWithResult("test-topic", "test-message");
    }
} 