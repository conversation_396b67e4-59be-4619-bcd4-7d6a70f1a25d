package com.mi.info.intl.retail.intlretail.app.controller.position;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.stream.JsonReader;
import com.mi.info.intl.retail.intlretail.app.controller.BaseController;
import com.mi.info.intl.retail.intlretail.service.api.position.PositionInspectionService;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.OptionalItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionFurnitureRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionHistoryItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionHistoryRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.dto.PositionInspectionSubmitRequest;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 阵地巡检控制器
 */
@Slf4j
@RestController
@RequestMapping({"/api/position/inspection", "/*/api/position/inspection"})
@ApiModule(value = "阵地巡检 Controller", apiInterface = PositionInspectionController.class)
public class PositionInspectionController extends BaseController {

    @Resource
    private PositionInspectionService positionInspectionService;

    public static final String FILE_NAME = "inspectionexample.json";

    /**
     * 查询阵地巡检列表
     *
     * @param request 请求参数
     * @return 阵地巡检列表响应
     */
    @PostMapping("/list")
    @ResponseBody
    @ApiDoc(name = "查询阵地巡检列表", value = "/api/position/inspection/list", description = "查询阵地巡检列表")
    public CommonApiResponse<PageResponse<PositionInspectionItem>> listPositionInspection(
            @RequestBody PositionInspectionRequest request) {
        String account = this.getAccount();
        request.setOwner(account);
        request.setInspenctionStatus(Lists.newArrayList(InspectionStatusEnum.VERIFICATION_PASSED.getCode(),
        InspectionStatusEnum.VERIFICATION_FAILED.getCode(),
        InspectionStatusEnum.TO_BE_VERIFIED.getCode(),
        InspectionStatusEnum.NOT_COMPLETED.getCode()));
        log.info("查询阵地巡检列表，请求参数：{}", request);
        PageResponse<PositionInspectionItem> response = positionInspectionService.listPositionInspection(request);
        return new CommonApiResponse<>(response);
    }

    /**
     * 阵地巡检提交信息
     *
     * @param request 请求参数
     * @return 阵地巡检提交响应
     */
    @PostMapping("/submit")
    @ResponseBody
    @ApiDoc(name = "App阵地巡检提交", value = "/api/position/inspection/submit", description = "阵地巡检提交")
    public CommonApiResponse<String> submitPositionInspection(@RequestBody PositionInspectionSubmitRequest request) {
        String account = this.getAccount();
        request.setOwner(account);
        log.info("阵地巡检提交，请求参数：{}", request);

        return positionInspectionService.submitPositionInspection(request);
    }
    /**
     * 阵地巡检提交信息
     *
     * @param request 请求参数
     * @return 阵地巡检提交响应
     */
    @PostMapping("/detail")
    @ResponseBody
    @ApiDoc(name = "App阵地巡检详情页查询", value = "/api/position/inspection/detail", description = "阵地巡检详情页查询")
    public CommonApiResponse<PositionInspectionDetailResponse> getPositionInspectionDetail(@RequestBody PositionInspectionDetailRequest request) {
        log.info("阵地巡检详情页查询，请求参数：{}", request);
        return positionInspectionService.getPositionInspectionDetail(request);
    }

    /**
     * APP巡检示例图
     *
     *
     * @return APP巡检示例图响应
     */
    @PostMapping("/sample")
    @ResponseBody
    @ApiDoc(name = "APP巡检示例图", value = "/api/position/inspection/sample", description = "APP巡检示例图")
    public CommonApiResponse<Map<String, String[]>> samplePositionInspection() {
        Map<String, String[]> hashMap = new HashMap<>();
        try (InputStream inputStream = JsonReader.class.getClassLoader().getResourceAsStream(FILE_NAME)) {
            if (inputStream != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                hashMap = objectMapper.readValue(inputStream, Map.class);
            }
        } catch (Exception e) {
            throw new RuntimeException("Error reading JSON file: " + e.getMessage(), e);
        }
        return new CommonApiResponse<>(hashMap);
    }

    /**
     * 查询阵地巡检操作历史
     * @param request InspectionHistoryRequest对象，包含positionInspectionId
     * @return 操作历史列表
     */
    @PostMapping("/operationHistory")
    @ResponseBody
    @ApiDoc(name = "查询阵地巡检操作历史", value = "/api/position/inspection/operationHistory", description = "查询阵地巡检操作历史")
    public CommonApiResponse<List<PositionInspectionHistoryItem>> operationHistory(@RequestBody @Valid PositionInspectionHistoryRequest request) {
        return positionInspectionService.operationHistory(request);
    }

    /**
     * 查询阵地家具列表
     *
     * @param request 阵地家具查询请求参数
     * @return 阵地家具列表响应
     */
    @PostMapping("/furnitures")
    @ResponseBody
    @ApiDoc(name = "查询阵地家具列表", value = "/api/position/inspection/furnitures", description = "查询阵地家具列表")
    public CommonApiResponse<List<OptionalItem<Integer>>> getPositionFurnitureList(
            @RequestBody @Valid PositionFurnitureRequest request) {
        return positionInspectionService.getPositionFurnitureList(request);
    }

    @PostMapping("abnormalReason")
    @ResponseBody
    @ApiDoc(name = "阵地家具列表异常原因选项", value = "/api/position/inspection/abnormalReason", description = "阵地家具列表异常原因选项")
    public CommonApiResponse<List<OptionalItem<Integer>>> abnormalReason() {
        return positionInspectionService.getAbnormalReason();
    }

    /**
     * 获取巡检记录汇总数据
     *
     * @param request 阵地巡检请求参数
     * @return 巡检记录汇总数据响应
     */
    @PostMapping("/summary")
    @ResponseBody
    @ApiDoc(name = "获取巡检记录汇总数据", value = "/mtop/intl-retail/api/position/inspection/summary", description = "获取巡检记录汇总数据")
    public CommonApiResponse<InspectionSummaryDTO> getSummary(@RequestBody PositionInspectionRequest request) {
        log.info("获取巡检记录汇总数据，请求参数：{}", request);
        request.setOwner(this.getAccount());
        InspectionSummaryDTO summaryDTO = positionInspectionService.summary(request);
        return new CommonApiResponse<>(summaryDTO);
    }
}
