package com.mi.info.intl.retail.intlretail.app.controller.i18n;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @date 2021/6/28
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class GetAreaDetailResp implements Serializable {

    private static final long serialVersionUID = 1911815840440739630L;
    private List<AreaItem> areaItemList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class AreaItem implements Serializable {
        private static final long serialVersionUID = -8122322750502994857L;

        private String areaId;
        private String shortNameCn;
        private String shortNameEn;
        private String fullNameEn;
        private String timezone;
        private String threeLetterId;
        private Integer digitalCode;
        private Long wmsCountryId;
        private String currency;
        private String currencyCode;
        private Integer currencyDigits;
        private String currencyThousandSymbol;
        private String currencyDigitsSymbol;
        private String localeCode;
        private String phonePrefix;
        private String idc;
        private String region;
        private String continent;
        private Integer status;
        private Extend extend;

        @Data
        @Builder
        @AllArgsConstructor
        @NoArgsConstructor
        public static class Extend implements Serializable {
            private static final long serialVersionUID = -2404988433960084714L;

            private String taxRate;
            private String emailMatch;
            private String telMatch;
            private String dateFormat;
            private String dateFormatJava;
            private String dateTimeMinFormat;
            private String dateTimeMinFormatJava;
            private String dateTimeSecFormat;
            private String dateTimeSecFormatJava;
            private String currencyMinorUnit;
            private String unicodeList;
        }
    }

}
