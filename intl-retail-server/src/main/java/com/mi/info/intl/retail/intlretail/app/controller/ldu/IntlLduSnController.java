package com.mi.info.intl.retail.intlretail.app.controller.ldu;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.service.api.ldu.IntlLduSnService;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.BathConReq;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnDto;
import com.mi.info.intl.retail.intlretail.service.api.ldu.dto.IntlLduSnReq;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.mone.docs.annotations.http.MiApiRequestMethod;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025-07-03
 */
@ApiModule(value = "国际新零售平台", apiInterface = IntlLduSnController.class)
@RestController
@RequestMapping("/api/lduPlanMaintenance")
public class IntlLduSnController {

    @Resource
    private IntlLduSnService intlLduSnService;

    @ApiDoc(name = "查询LDU列表", value = "/api/lduPlanMaintenance/pageList", method = MiApiRequestMethod.POST)
    @PostMapping("/pageList")
    public CommonApiResponse<IPage<IntlLduSnDto>> pageList(@RequestBody IntlLduSnReq query) {
        return intlLduSnService.pageList(query);
    }

    @ApiDoc(name = "新增LDU", value = "/api/lduPlanMaintenance/create", method = MiApiRequestMethod.POST)
    @PostMapping("/create")
    public CommonApiResponse<String> create(@RequestBody IntlLduSnDto query) {
        return intlLduSnService.create(query);
    }

    @ApiDoc(name = "导出LDU计划维护", value = "/api/lduPlanMaintenance/exportPlanMaintenance", method = MiApiRequestMethod.POST)
    @PostMapping("/exportPlanMaintenance")
    public CommonResponse<String> exportPlanMaintenance(@RequestBody IntlLduSnReq query) {
        return intlLduSnService.exportPlanMaintenance(query);
    }

    @ApiDoc(name = "停用LDU计划维护", value = "/api/lduPlanMaintenance/stopUse", method = MiApiRequestMethod.POST)
    @PostMapping("/stopUse")
    public CommonApiResponse<String> stopUse(@RequestBody IntlLduSnReq query) {
        return intlLduSnService.stopUse(query);
    }

    @ApiDoc(name = "批量停用LDU计划维护", value = "/api/lduPlanMaintenance/importStopPlanMaintenance", method = MiApiRequestMethod.POST)
    @PostMapping("/importStopPlanMaintenance")
    public CommonApiResponse<List<String>> importStopPlanMaintenance(@RequestBody BathConReq query) {
        return intlLduSnService.importStopPlanMaintenance(query);
    }

    @ApiDoc(name = "批量新增LDU计划维护", value = "/api/lduPlanMaintenance/importPlanMaintenance", method = MiApiRequestMethod.POST)
    @PostMapping("/importPlanMaintenance")
    public CommonApiResponse<List<String>> importPlanMaintenance(@RequestBody BathConReq query) {
        return intlLduSnService.importPlanMaintenance(query);
    }

    @ApiDoc(name = "下载LDU计划维护excel模版", value = "/api/lduPlanMaintenance/downLoadLduTemp", method = MiApiRequestMethod.POST)
    @PostMapping("/downLoadLduTemp")
    public CommonApiResponse<String> downLoadLduTemp(@RequestBody BathConReq query) {
        return intlLduSnService.downLoadLduTemp(query);
    }

}
