package com.mi.info.intl.retail.intlretail.app.controller;

import java.util.List;
import java.util.Map;

import javax.annotation.Resource;

import org.springframework.web.bind.annotation.*;

import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QueryApproverListReq;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySuRuleRetailerReq;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.rule.aggregate.SoRuleAggregateService;
import com.mi.info.intl.retail.so.domain.rule.bean.GetRetailerSoRuleResp;
import com.mi.info.intl.retail.so.domain.rule.bpm.SoRuleBpmCallBack;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.domain.sys.service.OrganizationUserService;

import lombok.extern.slf4j.Slf4j;

/**
 * 因此，规则控制器
 *
 * <AUTHOR>
 * @date 2025/07/29
 */
@Slf4j
@RestController
@RequestMapping("/api/so/v1")
public class SoRuleController extends BaseController {
    @Resource
    private IntlSysDictService intlSysDictService;

    @Resource
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Resource
    private SoRuleAggregateService soRuleAggregateService;

    @Resource
    private OrganizationUserService organizationUserService;

    @Resource
    private SoRuleBpmCallBack soRuleBpmCallBack;

    @PostMapping("/getDictLabelListByType")
    @ResponseBody
    public CommonApiResponse<Map<String, List<LabelValueDTO>>>
        getLabelValueListByDictCode(@RequestBody DictSysRequest request) {
        return new CommonApiResponse<>(intlSysDictService.getLabelValueListByDictCode(request));

    }

    @PostMapping("/getRetailerSoRule")
    @ResponseBody
    public CommonApiResponse<GetRetailerSoRuleResp> getRetailerSoRule(@RequestBody GetRetailerSoRuleReq request) {
        return new CommonApiResponse<>(intlSoRuleRetailerService.getRetailerSoRule(request));

    }

    @GetMapping("/getApproverList")
    public CommonApiResponse<List<ApproverDTO>>
        getApproverList(@RequestParam(value = "countryCode") String countryCode) {
        QueryApproverListReq req = new QueryApproverListReq();
        req.setCountryCode(countryCode);
        return new CommonApiResponse<>(organizationUserService.getApproverList(req));

    }

    @PostMapping("/getRetailList")
    @ResponseBody
    public CommonApiResponse<PageDTO<SoRuleRetailerDTO>> getRetailList(@RequestBody QuerySuRuleRetailerReq request) {
        return new CommonApiResponse<>(intlSoRuleRetailerService.getRetailerByCondition(request));

    }

    @PostMapping("/exportRetailerList")
    @ResponseBody
    public CommonApiResponse<String> exportRetailerList(@RequestBody QuerySuRuleRetailerReq request) {
        return new CommonApiResponse<>(intlSoRuleRetailerService.exportRetailerList(request));

    }

    @PostMapping("/createRule")
    public CommonApiResponse<Long> createRule(@RequestBody SoRuleDetailCreateDTO ruleDetailCreateDTO) {
        return CommonApiResponse.success(soRuleAggregateService.createRuleFlow(ruleDetailCreateDTO));
    }

    @PostMapping("/modifyRule")
    public CommonApiResponse<Long> modifyRule(@RequestBody SoRuleDetailModifyDTO ruleDetailModifyDto) {
        return CommonApiResponse.success(soRuleAggregateService.modifyRuleFlow(ruleDetailModifyDto));
    }

    @GetMapping("/getRuleDetail")
    public CommonApiResponse<SoRuleDetailResultDTO> getRuleDetail(@RequestParam Long id) {
        return CommonApiResponse.success(soRuleAggregateService.getRuleDetail(id));
    }

    @PostMapping("/validateRule")
    public CommonApiResponse<SoRuleValidateResultDTO>
        validateRuleRetailers(@RequestBody SoRuleValidateDTO retailerValidateDto) {
        return CommonApiResponse.success(soRuleAggregateService.validateRule(retailerValidateDto));
    }

    @PostMapping("/getRuleList")
    public CommonApiResponse<PageDTO<SoRuleDetailResultDTO>>
        getRuleList(@RequestBody SoRuleDetailQueryDTO soRuleQueryDto) {
        return CommonApiResponse.success(soRuleAggregateService.getRuleList(soRuleQueryDto));
    }

    /**
     * 回调审批流程
     * 
     * @param bpmCallBack 回调参数
     * @return void
     */
    @PostMapping("/modifyRuleCallBack")
    public CommonApiResponse<Void> soRuleBpmCallBack(@RequestBody BpmCallBackParamDto bpmCallBack) {
        soRuleBpmCallBack.doCallback(bpmCallBack);
        return CommonApiResponse.success(null);
    }

    @PostMapping("/modifyRuleRecall")
    public CommonApiResponse<String>
    modifyRuleRecall(@RequestBody SoRuleDetailModifyRecallDTO modifyRecallDto) {
        soRuleAggregateService.approveRecalled(modifyRecallDto);
        return CommonApiResponse.success(null);
    }

}
