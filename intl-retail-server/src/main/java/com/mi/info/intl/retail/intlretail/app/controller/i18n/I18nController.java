package com.mi.info.intl.retail.intlretail.app.controller.i18n;

import com.xiaomi.com.i18n.area.Area;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.youpin.infra.rpc.Result;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR> <PERSON> 董鑫儒
 * @Description
 * @Date 创建于 2025/8/5 16:30
 */
@RestController
@RequestMapping({"/api", "/*/api"})
public class I18nController {

    @ApiDoc("国际版-获取用户在所有地区的角色，api/proretail/config/area/detail")
    @PostMapping("/proretail/config/area/detail")
    @ResponseBody
    public Result<GetAreaDetailResp> areaDetail(AreaDetailReq req) {
        GetAreaDetailResp areaDetailResp = new GetAreaDetailResp();
        List<GetAreaDetailResp.AreaItem> areaItemList = new ArrayList<>();
        areaDetailResp.setAreaItemList(areaItemList);
        // queryAll的话，就查全部
        // areaId 不为空就查这个areaId的数据
        // areaId为空就取header中的areaId
        // 那不是总部角色，就不能获取全部数据

        final boolean isQueryAll = req.getQueryAll() != null && req.getQueryAll() == 1;
        if (isQueryAll) {
            List<String> onlineAreaIdList = Area.onlineAreaIdList();
            List<Area> allArea = Area.all();
            for (Area area : allArea) {
                if (onlineAreaIdList.contains(area.getAreaId())) {
                    GetAreaDetailResp.AreaItem areaItem = new GetAreaDetailResp.AreaItem();
                    BeanUtils.copyProperties(area, areaItem);

                    // extend 复制
                    Area.Extend extend = area.getExtend();
                    if (extend != null) {
                        GetAreaDetailResp.AreaItem.Extend extendItem = new GetAreaDetailResp.AreaItem.Extend();
                        BeanUtils.copyProperties(extend, extendItem);
                        areaItem.setExtend(extendItem);
                    }
                    areaItemList.add(areaItem);
                }
            }
        } else {
            final String areaId = com.xiaomi.nr.global.dev.base.RequestContextInfo.getAreaId();

            String areaIdQuery = StringUtils.isNotBlank(req.getAreaId()) ? req.getAreaId() : areaId;
            Area area = Area.of(areaIdQuery);
            if (area == null) {
                return Result.fail(GeneralCodes.ParamError, "area not found " + areaIdQuery);
            }

            GetAreaDetailResp.AreaItem areaItem = new GetAreaDetailResp.AreaItem();
            BeanUtils.copyProperties(area, areaItem);

            // extend 复制
            Area.Extend extend = area.getExtend();
            if (extend != null) {
                GetAreaDetailResp.AreaItem.Extend extendItem = new GetAreaDetailResp.AreaItem.Extend();
                BeanUtils.copyProperties(extend, extendItem);
                areaItem.setExtend(extendItem);
            }
            areaItemList.add(areaItem);
        }
        return Result.success(areaDetailResp);
    }
}
