package com.mi.info.intl.retail.intlretail.app.controller;

import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.ImeiReportVerifyService;
import com.mi.info.intl.retail.intlretail.service.api.so.QtyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.ImeiUploadService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.QtyImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.SoImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.ImeiImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportResponse;
import com.mi.info.intl.retail.model.CommonApiResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/api/so")
public class SoUploadController {
    @Resource
    private ImeiUploadService imeiUploadService;
    @Resource
    private QtyService qtyService;
    @Resource
    private ImeiReportVerifyService imeiReportVerifyService;
    @Resource
    private ImeiImportService imeiImportService;
    @Resource
    private SoImportService soImportService;
    @Resource
    private QtyImportService qtyImportService;


    @PostMapping("/submitImei")
    @ResponseBody
    public CommonApiResponse<Object> submitImei(@RequestBody SubmitImeiReq request) {
        log.info("submitImei request: {}", request);
        return imeiUploadService.submitImei(request);
    }

    @PostMapping("/imeiReportVerify")
    @ResponseBody
    public CommonApiResponse<Object> imeiReportVerify(@RequestBody ImeiReportVerifyRequest request) {
        return imeiReportVerifyService.imeiReportVerify(request);
    }

    @PostMapping("/getSkuList")
    @ResponseBody
    public CommonResponse<GetSkuListResponse> getSkuList(@RequestBody GetSkuListRequest request) {
        log.info("getSkuList request: {}", request);
        return qtyService.getSkuList(request);
    }

    @PostMapping("/submitQty")
    @ResponseBody
    public CommonResponse<Object> submitQty(@RequestBody SubmitQtyReq request) {
        log.info("submitQty request: {}", request);
        return qtyService.submitQty(request);
    }

    @PostMapping("/getFilterList")
    @ResponseBody
    public CommonResponse<GetFilterListResponse> getFilterList() {
        log.info("getFilterList request");
        return qtyService.getFilterList();
    }

    @PostMapping("/queryQtyStatistics")
    @ResponseBody
    public CommonResponse<QueryQtyStatisticsResponse> queryQtyStatistics(@RequestBody QueryQtyStatisticsRequest request) {
        log.info("queryQtyStatistics request: {}", request);
        return qtyService.queryQtyStatistics(request);
    }

    @PostMapping("/queryQtyList")
    @ResponseBody
    public CommonResponse<QueryQtyListResponse> queryQtyList(@RequestBody QueryQtyListRequest request) {
        log.info("queryQtyList request: {}", request);
        return qtyService.queryQtyList(request);
    }

    @PostMapping("/getQtyBoardData")
    @ResponseBody
    public CommonResponse<GetQtyBoardDataResponse> getQtyBoardData(@RequestBody GetQtyBoardDataRequest request) {
        log.info("getQtyBoardData request: {}", request);
        return qtyService.getQtyBoardData(request);
    }

    @PostMapping("/queryQtyDetail")
    @ResponseBody
    public CommonResponse<QueryQtyDetailResponse> queryQtyDetail(@RequestBody QueryQtyDetailRequest request) {
        log.info("queryQtyDetail request: {}", request);
        return qtyService.queryQtyDetail(request);
    }

    @PostMapping("/getStoreList")
    @ResponseBody
    public CommonResponse<GetStoreListResponse> getStoreList(@RequestBody GetStoreListRequest request) {
        log.info("getStoreList request: {}", request);
        return qtyService.getStoreList(request);
    }

    @PostMapping("/queryImeiListByPage")
    @ResponseBody
    public CommonApiResponse<ImeiListQueryResp> queryImeiListByPage(@RequestBody ImeiListQueryReq request) {
        log.info("queryImeiListByPage request: {}", request);
        return imeiUploadService.queryImeiListByPage(request);
    }

    @PostMapping("/queryImeiDetail")
    @ResponseBody
    public CommonApiResponse<ImeiDetailQueryResp> queryImeiDetail(@RequestBody ImeiDetailQueryReq request) {
        log.info("queryImeiDetail request: {}", request);
        return imeiUploadService.queryImeiDetail(request);
    }

    @PostMapping("/queryImeiSummary")
    @ResponseBody
    public CommonApiResponse<ImeiSummaryQueryResp> queryImeiSummary(@RequestBody ImeiSummaryQueryReq request) {
        log.info("queryImeiSummary request: {}", request);
        return imeiUploadService.queryImeiSummary(request);
    }

    @PostMapping("/getImportTemplate")
    @ResponseBody
    public CommonApiResponse<GetImportTemplateResponse> getImportTemplate(@RequestBody GetImportTemplateRequest request) {
        log.info("getImportTemplate request: {}", request);
        return soImportService.getImportTemplate(request);
    }

    @PostMapping("/importImeiData")
    @ResponseBody
    public CommonApiResponse<ImportDataResponse> queryImeiSummary(@RequestBody ImeiImportRequest request) {
        log.info("importImeiData request: {}", request);
        return imeiImportService.importImeiData(request);
    }

    @PostMapping("/importData")
    @ResponseBody
    public CommonApiResponse<ImportDataResponse> importData(@RequestBody ImportDataRequest request) {
        log.info("importData request: {}", request);
        return soImportService.importData(request);
    }
    @PostMapping("/imeiBarcodeRead")
    @ResponseBody
    public CommonApiResponse<Object> imeiBarcodeRead(@RequestBody ImeiBarcodeReadReq request) {
        log.info("imeiBarcodeRead request: {}", request);
        return imeiUploadService.imeiBarcodeRead(request);
    }
    @PostMapping("/qtyImportData")
    @ResponseBody
    public CommonApiResponse<QtyImportResponse> qtyImportData(@RequestBody QtyImportRequest request) {
        log.info("QtyImportData request: {}", request);
        return qtyImportService.importData(request);
    }

}
