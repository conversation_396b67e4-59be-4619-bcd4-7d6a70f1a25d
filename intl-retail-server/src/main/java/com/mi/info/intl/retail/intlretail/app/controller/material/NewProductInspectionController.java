package com.mi.info.intl.retail.intlretail.app.controller.material;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.collect.Lists;
import com.google.gson.stream.JsonReader;
import com.mi.info.intl.retail.intlretail.app.controller.BaseController;
import com.mi.info.intl.retail.intlretail.service.api.material.NewProductInspectionService;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.ExampleDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationDTO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationInfoVO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationPageResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.InspectionTaskConfigurationRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.IntlStoreMaterialStatusConfrimReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.IntlStoreMaterialStatusVO;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionDetailResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionItem;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionOperationHistoryResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialInspectionReq;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialModelRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.MaterialSampleResponse;
import com.mi.info.intl.retail.intlretail.service.api.material.dto.SubmitMaterialInspectionRequest;
import com.mi.info.intl.retail.intlretail.service.api.material.enums.TaskTypeEnum;
import com.mi.info.intl.retail.intlretail.service.api.position.enums.InspectionStatusEnum;
import com.mi.info.intl.retail.ldu.enums.FileUploadEnum;
import com.mi.info.intl.retail.ldu.service.IntlFileUploadService;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.model.PageResponse;
import com.mi.info.intl.retail.org.domain.repository.IntlStoreMaterialStatusRepository;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import javax.annotation.Resource;
import javax.validation.Valid;

@Slf4j
@RestController
@RequestMapping({"/api/newProduct/inspection", "/*/api/newProduct/inspection"})
@ApiModule(value = "新品物料巡检 Controller", apiInterface = NewProductInspectionController.class)
public class NewProductInspectionController extends BaseController {

    @Resource
    private NewProductInspectionService newProductInspectionService;

    @Autowired
    private IntlFileUploadService fileUploadService;

    @Autowired
    private IntlStoreMaterialStatusRepository intlStoreMaterialStatusRepository;

    public static final String FILE_NAME = "materialexample.json";

    @PostMapping("/list")
    @ApiDoc(name = "查询新品物料巡检列表", value = "/api/newProduct/inspection/list", description = "查询新品物料巡检列表")
    public CommonApiResponse<PageResponse<MaterialInspectionItem>> getMaterialInspectionList(
            @RequestBody MaterialInspectionReq request) {
        String account = this.getAccount();
        request.setOwner(account);
        request.setAreaId(this.getCountryCode());
        request.setInspectionStatus(Lists.newArrayList(InspectionStatusEnum.VERIFICATION_PASSED.getCode(),
                InspectionStatusEnum.VERIFICATION_FAILED.getCode(), InspectionStatusEnum.TO_BE_VERIFIED.getCode(),
                InspectionStatusEnum.NOT_COMPLETED.getCode(), InspectionStatusEnum.COMPLETED.getCode()));
        log.info("查询新品物料巡检列表，请求参数：{}", request);
        return newProductInspectionService.getMaterialInspectionList(request);
    }

    @PostMapping("/detail")
    @ApiDoc(name = "查询新品物料列表详情", value = "/api/newProduct/inspection/detail", description = "查询新品物料列表详情")
    public CommonApiResponse<MaterialInspectionDetailResponse> getMaterialInspectionDetail(
            @RequestBody MaterialInspectionDetailRequest request) {
        String account = this.getAccount();
        request.setOwner(account);
        return newProductInspectionService.getMaterialInspectionDetail(request);
    }

    @PostMapping("/submit")
    @ApiDoc(name = "新品物料列表提交", value = "/api/newProduct/inspection/submit", description = "查询新品物料列表提交")
    public CommonApiResponse<String> submitMaterialInspection(@RequestBody SubmitMaterialInspectionRequest request) {
        String account = this.getAccount();
        request.setOwner(account);
        return newProductInspectionService.submitMaterialInspection(request);
    }

    @PostMapping("/operationHistory")
    @ApiDoc(name = "新品物料巡检操作历史", value = "/api/newProduct/inspection/operationHistory", description = "新品物料巡检操作历史")
    public CommonApiResponse<List<MaterialInspectionOperationHistoryResponse>> getMaterialInspectionOperationHistory(
            @RequestBody MaterialInspectionOperationHistoryRequest request) {
        String account = this.getAccount();
        request.setOwner(account);
        return newProductInspectionService.getMaterialInspectionOperationHistory(request);
    }

    @PostMapping("/materialSample")
    @ApiDoc(name = "APP新品物料巡检示例图", value = "/api/newProduct/inspection/materialSample", description = "APP新品物料巡检示例图")
    public CommonApiResponse<MaterialSampleResponse> getMaterialSample(@RequestBody MaterialSampleRequest request) {
        List<ExampleDTO> data = new ArrayList<>();
        try (InputStream inputStream = JsonReader.class.getClassLoader().getResourceAsStream(FILE_NAME)) {
            if (inputStream != null) {
                ObjectMapper objectMapper = new ObjectMapper();
                ExampleDTO[] exampleDTOS = objectMapper.readValue(inputStream, ExampleDTO[].class);
                data = new ArrayList<>(Arrays.asList(exampleDTOS));
            }
        } catch (Exception e) {
            throw new RuntimeException("Error reading JSON file: " + e.getMessage(), e);
        }
        MaterialSampleResponse materialSampleResponse = newProductInspectionService.getMaterialSample(request);
        TaskTypeEnum taskType = materialSampleResponse.getTaskType();
        // 安全处理 taskType 和 exampleDTO
        if (taskType != null) {
            String shortname = taskType.getShortname();
            Optional<ExampleDTO> exampleDTOOpt = data.stream()
                    .filter(item -> shortname.contains(item.getMaterialKey()))
                    .findFirst();
            exampleDTOOpt.ifPresent(exampleDTO -> materialSampleResponse.setExampleImageList(exampleDTO.getImages()));
        }
        return CommonApiResponse.success(materialSampleResponse);
    }

    @PostMapping("/inspection/task/page")
    public CommonApiResponse<PageResponse<InspectionTaskConfigurationPageResponse>> findInspectionTaskPage(
            @RequestBody InspectionTaskConfigurationPageRequest dto) {
        return newProductInspectionService.findInspectionTaskPage(dto);
    }

    @PostMapping("/inspection/task/info")
    public CommonApiResponse<InspectionTaskConfigurationInfoVO> getInspectionTaskInfo(
            @RequestBody InspectionTaskConfigurationRequest dto) {
        return newProductInspectionService.getInspectionTaskInfo(dto);
    }

    @PostMapping("/inspection/task/type")
    public CommonApiResponse<List<Map<String, Object>>> getEnumByType(
            @RequestBody InspectionTaskConfigurationPageRequest dto) {
        return newProductInspectionService.getEnumByType(dto);
    }

    @PostMapping("/inspection/task/save")
    public CommonApiResponse saveInspectionTask(@RequestBody InspectionTaskConfigurationDTO dto) {
        return newProductInspectionService.saveInspectionTask(dto);
    }

    @PostMapping("/inspection/task/submit")
    public CommonApiResponse submitInspectionTask(@RequestBody InspectionTaskConfigurationDTO dto) {
        return newProductInspectionService.submitInspectionTask(dto);
    }

    @PostMapping("/inspection/task/enable")
    public CommonApiResponse startOrStopInspectionTask(@RequestBody InspectionTaskConfigurationRequest dto) {
        return newProductInspectionService.startOrStopInspectionTask(dto);
    }

    @PostMapping("/inspection/task/export")
    public CommonApiResponse inactiveInspectionTask(@RequestBody InspectionTaskConfigurationPageRequest dto) {
        return newProductInspectionService.exportInspectionTask(dto);
    }

    @PostMapping("/inspection/task/upload")
    public CommonApiResponse inactiveInspectionTask(@RequestBody InspectionTaskConfigurationRequest dto) {
        return newProductInspectionService.uploadAssignedStore(dto);
    }

    @PostMapping("/inspection/task/listArea")
    public CommonApiResponse listArea() {
        return newProductInspectionService.listArea();
    }

    /**
     * 查询离线上传对应的图片
     */
    @PostMapping("/pic")
    public CommonApiResponse<Map<String, List<String>>> getPicUrl(@RequestBody List<String> guids) {
        if (CollectionUtils.isEmpty(guids)) {
            return CommonApiResponse.failure(400, "guids is empty");
        }
        Map<String, List<String>> urlMap =
                fileUploadService.getUrlsByModuleAndGuids(FileUploadEnum.MATERIAL_INSPECTION, guids);
        return CommonApiResponse.success(urlMap);
    }

    /**
     * 查看物料是否收到
     */
    @PostMapping("/material")
    public CommonApiResponse<List<IntlStoreMaterialStatusVO>> material(@RequestBody Map<String, String> ms) {
        String businessCode = ms.get("businessCode");
        if (StringUtils.isBlank(businessCode)) {
            return CommonApiResponse.failure(400, "businessCode is empty");
        }
        List<IntlStoreMaterialStatusVO> list =
                intlStoreMaterialStatusRepository.findByBusinessCode(businessCode);

        return new CommonApiResponse<>(list);
    }
    
    /**
     *
     */
    @PostMapping("/material/confirm")
    public CommonApiResponse<String> materialConfirm(@Validated @RequestBody List<IntlStoreMaterialStatusConfrimReq> list) {
        if (list == null || list.size() == 0) {
            return CommonApiResponse.failure(400, "params is empty");
        }
        intlStoreMaterialStatusRepository.materialConfirm(list);
        return CommonApiResponse.success("");
    }

    @PostMapping("/store/model/list")
    @ApiDoc(name = "APP新品物料巡检-门店售卖机型列表", value = "/api/newProduct/inspection/store/model/list",
            description = "APP新品物料巡检-门店售卖机型列表")
    public CommonApiResponse<List<String>> storeModelList(@RequestBody MaterialModelRequest request) {
        log.info("storeModelList request : {}", request);
        List<String> projects = newProductInspectionService.getStoreModelList(request.getPositionCode());
        return CommonApiResponse.success(projects);
    }

    @PostMapping("/store/model/save")
    @ApiDoc(name = "APP新品物料巡检-门店售卖机型保存", value = "/api/newProduct/inspection/store/model/save",
            description = "APP新品物料巡检-门店售卖机型保存")
    public CommonApiResponse<Void> saveSalesStoreModels(@RequestBody MaterialModelRequest request) {
        log.info("saveSalesStoreModels request : {}", request);

        newProductInspectionService.saveSalesStoreModels(request);
        return CommonApiResponse.success();
    }
}
