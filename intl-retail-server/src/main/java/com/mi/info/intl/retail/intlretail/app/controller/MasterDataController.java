package com.mi.info.intl.retail.intlretail.app.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.MasterDataService;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.*;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.xiaomi.mone.docs.annotations.dubbo.ApiDoc;
import com.xiaomi.mone.docs.annotations.dubbo.ApiModule;
import com.xiaomi.mone.docs.annotations.http.MiApiRequestMethod;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 主数据相关控制器
 *
 * <AUTHOR>
 * @date 2025/7/25
 **/
@Slf4j
@RestController
@RequestMapping("/api/master/data")
@ApiModule(value = "主数据服务", apiInterface = MasterDataController.class)
public class MasterDataController extends BaseController {

    @Resource
    private MasterDataService masterDataService;

    /**
     * 黑名单列表
     */
    @ApiDoc(name = "分页查询黑名单列表", value = "/api/master/data/pageBlackList", method = MiApiRequestMethod.POST)
    @PostMapping("/pageBlackList")
    public CommonApiResponse<IPage<SnBlackDTO>> queryBlackList(@RequestBody SnBlackRequest request) {
        return masterDataService.pageQuerySnBlackList(request);
    }

    /**
     * 导入黑名单
     */
    @ApiDoc(name = "导入黑名单", value = "/api/master/data/importBlackList", method = MiApiRequestMethod.POST)
    @PostMapping("/importBlackList")
    public CommonApiResponse<ImportSnBlacklistErrorDTO> importBlackList(@RequestBody ImportBlacklistRequest request) {
        return masterDataService.importSnBlackList(request);
    }

    /**
     * 导出黑名单
     */
    @ApiDoc(name = "导出黑名单", value = "/api/master/data/exportBlackList", method = MiApiRequestMethod.POST)
    @PostMapping("/exportBlackList")
    public CommonApiResponse<String> exportBlackList(@RequestBody SnBlackRequest request) {
        return masterDataService.exportSnBlackList(request);
    }

    /**
     * 批量停用（导入文件）
     */
    @ApiDoc(name = "批量停用", value = "/api/master/data/batchDisable", method = MiApiRequestMethod.POST)
    @PostMapping("/batchDisable")
    public void batchDisable(@RequestBody BatchDisableRequest request) {
        masterDataService.batchDisable(request);
    }

    /**
     * 分页查询明文用户列表
     */
    @ApiDoc(name = "分页查询明文用户列表", value = "/api/master/data/pageQueryPlainTextUser", method = MiApiRequestMethod.POST)
    @PostMapping("/pageQueryPlainTextUser")
    public CommonApiResponse<IPage<PlainTextImeiUserDTO>> pageQueryPlainTextUser(
            @RequestBody PlainTextUserRequest request) {
        return masterDataService.pageQueryPlainTextUser(request);
    }

    /**
     * 新增明文用户
     */
    @ApiDoc(name = "新增明文用户", value = "/api/master/data/addPlainTextUser", method = MiApiRequestMethod.POST)
    @PostMapping("/addPlainTextUser")
    public CommonApiResponse<String> addPlainTextUser(@RequestBody AddPlainTextImeiUserRequest request) {
        return masterDataService.addPlainTextUser(request);
    }

    /**
     * 删除明文用户
     */
    @ApiDoc(name = "删除明文用户", value = "/api/master/data/deletePlainTextUser", method = MiApiRequestMethod.POST)
    @PostMapping("/deletePlainTextUser")
    public CommonApiResponse<String> deletePlainTextUser(@RequestBody DeletePlainTextUserRequest request) {
        return masterDataService.deletePlainTextUser(request);
    }

    @ApiDoc(name = "查询用户信息", value = "/api/master/data/userInfo", method = MiApiRequestMethod.GET)
    @GetMapping("/getUserInfo")
    public CommonApiResponse<GetUserInfoResp> getUserInfo(@RequestParam("userId") Long userId) {
        return masterDataService.getUserInfo(new UserInfoRequest().setKeyWord(userId));
    }

}
