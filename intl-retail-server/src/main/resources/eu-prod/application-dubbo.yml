nacos:
  namespace: public
  address: nacos://nacos.systech.b2c.srv:80
  config:
    address: nacos.systech.b2c.srv:80
init:
  # 这个不能动，否则不能上传，app的group已经预设好了
  group: release
  app:
    name: intl-retail-eu

store:
  dubbo:
    group: eu_online

xmstore:
  dubbo:
    group: eu-online

iib:
  dubbo:
    group: sgp_online_iib
push:
  dubbo:
    group: eu_online_push
proretailbi:
  dubbo:
    group: eu_online
center:
  dubbo:
    group: eu_online

cache:
  dubbo:
    group: eu_online_cache

college:
  dubbo:
    group: eu_online

eiam:
  dubbo:
    group: eu_online
retail:
  dubbo:
    group: eu_online

organization:
  scene: new_retail
  # manageChannelList参数：1-新零售直营，2-新零售授权，27-渠道零售
  organMap:
    256:
      positionId: 256
      organCode: GLOBAL
      positionName: HQ Strategic Operation
      manageChannelList:
        - 27
    242:
      positionId: 242
      organCode: null
      positionName: National Retail Manager
      manageChannelList:
        - 27
    255:
      positionId: 255
      organCode: GLOBAL
      positionName: HQ Strategy Manager
      manageChannelList:
        - 27
  dubbo-group: eu_online
  # 文件服务器
dubbo:
  nr-upload-center:
    projectId: 892
    appId: xm-yp-upc-0252
    appKey: 22092fb7edc433319d459f21c16815ad