nacos:
  namespace: public
  address: nacos://sgp.nacos.test.b2c.srv:80
  config:
    address: sgp.nacos.test.b2c.srv:80
init:
  group: ${user.name}
  app:
    name: intl-retail

#dubbo provider 被调用的 需要统一为user.name
dubbo-group:
  provider:
    intl-retail: ${user.name}
  consumer:
    center: staging
    channelBuild: sg_staging

store:
  dubbo:
    group: ${user.name}
cache:
  dubbo:
    group: ${user.name}
push:
  dubbo:
    group: ${user.name}
center:
  dubbo:
    group: ${user.name}
retail:
  dubbo:
    group: ${user.name}

#dubbo consumer
iib:
  dubbo:
    group: sg_staging
maindata:
  dubbo:
    group: sg_staging
copilot:
  dubbo:
    group: staging
proretailbi:
  dubbo:
    group: sg_staging
college:
  dubbo:
    group: sg_staging
eiam:
  dubbo:
    group: sg_staging
file:
  dubbo:
    group: sg_staging

# Added from application-conf.yml
env: dev
miwork:
  alarm:
    groupId:
      p0: oc_1e6d1e54ac5eae84730fe55e86a013e7
      p1: oc_1e6d1e54ac5eae84730fe55e86a013e7
      p2: oc_1e6d1e54ac5eae84730fe55e86a013e7
nr:
  dubbo:
    group: sg_staging

organization:
  scene: new_retail
  # manageChannelList参数：1-新零售直营，2-新零售授权，27-渠道零售
  organMap:
    256:
      positionId: 256
      organCode: GLOBAL
      positionName: HQ Strategic Operation
      manageChannelList:
        - 27
    242:
      positionId: 242
      organCode: null
      positionName: National Retail Manager
      manageChannelList:
        - 27
    255:
      positionId: 255
      organCode: GLOBAL
      positionName: HQ Strategy Manager
      manageChannelList:
        - 27
  dubbo-group: sg_staging
dubbo:
  nr-upload-center:
    projectId: 892
    appId: xm-yp-upc-0892
    appKey: 585e247677365126ec06d7591be4e9e6
