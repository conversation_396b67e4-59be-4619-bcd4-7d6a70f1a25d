nacos:
  namespace: public
  address: nacos://nacos.systech.b2c.srv:80
  config:
    address: nacos.systech.b2c.srv:80
init:
  # 这个不能动，否则不能上传，app的group已经预设好了
  group: pre
  app:
    name: intl-retail

store:
  dubbo:
    group: sg_staging

xmstore:
  dubbo:
    group: sg-preview

iib:
  dubbo:
    group: sgp_online_iib
push:
  dubbo:
    group: sg_pre_push

copilot:
  dubbo:
    group: sg_preview
proretailbi:
  dubbo:
    group: preview
center:
  dubbo:
    group: preview
eiam:
  dubbo:
    group: sg_preview
cache:
  dubbo:
    group: sg_pre_cache
retail:
  dubbo:
    group: sg_preview

organization:
  scene: new_retail
  # manageChannelList参数：1-新零售直营，2-新零售授权，27-渠道零售
  organMap:
    256:
      positionId: 256
      organCode: GLOBAL
      positionName: HQ Strategic Operation
      manageChannelList:
        - 27
    242:
      positionId: 242
      organCode: null
      positionName: National Retail Manager
      manageChannelList:
        - 27
    255:
      positionId: 255
      organCode: GLOBAL
      positionName: HQ Strategy Manager
      manageChannelList:
        - 27
  dubbo-group: sg_online
