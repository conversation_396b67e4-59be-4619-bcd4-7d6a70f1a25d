package com.mi.info.intl.retail.filter;

import org.apache.dubbo.common.constants.CommonConstants;
import org.apache.dubbo.common.extension.Activate;
import org.apache.dubbo.rpc.*;
import org.slf4j.MDC;

import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.utils.TraceIdUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * Dubbo TraceId 过滤器 自动处理 traceId 的传递，支持链路追踪
 *
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
@Activate(group = {CommonConstants.PROVIDER, CommonConstants.CONSUMER})
public class TraceIdFilter implements Filter {

    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        // 获取当前 traceId，没有获取到，那么设置设置到 RpcContext 和 MDC
        String traceId = TraceIdUtil.getCurrentTraceId();

        // 记录调用信息
        String serviceName = invoker.getInterface().getSimpleName();
        String methodName = invocation.getMethodName();

        log.debug("{} -> {}#{}, traceId: {}", RpcContext.getContext().isConsumerSide() ? "Consumer" : "Provider",
            serviceName, methodName, traceId);

        // 设置到 MDC
        MDC.put(CommonConstant.MDC_TRACE_ID_KEY, traceId);
        try {
            // 执行调用
            Result result = invoker.invoke(invocation);

            // 记录调用结果
            if (result.hasException()) {
                log.warn("{} -> {}#{} failed, traceId: {}, exception: {}",
                    RpcContext.getContext().isConsumerSide() ? "Consumer" : "Provider", serviceName, methodName,
                    traceId, result.getException().getMessage());
            } else {
                log.debug("{} -> {}#{} success, traceId: {}",
                    RpcContext.getContext().isConsumerSide() ? "Consumer" : "Provider", serviceName, methodName,
                    traceId);
            }

            return result;
        } catch (RpcException e) {
            log.error("{} -> {}#{} error, traceId: {}, exception: {}",
                RpcContext.getContext().isConsumerSide() ? "Consumer" : "Provider", serviceName, methodName, traceId,
                e.getMessage(), e);
            throw e;
        } finally {
            // 清理 traceId
            TraceIdUtil.clearCurrentTraceId();
        }
    }
}
