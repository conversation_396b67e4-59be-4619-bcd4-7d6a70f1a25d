package com.mi.info.intl.retail.utils;

import java.util.UUID;

import org.apache.dubbo.rpc.RpcContext;
import org.slf4j.MDC;

import com.mi.info.intl.retail.constant.CommonConstant;

import cn.hutool.core.lang.ObjectId;
import lombok.extern.slf4j.Slf4j;

/**
 * Dubbo TraceId 工具类 用于生成、设置和获取 traceId，支持链路追踪
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
public class TraceIdUtil {

    /**
     * 生成新的 traceId 优先使用 ObjectId，如果没有则使用 UUID
     * 
     * @return traceId
     */
    public static String generateTraceId() {
        try {
            return ObjectId.next();
        } catch (Exception e) {
            log.warn("Failed to generate ObjectId, fallback to UUID: {}", e.getMessage());
            return UUID.randomUUID().toString().replace("-", "");
        }
    }

    /**
     * 获取当前线程的 traceId 优先级：RpcContext > MDC > 生成新的
     * 
     * @return traceId
     */
    public static String getCurrentTraceId() {
        // 1. 从 RpcContext 获取
        String traceId = RpcContext.getContext().getAttachment(CommonConstant.TRACE_ID_KEY);
        if (traceId != null && !traceId.isEmpty()) {
            return traceId;
        }

        // 2. 从 MDC 获取
        traceId = MDC.get(CommonConstant.MDC_TRACE_ID_KEY);
        if (traceId != null && !traceId.isEmpty()) {
            return traceId;
        }

        // 3. 生成新的 traceId
        traceId = generateTraceId();
        setCurrentTraceId(traceId);
        return traceId;
    }

    /**
     * 设置当前线程的 traceId 同时设置到 RpcContext 和 MDC
     * 
     * @param traceId traceId
     */
    public static void setCurrentTraceId(String traceId) {
        if (traceId == null || traceId.isEmpty()) {
            log.warn("TraceId is null or empty, skip setting");
            return;
        }

        try {
            // 设置到 RpcContext
            RpcContext.getContext().setAttachment(CommonConstant.TRACE_ID_KEY, traceId);

            // 设置到 MDC
            MDC.put(CommonConstant.MDC_TRACE_ID_KEY, traceId);

            log.debug("Set traceId: {}", traceId);
        } catch (Exception e) {
            log.warn("Failed to set traceId: {}", e.getMessage());
        }
    }

    /**
     * 清除当前线程的 traceId
     */
    public static void clearCurrentTraceId() {
        try {
            // 清除 RpcContext
            RpcContext.getContext().removeAttachment(CommonConstant.TRACE_ID_KEY);

            // 清除 MDC
            MDC.remove(CommonConstant.MDC_TRACE_ID_KEY);

            log.debug("Cleared traceId");
        } catch (Exception e) {
            log.warn("Failed to clear traceId: {}", e.getMessage());
        }
    }

    /**
     * 检查当前线程是否有 traceId
     * 
     * @return 是否有 traceId
     */
    public static boolean hasTraceId() {
        String traceId = getCurrentTraceId();
        return traceId != null && !traceId.isEmpty();
    }

    /**
     * 从 RpcContext 获取 traceId
     * 
     * @return traceId，如果没有则返回 null
     */
    public static String getTraceIdFromRpcContext() {
        return RpcContext.getContext().getAttachment(CommonConstant.TRACE_ID_KEY);
    }

    /**
     * 从 MDC 获取 traceId
     * 
     * @return traceId，如果没有则返回 null
     */
    public static String getTraceIdFromMDC() {
        return MDC.get(CommonConstant.MDC_TRACE_ID_KEY);
    }

    /**
     * 从 MDC 获取 traceId
     *
     * @return traceId，如果没有则返回 null
     */
    public static void removeTraceIdFromMDC() {
        MDC.remove(CommonConstant.MDC_TRACE_ID_KEY);
    }

    /**
     * 设置 traceId 到 RpcContext
     * 
     * @param traceId traceId
     */
    public static void setTraceIdToRpcContext(String traceId) {
        if (traceId != null && !traceId.isEmpty()) {
            RpcContext.getContext().setAttachment(CommonConstant.TRACE_ID_KEY, traceId);
        }
    }

    /**
     * 设置 traceId 到 MDC
     * 
     * @param traceId traceId
     */
    public static void setTraceIdToMDC(String traceId) {
        if (traceId != null && !traceId.isEmpty()) {
            MDC.put(CommonConstant.MDC_TRACE_ID_KEY, traceId);
        }
    }

    /**
     * 创建带有 traceId 的日志前缀
     * 
     * @return 日志前缀
     */
    public static String getLogPrefix() {
        String traceId = getCurrentTraceId();
        return String.format("[TraceId: %s] ", traceId);
    }

    /**
     * 创建带有 traceId 的日志前缀（自定义前缀）
     * 
     * @param prefix 自定义前缀
     * @return 日志前缀
     */
    public static String getLogPrefix(String prefix) {
        String traceId = getCurrentTraceId();
        return String.format("[%s][TraceId: %s] ", prefix, traceId);
    }
}
