package com.mi.info.intl.retail.model;

import java.io.Serializable;
import java.util.Map;

import org.slf4j.MDC;

import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.exception.BizException;
import com.xiaomi.youpin.infra.rpc.errors.ErrorCode;
import com.xiaomi.youpin.infra.rpc.errors.GeneralCodes;

import lombok.Getter;
import lombok.Setter;

/**
 * 常见API响应
 *
 * <AUTHOR>
 * @date 2025/08/12
 */
@Getter
@Setter
public class CommonApiResponse<T> implements Serializable {
    private static final long serialVersionUID = -153195758211395798L;

    private int code;

    private String message;

    private T data;

    private String traceId;

    /**
     * 附加字段,方便以后扩展
     */
    private Map<String, String> attachments;

    private static final String SUCCESS = "ok";

    public CommonApiResponse(T body) {
        this.code = 0;
        this.message = "ok";
        this.data = body;
        this.traceId = MDC.get(CommonConstant.MDC_TRACE_ID_KEY);
    }

    public CommonApiResponse(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
        this.traceId = MDC.get(CommonConstant.MDC_TRACE_ID_KEY);
    }

    public static <T> CommonApiResponse<T> failure(int code, String message) {
        return new CommonApiResponse<>(code, message, null);
    }

    public static <T> CommonApiResponse<T> failure(ErrorCode errorCode, String message) {
        return new CommonApiResponse<>(errorCode.getCode(), message, null);
    }

    public static <T> CommonApiResponse<T> failure(int code, String message, T data) {
        return new CommonApiResponse<>(code, message, data);
    }

    public static <T> CommonApiResponse<T> success(T data) {
        return new CommonApiResponse<>(data);
    }

    public static <T> CommonApiResponse<T> fromException(Throwable e) {
        if (e instanceof BizException) {
            return new CommonApiResponse<>(((BizException) e).getCode(), e.getMessage(), null);
        } else {
            return failure(GeneralCodes.InternalError.getCode(), "系统繁忙，请稍后再试");
        }
    }

    public static <T> CommonApiResponse<T> fromException(Throwable e, String message) {
        if (e instanceof BizException) {
            return new CommonApiResponse<>(((BizException) e).getCode(), message, null);
        } else {
            return failure(GeneralCodes.InternalError.getCode(), message);
        }
    }
}

