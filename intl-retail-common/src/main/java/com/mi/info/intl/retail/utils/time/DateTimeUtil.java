package com.mi.info.intl.retail.utils.time;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Date;

import org.apache.commons.lang3.StringUtils;

import com.mi.info.intl.retail.enums.AreaEnum;
import com.xiaomi.com.i18n.area.Area;

import lombok.extern.slf4j.Slf4j;

/**
 * 日期时间工具类 提供字符串与 Date 之间的转换
 */
@Slf4j
public class DateTimeUtil {

    // 私有构造方法，防止工具类被实例化
    private DateTimeUtil() {
        throw new UnsupportedOperationException("Utility class");
    }

    public static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    public static final DateTimeFormatter DATE_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter FORMATTER1 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ssXXX");

    // 字符串转 Date
    public static Date parseToDate(String timeStr) {
        LocalDateTime localDateTime = LocalDateTime.parse(timeStr, DATE_TIME_FORMATTER);
        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    // Date 转字符串（只保留日期部分）
    public static String format(Date date) {
        return DATE_FORMATTER.format(date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
    }

    /**
     * 将时间戳转换为指定格式的日期时间字符串
     * 
     * @param timestamp 时间戳（秒）
     * @return 格式化的日期时间字符串 yyyy-MM-dd HH:mm:ss
     */
    public static String formatTimestamp(Long timestamp) {
        if (timestamp == null) {
            return "";
        }
        // 直接使用秒级时间戳，不需要再乘以1000
        Instant instant = Instant.ofEpochSecond(timestamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.systemDefault());
        return DATE_TIME_FORMATTER.format(localDateTime);
    }

    /**
     * 将时间戳转换为Date对象
     * 
     * @param timestamp 时间戳（秒）
     * @return Date对象
     */
    public static Date timestampToDate(Long timestamp) {
        if (timestamp == null) {
            return null;
        }
        return new Date(timestamp * 1000L);
    }

    /**
     * 判断指定国家当前时间是否和指定的时间匹配
     *
     * @param countryCode 国家代码
     * @return 是否和传入的时间相匹配
     */
    public static LocalDateTime matchAM(String countryCode) {
        if (countryCode == null || countryCode.trim().isEmpty()) {
            return null;
        }

        try {
            // 获取国家对应的时区
            ZoneId zoneId = getZoneIdByCountryCode(countryCode);
            if (zoneId == null) {
                return null;
            }

            // 获取该时区的当前时间
            ZonedDateTime zonedDateTime = ZonedDateTime.now(zoneId);

            return zonedDateTime.toLocalDateTime();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 根据国家代码获取当前时间
     * 
     * @param countryCode
     * @return
     */
    public static LocalDateTime getLocalDateTimeByCountryCode(String countryCode) {
        try {
            if (countryCode == null || countryCode.isEmpty()) {
                return null;
            }
            Area area = Area.of(countryCode);
            if (area == null) {
                return null;
            }
            String timezone = area.getTimezone();
            if (timezone == null || timezone.isEmpty()) {
                return null;
            }
            // 获取当前的 OffsetDateTime
            OffsetDateTime offsetDateTime = OffsetDateTime.ofInstant(Instant.now(), ZoneId.of(timezone));
            // 使用日期格式化器格式化 OffsetDateTime，确保包含时区偏移（如 +02:00）
            String formattedDate = offsetDateTime.format(FORMATTER1);
            // 将格式化的日期字符串解析回 OffsetDateTime，使用带时区信息的解析器
            OffsetDateTime parsedOffsetDateTime = OffsetDateTime.parse(formattedDate, FORMATTER1);
            // 转换为 LocalDateTime
            return parsedOffsetDateTime.toLocalDateTime();
        } catch (Exception ex) {
            log.error("Error getting timezone for country code: {}", countryCode, ex);
        }
        return null;
    }

    /**
     * 获取本地日期时间排空
     *
     * @param timeZone 时区
     * @param timeStamp 时间戳记
     * @return {@link String }
     */
    public static String getLocalDateTimeByTimeZone(String timeZone, long timeStamp) {
        String timezone = getTimeZoneByCountryCode(timeZone);
        if (StringUtils.isEmpty(timezone)) {
            return String.valueOf(timeStamp);
        }
        // 获取当前的 OffsetDateTime
        Instant instant = Instant.ofEpochMilli(timeStamp);
        LocalDateTime localDateTime = LocalDateTime.ofInstant(instant, ZoneId.of(timezone));
        return localDateTime.format(DATE_TIME_FORMATTER);
    }

    /**
     * 按国家代码获取时区
     *
     * @param countryCode 国家代码
     * @return {@link String }
     */
    public static String getTimeZoneByCountryCode(String countryCode) {
        if (countryCode == null || countryCode.isEmpty()) {
            return null;
        }
        if (countryCode.contains("-")) {
            String[] languageParts = countryCode.split("-");
            countryCode = languageParts[1];
        }
        Area area = Area.of(countryCode);
        if (area == null) {
            area = Area.of(AreaEnum.CN.getLocal());
        }
        return area.getTimezone();
    }

    /**
     * 根据国家代码获取对应的时区
     * 
     * @param countryCode 国家代码
     * @return 时区ID
     */
    private static ZoneId getZoneIdByCountryCode(String countryCode) {
        // 这里需要根据实际的国家代码与时区的映射关系来实现
        // 示例映射关系
        switch (countryCode.toUpperCase()) {
            case "IN": // 印度
                return ZoneId.of("Asia/Kolkata");
            case "ID": // 印尼
                return ZoneId.of("Asia/Jakarta");
            case "MY": // 马来西亚
                return ZoneId.of("Asia/Kuala_Lumpur");
            case "PH": // 菲律宾
                return ZoneId.of("Asia/Manila");
            case "TH": // 泰国
                return ZoneId.of("Asia/Bangkok");
            case "VN": // 越南
                return ZoneId.of("Asia/Ho_Chi_Minh");
            case "SG": // 新加坡
                return ZoneId.of("Asia/Singapore");
            default:
                return null;
        }
    }

    /**
     * 将 LocalDateTime 转换为时间戳（秒）
     * 
     * @param localDateTime LocalDateTime 对象
     * @return 时间戳（秒）
     */
    public static Long toTimestamp(LocalDateTime localDateTime) {
        if (localDateTime == null) {
            return null;
        }
        return localDateTime.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    /**
     * 将 Date 转换为 yyyy/MM/dd 格式的字符串
     * 
     * @param date Date 对象
     * @return 格式化后的字符串 yyyy/MM/dd
     */
    public static String formatDateToYMD(Date date) {
        if (date == null) {
            return "";
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy/MM/dd");
        LocalDate localDate = date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        return formatter.format(localDate);
    }

    /**
     * 将 LocalDateTime 格式化为 yyyy-MM-dd HH:mm:ss，时分秒可自定义传入（如00:00:00、23:59:59）
     * 
     * @param localDateTime LocalDateTime 对象
     * @param timeStr 时分秒字符串，格式为HH:mm:ss
     * @return 格式化后的字符串 yyyy-MM-dd HH:mm:ss
     */
    public static String formatLocalDateTimeWithCustomTime(LocalDateTime localDateTime, String timeStr) {
        if (localDateTime == null) {
            return "";
        }
        String datePart = localDateTime.toLocalDate().format(DateTimeFormatter.ofPattern("yyyy-MM-dd"));
        String timePart = timeStr;
        if (timeStr == null || timeStr.trim().isEmpty()) {
            timePart = localDateTime.toLocalTime().format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        }
        return datePart + " " + timePart;
    }

    /**
     * 将字符串转换为 Date 对象
     * 
     * @param dateTimeStr 时间字符串
     * @return Date 对象
     */
    public static Date parseToDateTime(String dateTimeStr) {
        try {
            return new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse(dateTimeStr);
        } catch (ParseException e) {
            log.error("Error parsing offsetDateTime: " + e);
        }
        return null;
    }

}
