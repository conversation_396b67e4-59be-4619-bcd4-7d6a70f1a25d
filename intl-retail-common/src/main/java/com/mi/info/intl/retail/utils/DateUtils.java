package com.mi.info.intl.retail.utils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;

public class DateUtils {

    /**
     * 获取某个月的第一天的最早时间（00:00:00）
     * @param dateStr 输入日期（格式：yyyy-MM-dd）
     * @return 该月第一天的 00:00:00（Date 类型）
     */
    public static Date getStartOfMonth(String dateStr) {
        // 解析输入日期（格式：yyyy-MM-dd）
        LocalDate date = LocalDate.parse(dateStr);

        // 获取该月的第一天
        LocalDate firstDayOfMonth = date.withDayOfMonth(1);

        // 设置时间为 00:00:00
        LocalDateTime startOfMonth = firstDayOfMonth.atTime(0, 0, 0);

        // 转换为 Date 类型
        return Date.from(startOfMonth.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取某个月的最后一天的最晚时间（23:59:59）
     * @param dateStr 输入日期（格式：yyyy-MM-dd）
     * @return 该月最后一天的 23:59:59（Date 类型）
     */
    public static Date getEndOfMonth(String dateStr) {
        // 解析输入日期（格式：yyyy-MM-dd）
        LocalDate date = LocalDate.parse(dateStr);

        // 获取该月的最后一天
        LocalDate lastDayOfMonth = date.withDayOfMonth(date.lengthOfMonth());

        // 设置时间为 23:59:59
        LocalDateTime endOfMonth = lastDayOfMonth.atTime(23, 59, 59);

        // 转换为 Date 类型（使用系统默认时区）
        return Date.from(endOfMonth.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当天的开始时间（00:00:00）
     * @param dateStr 输入日期（格式：yyyy-MM-dd）
     * @return 当天的 00:00:00（Date 类型）
     */
    public static Date getStartOfDay(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr); // 默认解析 yyyy-MM-dd
        LocalDateTime startOfDay = date.atTime(0, 0, 0);
        return Date.from(startOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取当天的结束时间（23:59:59）
     * @param dateStr 输入日期（格式：yyyy-MM-dd）
     * @return 当天的 23:59:59（Date 类型）
     */
    public static Date getEndOfDay(String dateStr) {
        LocalDate date = LocalDate.parse(dateStr);
        LocalDateTime endOfDay = date.atTime(23, 59, 59);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

}
