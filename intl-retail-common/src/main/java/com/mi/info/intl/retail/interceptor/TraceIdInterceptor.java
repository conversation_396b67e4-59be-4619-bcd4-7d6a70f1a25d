package com.mi.info.intl.retail.interceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.springframework.web.servlet.HandlerInterceptor;

import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.utils.TraceIdUtil;

import lombok.extern.slf4j.Slf4j;

/**
 * TraceId 拦截器 自动处理 HTTP 请求的 traceId，支持链路追踪
 * 
 * <AUTHOR>
 * @date 2024/12/19
 */
@Slf4j
public class TraceIdInterceptor implements HandlerInterceptor {

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
        throws Exception {
        // 从请求头获取 traceId
        String traceId = request.getHeader(CommonConstant.TRACE_ID_HEADER);

        // 如果没有从请求头获取到，则生成新的
        if (traceId == null || traceId.isEmpty()) {
            traceId = TraceIdUtil.generateTraceId();
            log.debug("Generated new traceId: {} for request: {}", traceId, request.getRequestURI());
        } else {
            log.debug("Received traceId: {} from request: {}", traceId, request.getRequestURI());
        }

        // 设置 traceId
        TraceIdUtil.setTraceIdToMDC(traceId);

        // 将 traceId 添加到响应头中
        response.addHeader(CommonConstant.TRACE_ID_HEADER, traceId);

        log.debug("Request started - URI: {}, Method: {}, TraceId: {}", request.getRequestURI(), request.getMethod(),
            traceId);

        return true;
    }

    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex)
        throws Exception {
        String traceId = TraceIdUtil.getCurrentTraceId();

        if (ex != null) {
            log.error("Request completed with error - URI: {}, Method: {}, TraceId: {}, Error: {}",
                request.getRequestURI(), request.getMethod(), traceId, ex.getMessage());
        } else {
            log.debug("Request completed - URI: {}, Method: {}, TraceId: {}, Status: {}", request.getRequestURI(),
                request.getMethod(), traceId, response.getStatus());
        }

        // 清理 traceId
        TraceIdUtil.removeTraceIdFromMDC();
    }
}
