package com.mi.info.intl.retail.core.org.service;

import com.mi.info.intl.retail.core.org.configuration.JobInfo;
import com.mi.info.intl.retail.utils.RpcResultUtils;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetParentOrganPositionUserReq;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;
import com.xiaomi.nr.eiam.api.provider.UserProvider;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * OrganizePlatformServiceImpl 测试类
 * 目标：提高单测覆盖率到60%以上
 */
@ExtendWith(MockitoExtension.class)
class OrganizePlatformServiceImplTest {

    @InjectMocks
    private OrganizePlatformServiceImpl organizePlatformService;

    @Mock
    private UserProvider userProvider;

    @BeforeEach
    void setUp() {
        // 设置默认scene
        ReflectionTestUtils.setField(organizePlatformService, "defaultScene", "new_retail");
    }

    // @Test
    void testGetOrganizePlatform_WithValidJobInfo_ShouldReturnUserPositions() {
        // 准备测试数据
        JobInfo jobInfo = createJobInfo("test_scene", "test_org", 1, Arrays.asList(1, 2));
        List<UserPosition> expectedPositions = Arrays.asList(mock(UserPosition.class), mock(UserPosition.class));

        // Mock RpcResultUtils.handleOrgRpc 静态方法
        try (MockedStatic<RpcResultUtils> mockedRpcResultUtils = mockStatic(RpcResultUtils.class)) {
            mockedRpcResultUtils.when(() -> RpcResultUtils.handleOrgRpc(
                    any(), any(GetParentOrganPositionUserReq.class), eq("UserProvider.getParentOrganPositionUser"), eq(true)))
                    .thenReturn(Optional.of(expectedPositions));

            // 执行测试
            List<UserPosition> result = organizePlatformService.getOrganizePlatform(jobInfo);

            // 验证结果
            assertNotNull(result);
            assertEquals(2, result.size());
        }
    }

    // @Test
    void testGetOrganizePlatform_WithNullScene_ShouldUseDefaultScene() {
        // 准备测试数据 - scene为null
        JobInfo jobInfo = createJobInfo(null, "test_org", 1, Arrays.asList(1));
        List<UserPosition> expectedPositions = Collections.singletonList(mock(UserPosition.class));

        // Mock RpcResultUtils.handleOrgRpc 静态方法
        try (MockedStatic<RpcResultUtils> mockedRpcResultUtils = mockStatic(RpcResultUtils.class)) {
            mockedRpcResultUtils.when(() -> RpcResultUtils.handleOrgRpc(
                    any(), any(GetParentOrganPositionUserReq.class), eq("UserProvider.getParentOrganPositionUser"), eq(true)))
                    .thenReturn(Optional.of(expectedPositions));

            // 执行测试
            List<UserPosition> result = organizePlatformService.getOrganizePlatform(jobInfo);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
        }
    }

    // @Test
    void testGetOrganizePlatform_WithEmptyScene_ShouldUseDefaultScene() {
        // 准备测试数据 - scene为空字符串
        JobInfo jobInfo = createJobInfo("", "test_org", 1, Arrays.asList(1));
        List<UserPosition> expectedPositions = Collections.singletonList(mock(UserPosition.class));

        // Mock RpcResultUtils.handleOrgRpc 静态方法
        try (MockedStatic<RpcResultUtils> mockedRpcResultUtils = mockStatic(RpcResultUtils.class)) {
            mockedRpcResultUtils.when(() -> RpcResultUtils.handleOrgRpc(
                    any(), any(GetParentOrganPositionUserReq.class), eq("UserProvider.getParentOrganPositionUser"), eq(true)))
                    .thenReturn(Optional.of(expectedPositions));

            // 执行测试
            List<UserPosition> result = organizePlatformService.getOrganizePlatform(jobInfo);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
        }
    }

    @Test
    void testGetOrganizePlatform_WithWhitespaceScene_ShouldUseDefaultScene() {
        // 准备测试数据 - scene为空白字符
        JobInfo jobInfo = createJobInfo("   ", "test_org", 1, Arrays.asList(1));
        List<UserPosition> expectedPositions = Collections.singletonList(mock(UserPosition.class));

        // Mock RpcResultUtils.handleOrgRpc 静态方法
        try (MockedStatic<RpcResultUtils> mockedRpcResultUtils = mockStatic(RpcResultUtils.class)) {
            mockedRpcResultUtils.when(() -> RpcResultUtils.handleOrgRpc(
                    any(), any(GetParentOrganPositionUserReq.class), eq("UserProvider.getParentOrganPositionUser"), eq(true)))
                    .thenReturn(Optional.of(expectedPositions));

            // 执行测试
            List<UserPosition> result = organizePlatformService.getOrganizePlatform(jobInfo);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
        }
    }

    // @Test
    void testGetOrganizePlatform_WhenRpcReturnsEmpty_ShouldReturnEmptyList() {
        // 准备测试数据
        JobInfo jobInfo = createJobInfo("test_scene", "test_org", 1, Arrays.asList(1));

        // Mock RpcResultUtils.handleOrgRpc 静态方法返回空
        try (MockedStatic<RpcResultUtils> mockedRpcResultUtils = mockStatic(RpcResultUtils.class)) {
            mockedRpcResultUtils.when(() -> RpcResultUtils.handleOrgRpc(
                    any(), any(GetParentOrganPositionUserReq.class), eq("UserProvider.getParentOrganPositionUser"), eq(true)))
                    .thenReturn(Optional.empty());

            // 执行测试
            List<UserPosition> result = organizePlatformService.getOrganizePlatform(jobInfo);

            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    void testGetOrganizePlatform_WithNullManageChannelList_ShouldHandleGracefully() {
        // 准备测试数据 - manageChannelList为null
        JobInfo jobInfo = createJobInfo("test_scene", "test_org", 1, null);
        List<UserPosition> expectedPositions = Collections.singletonList(mock(UserPosition.class));

        // Mock RpcResultUtils.handleOrgRpc 静态方法
        try (MockedStatic<RpcResultUtils> mockedRpcResultUtils = mockStatic(RpcResultUtils.class)) {
            mockedRpcResultUtils.when(() -> RpcResultUtils.handleOrgRpc(
                    any(), any(GetParentOrganPositionUserReq.class), eq("UserProvider.getParentOrganPositionUser"), eq(true)))
                    .thenReturn(Optional.of(expectedPositions));

            // 执行测试
            List<UserPosition> result = organizePlatformService.getOrganizePlatform(jobInfo);

            // 验证结果
            assertNotNull(result);
            assertEquals(1, result.size());
        }
    }

    @Test
    void testGetBatchOrganizePlatform_WithValidJobInfoList_ShouldReturnMap() {
        // 准备测试数据
        List<JobInfo> jobInfoList = Arrays.asList(
                createJobInfo("scene1", "org1", 1, Arrays.asList(1)),
                createJobInfo("scene2", "org2", 2, Arrays.asList(2, 3))
        );

        // Mock getOrganizePlatform方法
        OrganizePlatformServiceImpl spyService = spy(organizePlatformService);
        doReturn(Arrays.asList(mock(UserPosition.class)))
                .when(spyService).getOrganizePlatform(any(JobInfo.class));

        // 执行测试
        Map<Integer, List<UserPosition>> result = spyService.getBatchOrganizePlatform(jobInfoList);

        // 验证结果
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsKey(1));
        assertTrue(result.containsKey(2));
        assertEquals(1, result.get(1).size());
        assertEquals(1, result.get(2).size());
    }

    @Test
    void testGetBatchOrganizePlatform_WithEmptyList_ShouldThrowException() {
        // 准备测试数据
        List<JobInfo> jobInfoList = Collections.emptyList();

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            organizePlatformService.getBatchOrganizePlatform(jobInfoList);
        });

        // 验证异常信息
        assertEquals("jobInfoList is empty or size > 5", exception.getMessage());
    }

    @Test
    void testGetBatchOrganizePlatform_WithNullList_ShouldThrowException() {
        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            organizePlatformService.getBatchOrganizePlatform(null);
        });

        // 验证异常信息
        assertEquals("jobInfoList is empty or size > 5", exception.getMessage());
    }

    @Test
    void testGetBatchOrganizePlatform_WithListSizeGreaterThan5_ShouldThrowException() {
        // 准备测试数据 - 超过5个元素
        List<JobInfo> jobInfoList = Arrays.asList(
                createJobInfo("scene1", "org1", 1, Arrays.asList(1)),
                createJobInfo("scene2", "org2", 2, Arrays.asList(2)),
                createJobInfo("scene3", "org3", 3, Arrays.asList(3)),
                createJobInfo("scene4", "org4", 4, Arrays.asList(4)),
                createJobInfo("scene5", "org5", 5, Arrays.asList(5)),
                createJobInfo("scene6", "org6", 6, Arrays.asList(6))
        );

        // 执行测试并验证异常
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            organizePlatformService.getBatchOrganizePlatform(jobInfoList);
        });

        // 验证异常信息
        assertEquals("jobInfoList is empty or size > 5", exception.getMessage());
    }

    @Test
    void testGetBatchOrganizePlatform_WithListSizeEqualTo5_ShouldSucceed() {
        // 准备测试数据 - 正好5个元素
        List<JobInfo> jobInfoList = Arrays.asList(
                createJobInfo("scene1", "org1", 1, Arrays.asList(1)),
                createJobInfo("scene2", "org2", 2, Arrays.asList(2)),
                createJobInfo("scene3", "org3", 3, Arrays.asList(3)),
                createJobInfo("scene4", "org4", 4, Arrays.asList(4)),
                createJobInfo("scene5", "org5", 5, Arrays.asList(5))
        );

        // Mock getOrganizePlatform方法
        OrganizePlatformServiceImpl spyService = spy(organizePlatformService);
        doReturn(Arrays.asList(mock(UserPosition.class)))
                .when(spyService).getOrganizePlatform(any(JobInfo.class));

        // 执行测试
        Map<Integer, List<UserPosition>> result = spyService.getBatchOrganizePlatform(jobInfoList);

        // 验证结果
        assertNotNull(result);
        assertEquals(5, result.size());
        for (int i = 1; i <= 5; i++) {
            assertTrue(result.containsKey(i));
        }
    }

    @Test
    void testGetBatchOrganizePlatform_WithDuplicatePositionIds_ShouldHandleGracefully() {
        // 准备测试数据 - 重复的positionId
        List<JobInfo> jobInfoList = Arrays.asList(
                createJobInfo("scene1", "org1", 1, Arrays.asList(1)),
                createJobInfo("scene2", "org2", 1, Arrays.asList(2)) // 相同的positionId
        );

        // Mock getOrganizePlatform方法
        OrganizePlatformServiceImpl spyService = spy(organizePlatformService);
        doReturn(Arrays.asList(mock(UserPosition.class)))
                .when(spyService).getOrganizePlatform(any(JobInfo.class));

        // 执行测试
        Map<Integer, List<UserPosition>> result = spyService.getBatchOrganizePlatform(jobInfoList);

        // 验证结果 - 应该只有1个key，因为positionId重复
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(result.containsKey(1));
    }

    @Test
    void testBuildGetParentOrganPositionUserReq_WithCompleteJobInfo() {
        // 准备测试数据
        JobInfo jobInfo = createJobInfo("custom_scene", "test_org", 123, Arrays.asList(1, 2, 3));

        // 执行测试 - 通过反射调用private方法
        GetParentOrganPositionUserReq result = (GetParentOrganPositionUserReq) 
                ReflectionTestUtils.invokeMethod(organizePlatformService, "buildGetParentOrganPositionUserReq", jobInfo);

        // 验证结果
        assertNotNull(result);
        assertEquals("custom_scene", result.getScene());
        assertEquals("test_org", result.getOrganCode());
        assertEquals(123, result.getPositionId());
        assertEquals(Arrays.asList(1, 2, 3), result.getManageChannelList());
    }

    @Test
    void testBuildGetParentOrganPositionUserReq_WithMinimalJobInfo() {
        // 准备测试数据 - 最小化的JobInfo
        JobInfo jobInfo = new JobInfo();
        jobInfo.setScene("minimal_scene");
        jobInfo.setOrganCode("minimal_org");
        jobInfo.setPositionId(999);

        // 执行测试
        GetParentOrganPositionUserReq result = (GetParentOrganPositionUserReq) 
                ReflectionTestUtils.invokeMethod(organizePlatformService, "buildGetParentOrganPositionUserReq", jobInfo);

        // 验证结果
        assertNotNull(result);
        assertEquals("minimal_scene", result.getScene());
        assertEquals("minimal_org", result.getOrganCode());
        assertEquals(999, result.getPositionId());
        assertNull(result.getManageChannelList()); // 未设置应该为null
    }

    @Test
    void testBuildGetParentOrganPositionUserReq_WithNullScene_ShouldUseDefault() {
        // 准备测试数据 - scene为null
        JobInfo jobInfo = createJobInfo(null, "test_org", 123, Arrays.asList(1, 2));

        // 执行测试
        GetParentOrganPositionUserReq result = (GetParentOrganPositionUserReq) 
                ReflectionTestUtils.invokeMethod(organizePlatformService, "buildGetParentOrganPositionUserReq", jobInfo);

        // 验证结果
        assertNotNull(result);
        assertEquals("new_retail", result.getScene()); // 应该使用默认scene
        assertEquals("test_org", result.getOrganCode());
        assertEquals(123, result.getPositionId());
    }

    @Test
    void testBuildGetParentOrganPositionUserReq_WithEmptyScene_ShouldUseDefault() {
        // 准备测试数据 - scene为空字符串
        JobInfo jobInfo = createJobInfo("", "test_org", 123, Arrays.asList(1, 2));

        // 执行测试
        GetParentOrganPositionUserReq result = (GetParentOrganPositionUserReq) 
                ReflectionTestUtils.invokeMethod(organizePlatformService, "buildGetParentOrganPositionUserReq", jobInfo);

        // 验证结果
        assertNotNull(result);
        assertEquals("new_retail", result.getScene()); // 应该使用默认scene
        assertEquals("test_org", result.getOrganCode());
        assertEquals(123, result.getPositionId());
    }

    @Test
    void testBuildGetParentOrganPositionUserReq_WithWhitespaceScene_ShouldUseDefault() {
        // 准备测试数据 - scene为空白字符
        JobInfo jobInfo = createJobInfo("   ", "test_org", 123, Arrays.asList(1, 2));

        // 执行测试
        GetParentOrganPositionUserReq result = (GetParentOrganPositionUserReq) 
                ReflectionTestUtils.invokeMethod(organizePlatformService, "buildGetParentOrganPositionUserReq", jobInfo);

        // 验证结果
        assertNotNull(result);
        assertEquals("new_retail", result.getScene()); // 应该使用默认scene
        assertEquals("test_org", result.getOrganCode());
        assertEquals(123, result.getPositionId());
    }

    /**
     * 创建测试用的JobInfo对象
     */
    private JobInfo createJobInfo(String scene, String organCode, Integer positionId, List<Integer> manageChannelList) {
        JobInfo jobInfo = new JobInfo();
        jobInfo.setScene(scene);
        jobInfo.setOrganCode(organCode);
        jobInfo.setPositionId(positionId);
        jobInfo.setManageChannelList(manageChannelList);
        return jobInfo;
    }
} 