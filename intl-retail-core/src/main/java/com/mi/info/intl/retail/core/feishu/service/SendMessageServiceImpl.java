package com.mi.info.intl.retail.core.feishu.service;

import java.util.List;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.core.feishu.bean.config.FeiShuConfiguration;
import com.mi.info.intl.retail.core.feishu.bean.message.SendMessageReq;
import com.mi.info.intl.retail.core.feishu.bean.message.SendMessageResp;
import com.mi.info.intl.retail.core.feishu.bean.token.GetFeiShuTokenReq;
import com.mi.info.intl.retail.core.feishu.bean.token.GetFeiShuTokenResp;
import com.mi.info.intl.retail.core.feishu.bean.user.GetUserInfoListReq;
import com.mi.info.intl.retail.core.feishu.bean.user.GetUserInfoListResp;
import com.mi.info.intl.retail.core.org.configuration.JobInfo;
import com.mi.info.intl.retail.core.org.configuration.OrganizationPlatformConf;
import com.mi.info.intl.retail.core.org.service.OrganizePlatformService;
import com.mi.info.intl.retail.http.HttpClient;
import com.xiaomi.nr.eiam.admin.vo.provider.user.UserSensitiveInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.UserPosition;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class SendMessageServiceImpl implements SendMessageService {

    @Resource
    private FeiShuConfiguration feiShuConfiguration;

    @Resource
    private OrganizationPlatformConf organizationPlatformConf;

    @Resource
    private OrganizePlatformService organizePlatformService;

    @Override
    public void sendTextMessage(String message) {
        GetFeiShuTokenResp tokenResp = getToken();
        List<GetUserInfoListResp.DataDTO.UserListDTO> userList = getUserInfoList(tokenResp.getTenantAccessToken());
        sendMessage(userList, tokenResp.getTenantAccessToken(), message);
    }

    public GetFeiShuTokenResp getToken() {
        GetFeiShuTokenReq req = GetFeiShuTokenReq.builder().appId(feiShuConfiguration.getAppId())
            .appSecret(feiShuConfiguration.getAppSecret()).build();
        return HttpClient.textBody(feiShuConfiguration.getTokenUrl()).json(req).asBean(GetFeiShuTokenResp.class);
    }

    /**
     * 获取用户信息列表
     *
     * @param token 令牌
     * @return {@link List }<{@link String }>
     */
    public List<GetUserInfoListResp.DataDTO.UserListDTO> getUserInfoList(String token) {
        // 获取国家策略运营
        JobInfo info = organizationPlatformConf.getJobInfoCopy("256");
        List<UserPosition> list = organizePlatformService.getOrganizePlatform(info);
        List<UserSensitiveInfoResp> userInfoRespList = organizePlatformService.getBatchUserInfo(
            list.stream().map(UserPosition::getMiId).collect(Collectors.toList()),
            info.getScene() == null ? organizationPlatformConf.getScene() : info.getScene());
        if (CollectionUtils.isEmpty(userInfoRespList)) {
            return Lists.newArrayList();
        }
        GetUserInfoListReq userInfoListReq = new GetUserInfoListReq();
        userInfoListReq
            .setEmails(userInfoRespList.stream().map(UserSensitiveInfoResp::getEmail).collect(Collectors.toList()));

        GetUserInfoListResp userInfoListResp = HttpClient.textBody(feiShuConfiguration.getGetUnionidsUrl())
            .header("Authorization", "Bearer " + token).json(userInfoListReq).asBean(GetUserInfoListResp.class);

        if (userInfoListResp.getCode() != 0 || CollectionUtils.isEmpty(userInfoListResp.getData().getUserList())) {
            return Lists.newArrayList();
        }
        return userInfoListResp.getData().getUserList();
    }

    /**
     * 发送消息
     *
     * @param list 列表
     * @param token 令牌
     * @param message 信息
     */
    public void sendMessage(List<GetUserInfoListResp.DataDTO.UserListDTO> list, String token, String message) {
        if (CollectionUtils.isEmpty(list)) {
            log.warn("message send fail, user list is empty");
            return;
        }
        SendMessageReq messageReq = new SendMessageReq();
        SendMessageReq.TextContent content = new SendMessageReq.TextContent();
        content.setText(message);
        messageReq.setContent(JSON.toJSONString(content));
        messageReq.setMsgType("text");
        for (GetUserInfoListResp.DataDTO.UserListDTO user : list) {
            String userId = user.getUserId();
            messageReq.setReceiveId(userId);
            SendMessageResp resp =
                HttpClient.textBody(feiShuConfiguration.getMessageUrl()).header("Authorization", "Bearer " + token)
                    .queryString("receive_id_type", "open_id").json(messageReq).asBean(SendMessageResp.class);
            log.info("send message to userId:{}, resp:{}", userId, resp);

        }
    }

}
