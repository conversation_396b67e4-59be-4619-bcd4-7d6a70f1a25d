package com.mi.info.intl.retail.core.utils;

import com.mi.info.intl.retail.core.exception.RetailRunTimeException;
import lombok.experimental.UtilityClass;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Assert;

@UtilityClass
public class IntlRetailAssert extends Assert {

    public <T> T nonNull(T reference, String errorMessage) {
        checkArgument(reference != null, errorMessage);
        return reference;
    }

    public String notEmpty(String reference, String errorMessage) {
        checkArgument(StringUtils.isNotEmpty(reference), errorMessage);
        return reference;
    }

    public void checkArgument(boolean expression, String errorMessage) {
        if (!expression) {
            throw new RetailRunTimeException(errorMessage);
        }
    }

}
