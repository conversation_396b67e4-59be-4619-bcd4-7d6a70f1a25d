<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.retailer.mapper.read.IntlRmsRetailerReadMapper">

    <select id="queryRetailerByNameOrCode"
            resultType="com.mi.info.intl.retail.intlretail.service.api.ldu.dto.SearchRetailerResponseDto">
        SELECT
        irct.name as code,
        irct.retailer_name as name,
        irct2.country_code
        FROM
        intl_rms_retailer irct
        INNER JOIN
        intl_rms_country_timezone irct2 ON irct.country_id = irct2.country_id
        WHERE
        <choose>
            <when test="params.id != null and !params.id.isEmpty()">
                LOWER(irct.name) = LOWER(#{params.id})
            </when>
            <otherwise>
                (
                LOWER(irct.name) LIKE CONCAT('%', LOWER(#{params.keyword}), '%')
                OR LOWER(irct.retailer_name) LIKE CONCAT('%', LOWER(#{params.keyword}), '%')
                )
            </otherwise>
        </choose>
        AND irct2.country_code != 'CN'
        <if test="params.countryCode != null and !params.countryCode.isEmpty()">
            AND irct2.country_code IN
            <foreach collection="params.countryCode" item="codes" open="(" separator="," close=")">
                #{codes}
            </foreach>
        </if>
        LIMIT #{limit}
    </select>
</mapper>
