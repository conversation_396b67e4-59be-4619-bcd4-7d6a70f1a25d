package com.mi.info.intl.retail.user.app.impl;

import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataInputRequest;
import com.mi.info.intl.retail.intlretail.service.api.store.dto.BusinessDataResponse;
import com.mi.info.intl.retail.user.app.UserService;
import com.mi.info.intl.retail.user.domain.UserInfoManager;
import com.mi.info.intl.retail.user.infra.entity.IntlRmsUser;
import com.mi.info.intl.retail.user.infra.mapper.IntlRmsUserMapper;
import com.mi.info.intl.retail.utils.redis.RedisKey;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import com.mi.info.intl.retail.utils.redis.RedisClient;
import lombok.extern.slf4j.Slf4j;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import javax.annotation.Resource;

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = UserService.class)
public class UserServiceImpl implements UserService, UserApiService {
    @Autowired
    private IntlRmsUserMapper intlRmsUserMapper;
    @Autowired
    private UserInfoManager userInfoManager;
    @Resource
    private RedisClient redisClient;

    @Override
    public IntlRmsUser getUserInfo(String email) {
        if (StringUtils.isEmpty(email)) {
            return new IntlRmsUser();
        }
        return intlRmsUserMapper.selectByEmail(email);
    }

    @Override
    public IntlRmsUser getUserInfoDomainName(String domainName) {
        return intlRmsUserMapper.selectByDomainName(domainName);
    }

    @Override
    public List<BusinessDataResponse> getUserPositions(BusinessDataInputRequest userInfoRequest) {

        return userInfoManager.getUserPositions(userInfoRequest);
    }

    @Override
    public Optional<IntlRmsUserNewDto> getUserByMiId(Long miId) {
        if (ObjectUtils.isEmpty(miId)) {
            log.error("getUserByMiId param:mid is null");
            return Optional.empty();
        }
        //todo 优化，从redis查先
        LambdaQueryWrapper<IntlRmsUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.eq(IntlRmsUser::getMiId, miId);
        IntlRmsUser intlRmsUser = intlRmsUserMapper.selectOne(queryWrapper);
        if (ObjectUtils.isEmpty(intlRmsUser)) {
            log.error("getUserByMiId can not find user by miid:{}", miId);
            return Optional.empty();
        }
        IntlRmsUserNewDto intlRmsUserNewDto = new IntlRmsUserNewDto();
        ComponentLocator.getConverter().convert(intlRmsUser, intlRmsUserNewDto);
        return Optional.of(intlRmsUserNewDto);
    }

    /**
     * @param miIds
     * @return
     */
    @Override
    public Optional<List<IntlRmsUserNewDto>> getUserListByMiIds(List<Long> miIds) {
        if (CollectionUtils.isEmpty(miIds)) {
            log.error("getUserListByMiIds param:miIds is empty");
            throw new BizException("miId is empty");
        }
        if (miIds.size() > 500) {
            throw new BizException("miIds size is more than 500");
        }

        LambdaQueryWrapper<IntlRmsUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(IntlRmsUser::getMiId, miIds);
        List<IntlRmsUser> intlRmsUsers = intlRmsUserMapper.selectList(queryWrapper);
        if (CollectionUtils.isEmpty(intlRmsUsers)) {
            return Optional.empty();
        }
        List<IntlRmsUserNewDto> intlRmsUserNewDtos =
                JacksonUtil.parseArray(JacksonUtil.toStr(intlRmsUsers), IntlRmsUserNewDto.class);
        return Optional.of(intlRmsUserNewDtos);
    }

    @Override
    public Optional<UserInfoDTO> queryUserByMiId(Long miId) {
        log.info("查询用户信息, miId: {}", miId);

        try {
            IntlRmsUser user = intlRmsUserMapper.selectByMiId(miId);

            if (user != null) {
                log.info("查询用户信息成功, miId: {}, domainName: {}", miId, user.getDomainName());
                return Optional.of(convertToUserInfo(user));
            } else {
                log.warn("未找到用户信息, miId: {}", miId);
                return Optional.empty();
            }

        } catch (Exception e) {
            log.error("查询用户信息失败, miId: {}", miId, e);
            return Optional.empty();
        }
    }

    @Override
    public List<UserInfoDTO> getUserByName(String userName) {

        if (StringUtils.isNotEmpty(userName)) {
            log.info("查询用户信息, userName: {}", userName);
            List<IntlRmsUser> intlRmsUsers = intlRmsUserMapper.selectList(Wrappers.<IntlRmsUser>lambdaQuery()
                    .like(IntlRmsUser::getEnglishName, userName));
            if (CollectionUtils.isNotEmpty(intlRmsUsers)) {
                return intlRmsUsers.stream().map(this::convertToUserInfo).collect(Collectors.toList());
            }
        }

        List<IntlRmsUser> intlRmsUsers = intlRmsUserMapper.selectList(null);

        return intlRmsUsers.stream().map(this::convertToUserInfo).collect(Collectors.toList());

    }

    @Override
    public List<UserInfoDTO> getUserListByRmsIds(List<String> rmsUserIdList) {
        if (CollectionUtils.isEmpty(rmsUserIdList)) {
            log.error("getUserListByRmsIds param:rmsIds is empty");
            throw new BizException("rmsIds is empty");
        }
        if (rmsUserIdList.size() > 1000) {
            throw new BizException("rmsIds size is more than 500");
        }

        // 过滤掉空值
        List<String> validRmsIds = rmsUserIdList.stream()
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.toList());

        if (validRmsIds.isEmpty()) {
            return Collections.emptyList();
        }

        try {
            // 先从Redis Hash中批量获取
            List<UserInfoDTO> cachedUsers = getCachedUsersFromHash(validRmsIds);

            // 分离已缓存和未缓存的rmsIds
            Map<String, UserInfoDTO> cachedMap = cachedUsers.stream()
                    .filter(user -> user != null && StringUtils.isNotBlank(user.getRmsId()))
                    .collect(Collectors.toMap(UserInfoDTO::getRmsId, user -> user, (oldVal, newVal) -> newVal));

            List<String> uncachedRmsIds = validRmsIds.stream()
                    .filter(rmsId -> !cachedMap.containsKey(rmsId))
                    .collect(Collectors.toList());

            List<UserInfoDTO> result = new ArrayList<>(cachedMap.values());

            // 如果还有未缓存的数据，则查询数据库
            if (!uncachedRmsIds.isEmpty()) {
                List<UserInfoDTO> dbUsers = getUsersFromDatabase(uncachedRmsIds);
                // 将数据库查询结果同步到Redis Hash
                cacheUsersToHash(dbUsers);
                result.addAll(dbUsers);
            }

            return result;
        } catch (Exception e) {
            log.error("getUserListByRmsIds failed, rmsIds: {}", validRmsIds, e);
            // 出现异常时直接查询数据库，保证服务可用性
            return getUsersFromDatabase(validRmsIds);
        }
    }

    /**
     * 从Redis Hash中获取用户信息
     */
    private List<UserInfoDTO> getCachedUsersFromHash(List<String> rmsIds) {
        List<UserInfoDTO> result = new ArrayList<>();
        try {
            RedisKey redisKey = RedisKeyEnum.RMS_USER_MID_CACHE.get();
            // 仅获取需要的 rmsId 对应的 miId
            Map<Object, Object> cachedValues = redisClient.hmget(redisKey);
            if (cachedValues == null) {
                log.warn("Redis returned null when fetching user cache.");
                return Collections.emptyList();
            }

            Set<Object> rmsIdsCache = cachedValues.keySet();
            List<String> cacheExistRmsIds =
                    rmsIds.stream().filter(e -> rmsIdsCache.contains(e)).collect(Collectors.toList());

            cacheExistRmsIds.forEach(e -> {
                Object o = cachedValues.get(e);
                if (o != null) {
                    UserInfoDTO userInfoDTO = new UserInfoDTO();
                    userInfoDTO.setRmsId(e);
                    userInfoDTO.setMiId(Long.parseLong(o.toString()));
                    result.add(userInfoDTO);
                }
            });

            return result;
        } catch (Exception e) {
            log.warn("Failed to get users from cache, fallback to database", e);
            return Collections.emptyList();
        }
    }

    /**
     * 将用户信息缓存到Redis Hash
     */
    private void cacheUsersToHash(List<UserInfoDTO> users) {
        try {
            RedisKey redisKey = RedisKeyEnum.RMS_USER_MID_CACHE.get();
            // 构造Hash数据，field为rmsId，value为miId
            Map<String, Object> hashData = users.stream()
                    .filter(user -> user != null &&
                            StringUtils.isNotBlank(user.getRmsId()) &&
                            user.getMiId() != null)
                    .collect(Collectors.toMap(
                            UserInfoDTO::getRmsId,
                            user -> String.valueOf(user.getMiId())
                    ));

            if (!hashData.isEmpty()) {
                // 批量存储到Hash中
                redisClient.hmset(redisKey, hashData);
            }
        } catch (Exception e) {
            log.warn("Failed to cache users to hash", e);
        }
    }

    /**
     * 从数据库查询用户信息
     */
    private List<UserInfoDTO> getUsersFromDatabase(List<String> rmsIds) {
        LambdaQueryWrapper<IntlRmsUser> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(IntlRmsUser::getMiId, IntlRmsUser::getRmsUserid)
                .in(IntlRmsUser::getRmsUserid, rmsIds);

        List<IntlRmsUser> intlRmsUsers = intlRmsUserMapper.selectList(queryWrapper);

        return intlRmsUsers.stream()
                .filter(Objects::nonNull)
                .map(user -> {
                    UserInfoDTO userInfo = new UserInfoDTO();
                    userInfo.setMiId(user.getMiId());
                    userInfo.setRmsId(user.getRmsUserid());
                    return userInfo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 转换IntlRmsUser为UserInfoDTO
     */
    private UserInfoDTO convertToUserInfo(IntlRmsUser user) {
        UserInfoDTO userInfo = new UserInfoDTO();
        userInfo.setMiId(user.getMiId());
        userInfo.setDomainName(user.getDomainName());
        userInfo.setJobId(user.getJobId());
        userInfo.setEnglishName(user.getEnglishName());
        return userInfo;
    }
}
