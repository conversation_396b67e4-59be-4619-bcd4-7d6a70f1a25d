<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper">

    <!-- 根据Store Code查询门店信息 -->
    <select id="getStoreByCode" resultType="com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper$StoreInfoDto">
        SELECT
        s.store_id as storeId,
        s.code,
        s.crss_code as crssCode,
        s.name,
        s.country_shortcode as countryShortcode,
        s.created_on as createdOn,
        s.state_code as stateCode
        FROM intl_rms_store s
        WHERE (s.code = #{storeCode} OR s.crss_code = #{storeCode})
        AND s.state_code = 0
        LIMIT 1
    </select>


    <!-- 查询门店下所有阵地 -->
    <select id="getPositionsByStoreCode" resultType="com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper$PositionInfoDto">
        SELECT
        p.position_id as positionId,
        p.code,
        p.name,
        p.type_name as typeName,
        p.created_on as createdOn,
        p.state_code as stateCode
        FROM intl_rms_position p
        JOIN intl_rms_store s ON p.store_id = s.store_id
        WHERE (s.code = #{storeCode} OR s.crss_code = #{storeCode})
        AND p.state_code = 0
        AND s.state_code = 0
        ORDER BY
        CASE p.type_name
        WHEN 'SIS' THEN 1
        WHEN 'ES' THEN 2
        WHEN 'DZ' THEN 3
        WHEN 'DC' THEN 4
        WHEN 'POS' THEN 5
        ELSE 6
        END,
        p.created_on ASC
    </select>

    <!-- 根据Product ID校验产品是否存在 -->
    <select id="validateProductByProductId" resultType="boolean">
        <![CDATA[
        SELECT COUNT(1) > 0
        FROM intl_rms_product a
        INNER JOIN intl_sku_available b ON a.goods_id = b.product_code
        WHERE a.goods_id = #{productId}
        AND b.salestime <> 0
        AND b.salestime <= UNIX_TIMESTAMP() * 1000
        AND b.status = 0
        ]]>
    </select>

    <!-- 根据SKU校验产品是否存在 -->
    <select id="validateProductBySku" resultType="String">
        <![CDATA[
        SELECT a.goods_id
        FROM intl_rms_product a
        INNER JOIN intl_sku_available b ON a.goods_id = b.product_code
        WHERE a.sku_id = #{sku}
        AND b.salestime <> 0
        AND b.salestime <= UNIX_TIMESTAMP() * 1000
        AND b.status = 0
        LIMIT 1
        ]]>
    </select>

    <!-- 根据69 Code校验产品是否存在 -->
    <select id="validateProductByCode69" resultType="String">
        <![CDATA[
        SELECT a.goods_id
        FROM intl_rms_product a
        INNER JOIN intl_sku_available b ON a.goods_id = b.product_code
        WHERE a.code69 = #{code69}
        AND b.salestime <> 0
        AND b.salestime <= UNIX_TIMESTAMP() * 1000
        AND b.status = 0
        LIMIT 1
        ]]>
    </select>

</mapper>

    