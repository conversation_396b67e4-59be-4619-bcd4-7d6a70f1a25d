<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoQtyMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty">
        <id property="id" column="id" />
        <result property="rmsId" column="rms_id" />
        <result property="goodsId" column="goods_id" />
        <result property="productCode" column="product_code" />
        <result property="spuName" column="spu_name" />
        <result property="quantity" column="quantity" />
        <result property="orgInfoId" column="org_info_id" />
        <result property="userInfoId" column="user_info_id" />
        <result property="storeRmsCode" column="store_rms_code" />
        <result property="positionRmsCode" column="position_rms_code" />
        <result property="salesmanMid" column="salesman_mid" />
        <result property="salesTime" column="sales_time" />
        <result property="rrpCode" column="rrp_code" />
        <result property="rrp" column="rrp" />
        <result property="currency" column="currency" />
        <result property="batchId" column="batch_id" />
        <result property="reportingType" column="reporting_type" />
        <result property="note" column="note" />
        <result property="dataFrom" column="data_from" />
        <result property="isPhotoExist" column="is_photo_exist" />
        <result property="supplierCode" column="supplier_code" />
        <result property="retailerCode" column="retailer_code" />
        <result property="status" column="status" />
        <result property="createdby" column="createdby" />
        <result property="createdon" column="createdon" />
        <result property="modifiedby" column="modifiedby" />
        <result property="modifiedon" column="modifiedon" />
        <result property="createdOnNew" column="created_on_new" />
        <result property="updateOnNew" column="update_on_new" />
        <result property="batchIdStr" column="batch_id_str" />
        <result property="detailId" column="detail_id" />
        <result property="stockId" column="stock_id" />
        <result property="productLineEn" column="product_line_en" />
        <result property="productName" column="product_name" />
        <result property="productLineCode" column="product_line_code" />

    </resultMap>

    <sql id="Base_Column_List">
        id, rms_id, goods_id, product_code, spu_name, quantity, org_info_id, user_info_id, store_rms_code, position_rms_code, salesman_mid,
         sales_time, rrp_code, rrp, currency, batch_id, reporting_type, note, data_from, is_photo_exist, supplier_code, retailer_code, status,
         createdby, createdon, modifiedby, modifiedon, created_on_new, update_on_new, batch_id_str, detail_id, stock_id, product_line_en,
        product_name, product_line_code
    </sql>

    <!-- 查询可售SKU列表 -->
    <select id="getSkuList" resultType="com.mi.info.intl.retail.intlretail.service.api.so.dto.GetSkuListResponse$ProductInfo">
        <![CDATA[
        SELECT
            product.id AS id,
            product.english_name AS productName,
            product.goods_id AS productCode,
            product.product_line_en AS productLine,
            product.product_line_code AS productLineCode,
            product.shortname AS shortName
        FROM
            intl_sku_available sku
        INNER JOIN
            intl_rms_product product ON sku.product_code = product.goods_id
        WHERE
            sku.country_code = #{countryCode}
            AND sku.status = 0
            AND (#{productLineCode} = -1 OR product.product_line_code = #{productLineCode})
            AND (product.shortname = #{shortName} OR #{shortName} IS NULL OR #{shortName} = '')
            AND (product.english_name = #{productName} OR #{productName} IS NULL OR #{productName} = '')
            AND (product.english_name LIKE CONCAT('%', #{search}, '%') OR #{search} IS NULL OR #{search} = '')
          AND sku.salestime IS NOT NULL
        AND sku.salestime <= UNIX_TIMESTAMP() * 1000

            ORDER BY product.english_name
        ]]>
    </select>

    <!-- 获取用户门店列表 -->
    <select id="getUserStoreList" resultType="java.lang.Long">
        SELECT DISTINCT s.id as store_Id
        FROM intl_rms_user u
        JOIN intl_rms_personnel_position up ON u.rms_userid = up.user_id
        JOIN intl_rms_position p ON up.position_id = p.position_id
        JOIN intl_rms_store s ON p.store_id = s.store_id
        WHERE u.mi_id = #{miId}
        AND u.is_disabled = 0
        AND up.state_code = 0
        AND p.state_code = 0
        AND s.state_code = 0
    </select>

    <!-- 查询QTY列表 -->
    <select id="queryQtyList" resultType="com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyListResponse$QtyDetail">
        <![CDATA[
        SELECT
            qty.id AS id,
            p.english_name AS productName,
            qty.quantity AS quantity,
            qty.note AS note,
            org.store_code AS storeName,
            user.salesman_rmsaccount AS userName,
            DATE(FROM_UNIXTIME(qty.sales_time / 1000)) AS salesDate,
            FROM_UNIXTIME(qty.sales_time / 1000, '%Y-%m-%d %H:%i:%s') AS salesTime
        FROM intl_so_qty qty
        JOIN intl_so_org_info org ON qty.org_info_id = org.id
        JOIN intl_so_user_info user ON qty.user_info_id = user.id
        JOIN intl_rms_product p ON qty.product_code = p.goods_id
        WHERE org.country_code = #{countryCode}
        ]]>
        <if test="userStoreList != null and userStoreList.size() > 0">
            AND org.store_id IN
            <foreach collection="userStoreList" item="storeId" open="(" separator="," close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="userStoreList == null or userStoreList.size() == 0">
            AND user.salesman_mid = #{miId}
        </if>
        <if test="search != null and search != ''">
            AND (p.english_name LIKE CONCAT('%', #{search}, '%')
            OR p.chinese_name LIKE CONCAT('%', #{search}, '%'))
        </if>
        <if test="reportType != null">
            AND qty.reporting_type = #{reportType}
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND org.store_code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND p.product_line_en IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="channel" open="(" separator="," close=")">
                #{channel}
            </foreach>
        </if>
        <if test="dateFilterType == '2' and startTime != null and startTime != '' and endTime != null and endTime != ''">
            AND qty.sales_time >= #{startTime} AND qty.sales_time &lt;= #{endTime}
        </if>
        AND qty.status = 0
        ORDER BY qty.sales_time DESC
    </select>



    <!-- 统计当前筛选条件所有上报数量 -->
    <select id="queryQtyTotalCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM intl_so_qty qty
        JOIN intl_so_org_info org ON qty.org_info_id = org.id
        JOIN intl_so_user_info user ON qty.user_info_id = user.id
        JOIN intl_rms_product p ON qty.product_code = p.goods_id
        WHERE org.country_code = #{countryCode}
        <if test="userStoreList != null and userStoreList.size() > 0">
            AND org.store_id IN
            <foreach collection="userStoreList" item="storeId" open="(" separator="," close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="userStoreList == null or userStoreList.size() == 0">
            AND user.salesman_mid = #{miId}
        </if>
        <if test="search != null and search != ''">
            AND (p.english_name LIKE CONCAT('%', #{search}, '%')
            OR p.chinese_name LIKE CONCAT('%', #{search}, '%'))
        </if>
        <if test="reportType != null">
            AND qty.reporting_type = #{reportType}
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND org.store_code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND p.product_line_en IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="channel" open="(" separator="," close=")">
                #{channel}
            </foreach>
        </if>
        <if test="dateFilterType == '2' and startTime != null and startTime != '' and endTime != null and endTime != ''">
            AND qty.sales_time >= #{startTime} AND qty.sales_time &lt;= #{endTime}
        </if>
        AND qty.status = 0
    </select>

    <!-- 统计当前筛选条件下上报来源为PC的数量 -->
    <select id="queryQtyPcCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM intl_so_qty qty
        JOIN intl_so_org_info org ON qty.org_info_id = org.id
        JOIN intl_so_user_info user ON qty.user_info_id = user.id
        JOIN intl_rms_product p ON qty.product_code = p.goods_id
        WHERE org.country_code = #{countryCode}
        <if test="userStoreList != null and userStoreList.size() > 0">
            AND org.store_id IN
            <foreach collection="userStoreList" item="storeId" open="(" separator="," close=")">
                #{storeId}
            </foreach>
        </if>
        <if test="userStoreList == null or userStoreList.size() == 0">
            AND user.salesman_mid = #{miId}
        </if>
        <if test="search != null and search != ''">
            AND (p.english_name LIKE CONCAT('%', #{search}, '%')
            OR p.chinese_name LIKE CONCAT('%', #{search}, '%'))
        </if>
        <if test="reportType != null">
            AND qty.reporting_type = #{reportType}
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND org.store_code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND p.product_line_en IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="type" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="channel" open="(" separator="," close=")">
                #{channel}
            </foreach>
        </if>
        <if test="dateFilterType == '2' and startTime != null and startTime != '' and endTime != null and endTime != ''">
            AND qty.sales_time >= #{startTime} AND qty.sales_time &lt;= #{endTime}
        </if>
        AND qty.status = 0
        AND qty.data_from = 2
    </select>

    <!-- 统计当前筛选条件下上报来源为APP的数量 -->
    <select id="queryQtyAppCount" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM intl_so_qty qty
        JOIN intl_so_org_info org ON qty.org_info_id = org.id
        JOIN intl_so_user_info user ON qty.user_info_id = user.id
        JOIN intl_rms_product p ON qty.product_code = p.goods_id
        JOIN intl_rms_store s ON org.store_id = s.id
        WHERE 1=1
        AND org.country_code = #{countryCode}
        AND qty.status = 0
        AND qty.data_from = 1
        <choose>
            <when test="userStoreList != null and userStoreList.size() > 0">
                AND org.store_id IN
                <foreach collection="userStoreList" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </when>
            <otherwise>
                AND user.salesman_mid = #{miId}
            </otherwise>
        </choose>
        <if test="search != null and search != ''">
            AND (p.english_name LIKE CONCAT('%', #{search}, '%')
            OR p.chinese_name LIKE CONCAT('%', #{search}, '%'))
        </if>
        <if test="reportType != null">
            AND qty.reporting_type = #{reportType}
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND s.code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND p.product_line_en IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="stype" open="(" separator="," close=")">
                #{stype}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="ctype" open="(" separator="," close=")">
                #{ctype}
            </foreach>
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            AND qty.sales_time >= #{startTime} AND qty.sales_time &lt; #{endTime}
        </if>
    </select>

    <!-- 统计QTY数据（分组查询，一次查询获取总数、PC数量、APP数量） -->
    <select id="queryQtyStatisticsGroup" resultType="java.util.Map">
        SELECT
            COUNT(*) AS totalCount,
            SUM(CASE WHEN qty.reporting_type =100000000  THEN 1 ELSE 0 END) AS pcCount,
            SUM(CASE WHEN qty.reporting_type = 100000001 THEN 1 ELSE 0 END) AS appCount
        FROM intl_so_qty qty
        JOIN intl_so_org_info org ON qty.org_info_id = org.id
        JOIN intl_so_user_info user ON qty.user_info_id = user.id
        JOIN intl_rms_product p ON qty.product_code = p.goods_id
        JOIN intl_rms_store s ON org.store_id = s.id
        WHERE 1=1
        AND org.country_code = #{countryCode}
        AND qty.status = 0
        <choose>
            <when test="userStoreList != null and userStoreList.size() > 0">
                AND org.store_id IN
                <foreach collection="userStoreList" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </when>
            <otherwise>
                AND user.salesman_mid = #{miId}
            </otherwise>
        </choose>
        <if test="search != null and search != ''">
            AND (p.english_name LIKE CONCAT('%', #{search}, '%')
            OR p.chinese_name LIKE CONCAT('%', #{search}, '%'))
        </if>
        <if test="reportType != null">
            AND qty.reporting_type = #{reportType}
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND s.code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND p.product_line_en IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="stype" open="(" separator="," close=")">
                #{stype}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="ctype" open="(" separator="," close=")">
                #{ctype}
            </foreach>
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            AND qty.sales_time >= #{startTime} AND qty.sales_time &lt; #{endTime}
        </if>
    </select>

    <!-- 查询QTY看板数据 - 按日期分组 -->
    <select id="getQtyBoardDataByDate" resultType="java.util.Map">
        SELECT 
            p.product_line_en AS productLine,
            DAY(FROM_UNIXTIME(qty.sales_time / 1000)) AS dayOfMonth,
            SUM(qty.quantity) AS qtySum
        FROM intl_so_qty qty
        JOIN intl_so_org_info org ON qty.org_info_id = org.id
        JOIN intl_so_user_info user ON qty.user_info_id = user.id
        JOIN intl_rms_product p ON qty.product_code = p.goods_id
        WHERE org.country_code = #{countryCode}
        AND user.salesman_mid = #{miId}
        AND qty.status = 0
        AND qty.sales_time >= #{startTime}
        AND qty.sales_time &lt; #{endTime}
        GROUP BY p.product_line_en, DAY(FROM_UNIXTIME(qty.sales_time / 1000))
        ORDER BY p.product_line_en, dayOfMonth
    </select>

    <!-- 查询IMEI看板数据 - 按日期分组 -->
    <select id="getImeiBoardDataByDate" resultType="java.util.Map">
        SELECT 
            p.product_line_en AS productLine,
            DAY(FROM_UNIXTIME(imei.sales_time / 1000)) AS dayOfMonth,
            COUNT(*) AS imeiCount
        FROM intl_so_imei imei
        JOIN intl_so_org_info org ON imei.org_info_id = org.id
        JOIN intl_so_user_info user ON imei.user_info_id = user.id
        JOIN intl_rms_product p ON imei.product_code = p.goods_id
        WHERE org.country_code = #{countryCode}
        AND user.salesman_mid = #{miId}
        AND imei.status = 0
        AND imei.verification_result = 100000002
        AND imei.sales_time >= #{startTime}
        AND imei.sales_time &lt; #{endTime}
        GROUP BY p.product_line_en, DAY(FROM_UNIXTIME(imei.sales_time / 1000))
        ORDER BY p.product_line_en, dayOfMonth
    </select>

    <!-- 查询QTY看板数据 - 保持原有接口兼容性 -->
    <select id="getQtyBoardData" resultType="com.mi.info.intl.retail.intlretail.service.api.so.dto.GetQtyBoardDataResponse$BoardData">
        <![CDATA[
        SELECT 
            p.product_line_en AS productLine,
            COALESCE(SUM(CASE 
                WHEN DATE(FROM_UNIXTIME(qty.sales_time / 1000)) = CURDATE() 
                THEN qty.quantity 
                ELSE 0 
            END), 0) AS salesQtyDay,
            COALESCE(SUM(CASE 
                WHEN DATE(FROM_UNIXTIME(qty.sales_time / 1000)) >= DATE_SUB(CURDATE(), INTERVAL WEEKDAY(CURDATE()) DAY) 
                AND DATE(FROM_UNIXTIME(qty.sales_time / 1000)) <= CURDATE() 
                THEN qty.quantity 
                ELSE 0 
            END), 0) AS salesQtyWeek,
            COALESCE(SUM(CASE 
                WHEN DATE(FROM_UNIXTIME(qty.sales_time / 1000)) >= DATE_FORMAT(CURDATE(), '%%Y-%%m-01') 
                AND DATE(FROM_UNIXTIME(qty.sales_time / 1000)) <= CURDATE() 
            THEN qty.quantity 
                ELSE 0 
            END), 0) AS salesQtyMonth
        FROM intl_so_qty qty
        JOIN intl_so_org_info org ON qty.org_info_id = org.id
        JOIN intl_so_user_info user ON qty.user_info_id = user.id
        JOIN intl_rms_product p ON qty.product_code = p.goods_id
        WHERE org.country_code = #{countryCode}
        AND user.salesman_mid = #{miId}
        AND qty.status = 0
        GROUP BY p.product_line_en
        ORDER BY p.product_line_en
        ]]>
    </select>

    <!-- 查询QTY明细页数据 -->
    <select id="queryQtyDetail" resultType="com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyDetailResponse">

            SELECT
                qty.id AS id,
                p.english_name AS productName,
                qty.quantity AS quantity,
                qty.note AS note,
                qty.reporting_type AS reportType,
                DATE(FROM_UNIXTIME(qty.sales_time / 1000)) AS date,
                qty.quantity AS count,
                qty.is_photo_exist AS isPhotoExist,
                s.name AS storeName,
                u.english_name AS createdBy,
                qty.createdon AS createdOn,
                org.country_code AS countryCode,
                f.fds_url AS url,
                qty.detail_id as  detailId
            FROM intl_so_qty qty
                JOIN intl_so_org_info org ON qty.org_info_id = org.id
                JOIN intl_so_user_info user ON qty.user_info_id = user.id
                JOIN intl_rms_user u ON user.createdby_mid = u.mi_id
                JOIN intl_rms_product p ON qty.product_code = p.goods_id
                JOIN intl_rms_store s ON org.store_id = s.id
                LEFT JOIN intl_file_upload f ON qty.detail_id = f.guid
            WHERE qty.id = #{qtyId}
              AND qty.status = 0
                LIMIT 1

    </select>

    <!-- 查询QTY明细的图片列表 -->
    <select id="queryQtyPhotoList" resultType="com.mi.info.intl.retail.intlretail.service.api.so.dto.QueryQtyDetailResponse$PhotoInfo">
        <![CDATA[
        SELECT
            CONCAT('det-', UUID()) AS detailId,
            f.fds_url AS url
        FROM intl_file_upload f
        WHERE f.guid = #{qtyId}
        AND f.module_name = 'qty_upload'
        AND f.fds_url IS NOT NULL
        AND f.fds_url != ''
        ORDER BY f.uploader_time DESC
        ]]>
    </select>

    <!-- 查询QTY销售时间戳 -->
    <select id="queryQtySalesTime" resultType="java.lang.Long">
        SELECT qty.sales_time
        FROM intl_so_qty qty
        JOIN intl_so_user_info user ON qty.user_info_id = user.id
        WHERE qty.id = #{qtyId}
        AND user.salesman_mid = #{miId}
        AND qty.status = 0
    </select>

    <!-- 分页查询QTY列表 -->
    <select id="queryQtyListWithPage" resultType="java.util.Map">
        SELECT
            qty.id AS id,
            p.english_name AS productName,
            p.goods_id AS productCode,
            p.product_line_en AS productLine,
            p.shortname AS shortName,
            s.name AS storeName,
            s.code AS storeCode,
            org.store_type AS storeType,
            org.store_channel_type AS channelType,
            qty.quantity AS quantity,
            qty.note AS note,
            qty.reporting_type AS reportType,
            qty.is_photo_exist AS photoExistFlag,
            qty.sales_time AS sales_time,
            org.country_code AS country_code,
            user_info.english_name AS userName
        FROM intl_so_qty qty
        JOIN intl_so_org_info org ON qty.org_info_id = org.id
        JOIN intl_so_user_info  user ON qty.user_info_id = user.id
        JOIN
        intl_rms_user user_info ON qty.salesman_mid = user_info.mi_id
        JOIN intl_rms_product p ON qty.product_code = p.goods_id
        JOIN intl_rms_store s ON org.store_id = s.id
        WHERE 1=1
        AND org.country_code = #{countryCode}
        AND qty.status = 0
        <choose>
            <when test="userStoreList != null and userStoreList.size() > 0">
                AND org.store_id IN
                <foreach collection="userStoreList" item="storeId" open="(" separator="," close=")">
                    #{storeId}
                </foreach>
            </when>
            <otherwise>
                AND user.salesman_mid = #{miId}
            </otherwise>
        </choose>
        <if test="search != null and search != ''">
            AND (p.english_name LIKE CONCAT('%', #{search}, '%')
            OR p.chinese_name LIKE CONCAT('%', #{search}, '%'))
        </if>
        <if test="reportType != null">
            AND qty.reporting_type = #{reportType}
        </if>
        <if test="storeCode != null and storeCode.size() > 0">
            AND s.code IN
            <foreach collection="storeCode" item="scode" open="(" separator="," close=")">
                #{scode}
            </foreach>
        </if>
        <if test="productLine != null and productLine.size() > 0">
            AND p.product_line_code IN
            <foreach collection="productLine" item="line" open="(" separator="," close=")">
                #{line}
            </foreach>
        </if>
        <if test="storeType != null and storeType.size() > 0">
            AND org.store_type IN
            <foreach collection="storeType" item="stype" open="(" separator="," close=")">
                #{stype}
            </foreach>
        </if>
        <if test="channelType != null and channelType.size() > 0">
            AND org.store_channel_type IN
            <foreach collection="channelType" item="ctype" open="(" separator="," close=")">
                #{ctype}
            </foreach>
        </if>
        <if test="startTime != null and startTime != '' and endTime != null and endTime != ''">
            AND qty.sales_time >= #{startTime} AND qty.sales_time &lt; #{endTime}
        </if>
        ORDER BY qty.sales_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 获取门店列表 -->
    <select id="getStoreList" resultType="com.mi.info.intl.retail.intlretail.service.api.so.dto.GetStoreListResponse$StoreInfo">
        SELECT
        DISTINCT
            s.CODE AS code,
            s.NAME AS name,
            s.grade,
            s.grade_name AS gradeName
        FROM
            intl_rms_user u
            JOIN intl_rms_personnel_position up ON u.rms_userid = up.user_id
            JOIN intl_rms_position p ON up.position_id = p.position_id
            JOIN intl_rms_store s ON p.store_id = s.store_id
        WHERE
            u.is_disabled = 0 
            AND up.state_code = 0 
            AND p.state_code = 0 
            AND s.state_code = 0 
            AND u.mi_id = #{miId}
            <if test="search != null and search != ''">
                AND (s.NAME LIKE CONCAT('%', #{search}, '%') OR s.CODE LIKE CONCAT('%', #{search}, '%'))
            </if>
        ORDER BY p.NAME
    </select>

    <insert id="batchInsert" parameterType="com.mi.info.intl.retail.so.domain.datasync.dto.IntlSoQtyBatchSaveData">
        INSERT INTO intl_so_qty
        (rms_id, quantity, rrp, sales_time, note, product_code,modifiedon, createdon,
        reporting_type, status, store_rms_code,position_rms_code, retailer_code,currency, rrp_code, createdby,
        salesman_mid,
        modifiedby,data_from, user_info_id, org_info_id,stock_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.rmsId}, #{item.quantity}, #{item.rrp}, #{item.salesTime}, #{item.note}, #{item.productCode},
            #{item.modifiedon}, #{item.createdon}, #{item.reportingType}, #{item.status}, #{item.storeRmsCode},
            #{item.positionRmsCode}, #{item.retailerCode},#{item.currency}, #{item.rrpCode}, #{item.createdby},
            #{item.salesmanMid}, #{item.modifiedby},
            #{item.dataFrom},#{item.userInfoId}, #{item.orgInfoId},#{item.stockId})
        </foreach>
    </insert>
</mapper>
