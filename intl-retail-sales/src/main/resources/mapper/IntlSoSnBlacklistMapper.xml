<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoSnBlacklistMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist">
            <id property="id" column="id" />
            <result property="countryCode" column="country_code" />
            <result property="type" column="type" />
            <result property="snImei" column="sn_imei" />
            <result property="snhash" column="snhash" />
            <result property="rmsId" column="rms_id" />
            <result property="dataFrom" column="data_from" />
            <result property="status" column="status" />
            <result property="createdBy" column="createdby" />
            <result property="createdOn" column="createdon" />
            <result property="modifiedBy" column="modifiedby" />
            <result property="modifiedOn" column="modifiedon" />
    </resultMap>

    <sql id="Base_Column_List">
        id,country_code,type,sn_imei,snhash,rms_id,
        data_from,status,createdby,createdon,modifiedby,
        modifiedon
    </sql>

    <update id="updateStatusBatchByIds">
        UPDATE intl_so_sn_blacklist
        SET status = #{status},
            modifiedby = #{modifiedby},
        modifiedon = UNIX_TIMESTAMP()
        WHERE id IN
        <foreach item="item" index="index" collection="blacklistIdList"
                 open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>

    <select id="selectPageList" resultType="com.mi.info.intl.retail.so.domain.upload.entity.IntlSoSnBlacklist">
        SELECT  t1.id,t1.country_name,t1.type,
                t1.sn_imei,t1.snhash,t1.rms_id,
                t1.data_from,t1.status,t1.created_by,
                t1.created_on,t1.modified_by,
                t1.modified_on FROM intl_so_sn_blacklist t1
        <if test="request.createdBy != null and request.createdBy != ''">
            LEFT JOIN intl_rms_user t2 ON t1.created_by = t2.mi_id
        </if>
        <where>
            <if test="request.id != null">
               AND t1.id = #{request.id}
            </if>

            <if test="request.countryCodeList != null and !request.countryCodeList.isEmpty()">
                AND t1.country_code IN
                <foreach item="item" index="index" collection="request.countryCodeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
           <if test="request.typeCode != null and request.typeCode !=''">
               AND t1.type = #{request.typeCode}
           </if>
            <if test="request.snImei != null and request.snImei !=''">
               AND t1.sn_imei LIKE CONCAT('%',#{request.snImei},'%')
           </if>
            <if test="request.status != null">
                AND t1.status = #{request.status}
            </if>
            <if test="request.createStartOn != null">
                AND t1.created_on BETWEEN #{request.createStartOn} AND #{request.createEndOn}
            </if>
        <if test="request.createdBy != null and request.createdBy !=''">
            AND t2.english_name LIKE CONCAT('%',#{request.createdBy},'%')
           OR t1.created_by LIKE CONCAT('%',#{request.createdBy},'%')
        </if>

        </where>
        ORDER BY t1.modified_on DESC
        LIMIT #{request.pageSize} OFFSET #{request.offset}
    </select>


    <select id="countPageList" resultType="java.lang.Integer">
        SELECT count(1) FROM intl_so_sn_blacklist t1

        <if test="request.createdBy != null and request.createdBy != ''">
        LEFT JOIN intl_rms_user t2 ON t1.created_by = t2.mi_id
        </if>
        <where>

            <if test="request.id != null">
                AND t1.id = #{request.id}
            </if>

            <if test="request.countryCodeList != null and !request.countryCodeList.isEmpty()">
                AND t1.country_code IN
                <foreach item="item" index="index" collection="request.countryCodeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="request.typeCode != null and request.typeCode !=''">
                AND t1.type = #{request.typeCode}
            </if>
            <if test="request.snImei != null and request.snImei != ''">
                AND t1.sn_imei LIKE CONCAT('%',#{request.snImei},'%')
            </if>
            <if test="request.status != null">
                AND t1.status = #{request.status}
            </if>
            <if test="request.createStartOn != null">
                AND t1.created_on BETWEEN #{request.createStartOn} AND #{request.createEndOn}
            </if>
            <if test="request.createdBy != null and request.createdBy != ''">
                AND t2.english_name LIKE CONCAT('%',#{request.createdBy},'%')
                OR t1.created_by LIKE CONCAT('%',#{request.createdBy},'%')
            </if>

        </where>
    </select>


</mapper>
