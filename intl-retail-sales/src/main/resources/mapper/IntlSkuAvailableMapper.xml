<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSkuAvailableMapper">

    <resultMap id="BaseResultMap" type="com.mi.info.intl.retail.so.domain.upload.entity.IntlSkuAvailable">
            <id property="id" column="id" />
            <result property="countryCode" column="country_code" />
            <result property="productCode" column="product_code" />
            <result property="deliverytime" column="deliverytime" />
            <result property="salestime" column="salestime" />
            <result property="status" column="status" />
            <result property="createdBy" column="createdby" />
            <result property="createdOn" column="createdon" />
            <result property="modifiedBy" column="modifiedby" />
            <result property="modifiedOn" column="modifiedon" />
    </resultMap>

    <sql id="Base_Column_List">
        id,country_code,product_code,deliverytime,salestime,status,
        createdby,createdon,modifiedby,modifiedon
    </sql>

    <resultMap id="DataBySearchResultMap" type="com.mi.info.intl.retail.so.infra.dto.IntlSkuAvailableDetail">
        <!-- sku 表字段 -->
        <id property="id" column="id" />
        <result property="productId" column="product_code" />
        <result property="countryCode" column="country_code" />
        <result property="countryName" column="country_name" />
        <result property="salestime" column="salestime" />
        <result property="deliverytime" column="deliverytime" />

        <!-- product 表字段 -->
        <result property="skuId" column="sku_id" />
        <result property="skuName" column="sku_name" />
        <result property="shortname" column="shortname" />
        <result property="modelLevel" column="model_level_name" />
        <result property="code69" column="code69" />
        <result property="isSn" column="is_sn" />
        <result property="is69" column="is_69" />
        <result property="spuId" column="spu_id" />
        <result property="spuEn" column="spu_name_en" />
        <result property="categoryEn" column="category_en" />
        <result property="productLine" column="product_line" />
        <result property="productLineEn" column="product_line_en" />
        <result property="status" column="statsu" />
    </resultMap>

    <select id="getDataBySearch" resultMap="DataBySearchResultMap">
        SELECT
            sku.id,
            sku.product_code,
            sku.country_code,
            sku.country_name,
            sku.salestime,
            sku.deliverytime,
            sku.status,
            product.sku_id,
            product.sku_name,
            product.shortname,
            product.model_level_name,
            product.code69,
            product.is_sn,
            product.is_69,
            product.spu_id,
            product.spu_name_en,
            product.category_en,
            product.product_line,
            product.product_line_en
        FROM intl_sku_available sku
        LEFT JOIN intl_rms_product product ON sku.product_code = product.goods_id
        <include refid="BaseSearchCondition"/>
        LIMIT #{startNum}, #{limit}
    </select>
    <select id="getCountBySearch" resultType="java.lang.Integer">
        SELECT
           COUNT(*) AS count
        FROM intl_sku_available sku
        LEFT JOIN intl_rms_product product ON sku.product_code = product.goods_id
        <include refid="BaseSearchCondition"/>
    </select>


    <!-- 定义可复用的查询条件 -->
<sql id="BaseSearchCondition">
    <if test="searchBody != null">
        <where>
            <!-- 基础条件 -->
            <if test="searchBody.id != null and searchBody.id != 0">
                AND sku.id LIKE CONCAT('%', #{searchBody.id}, '%')
            </if>
            <if test="searchBody.productId != null and searchBody.productId != 0">
                AND sku.product_code = #{searchBody.productId}
            </if>
            <if test="searchBody.skuId != null and searchBody.skuId != ''">
                AND product.sku_id LIKE CONCAT('%', #{searchBody.skuId}, '%')
            </if>
            <if test="searchBody.shortname != null and searchBody.shortname != ''">
                AND product.shortname LIKE CONCAT('%', #{searchBody.shortname}, '%')
            </if>
            <if test="searchBody.code69 != null and searchBody.code69 != ''">
                AND product.code69 LIKE CONCAT('%', #{searchBody.code69}, '%')
            </if>
            <if test="searchBody.isSn != null">
                AND product.is_sn = #{searchBody.isSn}
            </if>
            <if test="searchBody.is69 != null">
                AND product.is_69 = #{searchBody.is69}
            </if>
            <if test="searchBody.spuId != null and searchBody.spuId != ''">
                AND product.spu_id LIKE CONCAT('%', #{searchBody.spuId}, '%')
            </if>
            <if test="searchBody.spuEn != null and searchBody.spuEn != ''">
                AND product.spu_name_en LIKE CONCAT('%', #{searchBody.spuEn}, '%')
            </if>
            <if test="searchBody.categoryEn != null and searchBody.categoryEn != ''">
                AND product.category_en LIKE CONCAT('%', #{searchBody.categoryEn}, '%')
            </if>
            <if test="searchBody.productLine != null and searchBody.productLine != ''">
                AND product.product_line = #{searchBody.productLine}
            </if>
            <if test="searchBody.productLineEn != null and !searchBody.productLineEn.isEmpty()">
                AND product.product_line_en IN
                <foreach item="item" index="index" collection="searchBody.productLineEn"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            <if test="searchBody.status != null and searchBody.status == 2">
                AND sku.salestime >=  UNIX_TIMESTAMP() * 1000
            </if>
            <if test="searchBody.status != null and searchBody.status == 1">
                AND sku.salestime  &lt;  UNIX_TIMESTAMP() * 1000
            </if>
            <if test="searchBody.countryCode != null and searchBody.countryCode != ''">
            AND sku.country_code = #{searchBody.countryCode}
            </if>
            <!--
            <if test="searchBody.countryCode != null and !searchBody.countryCode.isEmpty()">
                AND sku.country_code IN
                <foreach item="item" index="index" collection="searchBody.countryCode"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
            -->
            <if test="searchBody.salestime != null and searchBody.salestime.size() == 2">
                AND sku.salestime BETWEEN #{searchBody.salestime[0]} AND #{searchBody.salestime[1]}
            </if>
            <if test="searchBody.deliverytime != null and searchBody.deliverytime.size() == 2">
                AND sku.deliverytime BETWEEN #{searchBody.deliverytime[0]} AND #{searchBody.deliverytime[1]}
            </if>
        </where>
    </if>
</sql>

</mapper>
