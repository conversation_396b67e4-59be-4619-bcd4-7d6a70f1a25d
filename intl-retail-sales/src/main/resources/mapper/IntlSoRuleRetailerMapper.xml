<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleRetailerMapper">

    <select id="getRetailerStatistics"
            resultType="com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerStatisticsDTO">
        select count(*)                                              as totalRetailersCount,
        IFNULL(sum(IF(imei_switch = 1, 1, 0)), 0)                    as imeiRetailersCount,
        IFNULL(sum(IF(qty_switch = 1, 1, 0)), 0)                     as qtyRetailersCount,
        IFNULL(sum(IF(imei_switch = 0 and qty_switch = 0, 1, 0)), 0) as noRuleRetailersCount
        from intl_so_rule_retailers
        where country_code = #{countryCode}
        and category = #{category}
        <if test="operatorId != null">
            and created_by = #{operatorId}
        </if>
        <if test="excludeRetailerCodes != null and excludeRetailerCodes.size() > 0">
            and retailer_code not in
            <foreach collection="excludeRetailerCodes" item="retailerCode" separator="," open="(" close=")">
                #{retailerCode}
            </foreach>
        </if>
    </select>

    <!-- 批量插入或更新零售商规则配置，基于 retailer_code 进行更新 -->
    <insert id="batchInsertRetailer" parameterType="java.util.List">
        INSERT INTO intl_so_rule_retailers
        (region_code, country_code, category, rule_id, retailer_code, retailer_name, channel_type,
        imei_switch, qty_switch, create_retailer_time, created_at, created_by, updated_by, updated_at)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.regionCode},
            #{item.countryCode},
            #{item.category},
            #{item.ruleId},
            #{item.retailerCode},
            #{item.retailerName},
            #{item.channelType},
            #{item.imeiSwitch},
            #{item.qtySwitch},
            #{item.createRetailerTime},
            #{item.createdAt},
            #{item.createdBy},
            #{item.updatedBy},
            #{item.updatedAt}
            )
        </foreach>
    </insert>

    <!-- 根据id批量更新零售商规则配置 -->
    <update id="batchUpdateById" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE intl_so_rule_retailers
            SET region_code = #{item.regionCode},
            country_code = #{item.countryCode},
            retailer_name = #{item.retailerName},
            channel_type = #{item.channelType},
            updated_at = #{item.updatedAt}
            WHERE id = #{item.id}
        </foreach>
    </update>

</mapper>