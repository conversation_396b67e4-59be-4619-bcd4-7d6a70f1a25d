<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper">

    <update id="updateImeiSyncStatus"
            parameterType="com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto">
        update stock_imei_data_result i
        <set>
            i.sync_status=#{syncStatus},
            <if test="syncStartTime != null">
                i.sync_start_time=#{syncStartTime},
            </if>
            <if test="syncEndTime != null">
                i.sync_end_time=#{syncEndTime},
            </if>
        </set>
        where i.id_new in
        <foreach item="item" collection="idList" separator="," close=")" index="index" open="(">
            #{item}
        </foreach>
    </update>

    <update id="updateQtySyncStatus" parameterType="com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto">
        update stock_qty_data_result i
        <set>
            i.sync_status=#{syncStatus},
            <if test="syncStartTime != null">
                i.sync_start_time=#{syncStartTime},
            </if>
            <if test="syncEndTime != null">
                i.sync_end_time=#{syncEndTime},
            </if>
        </set>
        where i.id_new in
        <foreach item="item" collection="idList" separator="," close=")" index="index" open="(">
            #{item}
        </foreach>

    </update>

    <select id="getImeiMinId" resultType="java.lang.Long">
        select min(id_new)
        from stock_imei_data_result
    </select>
    <select id="getImeiMaxId" resultType="java.lang.Long">
        select max(id_new)
        from stock_imei_data_result
    </select>

    <select id="queryStockImeiDataResult" parameterType="com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto"
            resultType="com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData">
        SELECT id_new,
               status,
               failed_reason_detail,
               failed_reason,
               report_type,
               si_verify_result,
               verify_result,
               verifying_state,
               imei_rule_is_activing_check,
               modified_by_rms_id,
               new_rrpdata,
               modifiedon as modifiedon,
               activation_frequency,
               activation_site,
               activation_time,
               activation_verification_time,
               imei_rule_after,
               allow_sales_country,
               imei_rule_before,
               imei1,
               imei1_mask,
               imei2,
               imei2_mask,
               imei_from_hub,
               note,
               rrp,
               repeat_user_detail,
               verify_result_detail,
               rms_id,
               sales_time,
               sn,
               sn_mask,
               sn_hash,
               first_level_account_code,
               last_md,
               created_on,
               created_by_rms_id,
               sales_man_rms_id,
               product_code,
               final_sales_country,
               store_id,
               new_store_id,
               created_by_mid,
               sales_by_mid,
               retailer_code,
               created_on     AS createdTime,
               created_by_mid AS modifiedbyMiId,
               sales_by_mid   AS salesmanMiid,
               position_code  AS positionCodeRMS,
               store_code_rms AS storeCodeRMS,
               store_code     AS storeCodeNew,
               rrp_code       AS rrpCode,
               currency,
               1              as isStockData,
               1              as dataFrom,
               supplier_code
        FROM stock_imei_data_result t
        where t.sync_status !=1
        and t.id_new > #{lastSyncId}
        ORDER BY t.id_new
            LIMIT #{batchSize}
    </select>

    <select id="queryStockQtyDataResult" parameterType="com.mi.info.intl.retail.so.app.mq.dto.RmsStockDataSyncDto"
            resultType="com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData">
        SELECT id_new,
               status,
               report_type,
               rms_id,
               quantity,
               rrp,
               sales_time,
               note,
               modifiedon,
               product_code,
               store_id,
               position_id,
               retailer_code,
               currency,
               created_on     as createdon,
               created_by_mid as modifiedbyMiId,
               sales_by_mid   as salesmanMiid,
               position_code  as positionCodeRMS,
               store_code_rms as storeCodeRMS,
               store_code     as storeCodeNew,
               rrp_code       as rrpRMSCode,
               1              as isStockData,
               1              as dataFrom
        FROM stock_qty_data_result t
        where t.sync_status !=1
        and t.id_new > #{lastSyncId}
        ORDER BY t.id_new
            LIMIT #{batchSize}
    </select>

    <select id="getQtyMinId" resultType="java.lang.Long">
        select min(id_new)
        from stock_qty_data_result
    </select>

    <select id="getQtyMaxId" resultType="java.lang.Long">
        select max(id_new)
        from stock_qty_data_result
    </select>

</mapper>
