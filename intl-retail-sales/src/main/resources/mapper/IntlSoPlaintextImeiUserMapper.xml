<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoPlaintextImeiUserMapper">


    <sql id="Base_Column_List">
        id,country_name,country_code,
        photo_url,user_mid,createdby,
        createdon,modifiedby,modifiedon,
        status
    </sql>

    <select id="selectPageList" resultType="com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO">
        SELECT t1.id,t1.country_name,
        t1.photo_ids,t1.created_by,
        t1.created_on,t1.modified_by,t1.modified_on,
        t1.status,t3.english_name as createdByName,
        t1.user_mid as userAccount,t1.user_name as userName
        FROM intl_so_plaintext_imei_user t1
        LEFT JOIN intl_rms_user t3 ON t1.created_by = t3.mi_id
        <where>
            t1.status = 0
            <if test="query.userName != null and query.userName != ''">
                AND (t1.user_name LIKE CONCAT('%',#{query.userName},'%')
                OR t1.user_mid LIKE CONCAT('%',#{query.userName},'%'))
            </if>

            <if test="query.createdBy != null and query.createdBy != ''">
                AND (t3.english_name LIKE CONCAT('%',#{query.createdBy},'%')
                OR t1.createdBy LIKE CONCAT('%',#{query.createdBy},'%'))
            </if>

            <if test="query.userTitle != null and query.userTitle != '' ">
                AND  FIND_IN_SET(#{query.userTitle}, t1.job_title_id)
            </if>

            <if test="query.countryCodeList != null and query.countryCodeList.size() >0 ">
                AND t1.country_code IN
                <foreach item="item" index="index" collection="query.countryCodeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.createStartOn != null and query.createStartOn != '' ">
                AND t1.created_on BETWEEN #{query.createStartOn} AND #{query.createEndOn}
            </if>

            <if test="query.id != null ">
                AND t1.id = #{query.id}
            </if>
        </where>
        ORDER BY t1.modified_on DESC
        LIMIT #{query.pageSize} OFFSET #{query.offset}
    </select>



    <select id="countPageList" resultType="java.lang.Integer">
        SELECT count(*)
        FROM intl_so_plaintext_imei_user t1
        <if test="query.userName != null and query.userName != '' or query.userTitle != null and query.userTitle != ''">
            LEFT JOIN intl_rms_user t2 ON t1.user_mid = t2.mi_id
        </if>
        <if test="query.createdBy != null and query.createdBy != ''">
            LEFT JOIN intl_rms_user t3 ON t1.created_by = t3.mi_id
        </if>
        <where>
            t1.status = 0
            <if test="query.userName != null and query.userName != ''">
                AND (t1.user_name LIKE CONCAT('%',#{query.userName},'%')
                OR t1.user_mid LIKE CONCAT('%',#{query.userName},'%'))
            </if>

            <if test="query.createdBy != null and query.createdBy != '' ">
                AND (t3.english_name LIKE CONCAT('%',#{query.createdBy},'%')
                OR t1.created_by LIKE CONCAT('%',#{query.createdBy},'%'))
            </if>

            <if test="query.userTitle != null and query.userTitle != '' ">
                AND  FIND_IN_SET(#{query.userTitle}, t1.job_title_id)
            </if>

            <if test="query.countryCodeList != null and query.countryCodeList.size() >0">
                AND t1.country_code IN
                <foreach item="item" index="index" collection="query.countryCodeList"
                         open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>

            <if test="query.createStartOn != null and query.createStartOn != '' ">
                AND t1.created_on BETWEEN #{query.createStartOn} AND #{query.createEndOn}
            </if>

            <if test="query.id != null ">
                AND t1.id = #{query.id}
            </if>
        </where>
    </select>

</mapper>
