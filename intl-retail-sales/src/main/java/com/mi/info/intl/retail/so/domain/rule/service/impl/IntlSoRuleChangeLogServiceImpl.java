package com.mi.info.intl.retail.so.domain.rule.service.impl;

import static com.mi.info.intl.retail.exception.ErrorCodes.SO_RULE_LOG_NOT_EXIST;

import java.util.*;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.so.domain.rule.constants.SoRuleDictKey;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.springframework.stereotype.Service;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SoRuleBpmBodyDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.*;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleChangeLogReq;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySoRuleLogReq;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetailLog;
import com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleChangeLogMapper;
import com.mi.info.intl.retail.utils.AESGCMUtil;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.mi.info.intl.retail.utils.time.DateTimeUtil;

import lombok.extern.slf4j.Slf4j;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2025/7/24 11:05
 */
@Slf4j
@Service
public class IntlSoRuleChangeLogServiceImpl extends ServiceImpl<IntlSoRuleChangeLogMapper, IntlSoRuleDetailLog>
    implements IntlSoRuleChangeLogService {

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private IntlSysDictService intlSysDictService;

    @Override
    public PageDTO<SoRuleChangeLogListDTO> getSoRuleDetailLogList(QuerySoRuleLogReq req) {
        IPage<IntlSoRuleDetailLog> page = new Page<>(req.getPageNum(), req.getPageSize());
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getMasterId, req.getId());
        queryWrapper.orderByDesc(IntlSoRuleDetailLog::getCreatedAt);

        IPage<IntlSoRuleDetailLog> logPage = this.page(page, queryWrapper);
        String timeZone = DateTimeUtil.getTimeZoneByCountryCode(UserInfoUtil.getUserContext().getLanguage());
        List<SoRuleChangeLogListDTO> collect = logPage.getRecords().stream().map(item -> {
            SoRuleChangeLogListDTO dto = new SoRuleChangeLogListDTO();
            dto.setId(item.getId());
            dto.setApprovalId(item.getApprovalId());
            dto.setStatus(item.getStatus());
            dto.setStatusLabel(SoRuleDetailApproveStatus.getDescByValue(item.getStatus()));
            String approvalJson = item.getApproverList();
            List<ApproverDTO> approverList = JSON.parseArray(approvalJson, ApproverDTO.class);
            if (CollectionUtils.isNotEmpty(approverList)) {
                ApproverDTO approverDTO = approverList.stream().min(Comparator.comparing(ApproverDTO::getSort)).get();
                dto.setApplicant(approverDTO.getApproverList().get(0).getDisPlayName());
            }
            dto.setApplicationTime(item.getUpdatedAt());
            dto.setApplicationTimeStr(DateTimeUtil.getLocalDateTimeByTimeZone(timeZone, item.getUpdatedAt()));
            return dto;
        }).collect(Collectors.toList());
        PageDTO<SoRuleChangeLogListDTO> pageResult =
            new PageDTO<>(logPage.getCurrent(), logPage.getSize(), logPage.getTotal());
        pageResult.setRecords(collect);
        return pageResult;
    }

    /**
     * 通过ID获取SO规则细节日志
     *
     * @param req req
     * @return {@link SoRuleChangeLogDTO }
     */
    @Override
    public SoRuleChangeLogDTO getSoRuleChangeLogById(QuerySoRuleChangeLogReq req) {

        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getId, req.getId());
        IntlSoRuleDetailLog intlSoRuleDetailLog = this.getOne(queryWrapper);
        if (intlSoRuleDetailLog == null) {
            log.error("The so rule does not exist in the approval record {}", req.getId());
            throw new BizException(SO_RULE_LOG_NOT_EXIST, req.getId());
        }
        return getSoRuleDetailLogDTO(intlSoRuleDetailLog);

    }

    @Override
    public SoRuleChangeLogDTO getRuleDetailByCodeAndStatus(String countryCode, Integer status) {
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getCountryCode, countryCode).eq(status != null,
            IntlSoRuleDetailLog::getStatus, status);
        IntlSoRuleDetailLog intlSoRuleDetailLog = this.getOne(queryWrapper);
        if (intlSoRuleDetailLog == null) {
            log.error("The so rule does not exist in the approval record {}", countryCode);
            throw new BizException(SO_RULE_LOG_NOT_EXIST, countryCode);
        }
        return getSoRuleDetailLogDTO(intlSoRuleDetailLog);
    }

    public SoRuleChangeLogDTO getSoRuleDetailLogDTO(IntlSoRuleDetailLog intlSoRuleDetailLog) {
        SoRuleChangeLogDTO dto = new SoRuleChangeLogDTO();
        Optional<CountryDTO> countryOptional =
            countryTimeZoneApiService.getCountryInfoFromCache(intlSoRuleDetailLog.getCountryCode());
        if (countryOptional.isPresent()) {
            CountryDTO countryDTO = countryOptional.get();
            dto.setCountryName(countryDTO.getCountryName());
            dto.setRegionName(countryDTO.getArea());
        }
        Map<Integer, String> switchDict = getRetailerDefaultSwitchDict();
        List<Integer> switchList = JSON.parseArray(intlSoRuleDetailLog.getDefaultRetailersSwitch(), Integer.class);
        dto.setDefaultRetailersSwitchLabels(switchList.stream().map(switchDict::get).collect(Collectors.toList()));
        dto.setId(intlSoRuleDetailLog.getId());
        dto.setCountryCode(intlSoRuleDetailLog.getCountryCode());
        dto.setEffectiveTime(intlSoRuleDetailLog.getEffectiveTime());
        dto.setStatus(intlSoRuleDetailLog.getStatus());
        dto.setPhotoRuleList(JSON.parseArray(intlSoRuleDetailLog.getPhotoRuleList(), PhotoRuleDTO.class));
        dto.getPhotoRuleList().forEach(it -> {
            it.setImeiRequirePhotoLabel(SwitchEnum.convert(it.getImeiRequirePhoto()).getDescription());
            it.setQtyRequirePhotoLabel(SwitchEnum.convert(it.getQtyRequirePhoto()).getDescription());
        });
        dto.setImeiRuleList(JSON.parseArray(intlSoRuleDetailLog.getImeiRuleList(), ImeiRuleDTO.class));
        dto.setApproverList(JSON.parseArray(intlSoRuleDetailLog.getApproverList(), ApproverDTO.class));
        if (CollectionUtils.isNotEmpty(dto.getApproverList())) {
            // 对敏感字段进行解密
            for (ApproverDTO approverDTO : dto.getApproverList()) {
                approverDTO.getApproverList().forEach(
                    approverInfo -> approverInfo.setUserName(AESGCMUtil.decryptGCM(approverInfo.getUserName())));
                // 格式化审批时间
                approverDTO.setApproveTimeStr(IntlTimeUtil.getAreaTimeStr(approverDTO.getApproveTime()));
            }
        }
        SoRuleBpmBodyDTO soRuleBpmBodyDTO = JSON.parseObject(intlSoRuleDetailLog.getBpmBody(), SoRuleBpmBodyDTO.class);
        soRuleBpmBodyDTO.setApplicant(AESGCMUtil.decryptGCM(soRuleBpmBodyDTO.getApplicant()));
        Map<String, Object> approvers = soRuleBpmBodyDTO.getApprovers();
        if (MapUtils.isNotEmpty(approvers)) {
            approvers.entrySet()
                .forEach(entry -> entry.setValue(AESGCMUtil.decryptGCM(String.valueOf(entry.getValue()))));
        }
        soRuleBpmBodyDTO.getFormData()
            .setApplicant(AESGCMUtil.decryptGCM(soRuleBpmBodyDTO.getFormData().getApplicant()));
        dto.setBpmBody(JSON.toJSONString(soRuleBpmBodyDTO));
        dto.setCreatedAt(intlSoRuleDetailLog.getCreatedAt());
        dto.setCreatedBy(intlSoRuleDetailLog.getCreatedBy());
        dto.setUpdatedAt(intlSoRuleDetailLog.getUpdatedAt());
        dto.setUpdatedBy(intlSoRuleDetailLog.getUpdatedBy());
        return dto;
    }

    @Override
    public boolean existApprovingRule(String countryCode) {
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getCountryCode, countryCode).in(IntlSoRuleDetailLog::getStatus,
            SoRuleDetailApproveStatus.HAS_PENDING_APPROVAL_YES);
        return this.count(queryWrapper) > 0;
    }

    @Override
    public IntlSoRuleDetailLog getByApprovalId(String approvalId) {
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getApprovalId, approvalId);
        return this.getOne(queryWrapper);
    }

    @Override
    public IntlSoRuleDetailLog getDraftModifyRule(String countryCode) {
        LambdaQueryWrapper<IntlSoRuleDetailLog> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(IntlSoRuleDetailLog::getCountryCode, countryCode).eq(IntlSoRuleDetailLog::getStatus,
            SoRuleDetailApproveStatus.CREATE.getValue());
        return this.getOne(queryWrapper);
    }

    @Override
    public Map<Integer, String> getRetailerDefaultSwitchDict() {
        DictSysRequest dictSysRequest = new DictSysRequest();
        List<DictSysDTO> dictCodeList =
            Lists.newArrayList(new DictSysDTO(SoRuleDictKey.SO_RULE, SoRuleDictKey.DEFAULT_ENABLE_RULE));
        dictSysRequest.setDictCodeList(dictCodeList);
        Map<String, List<LabelValueDTO>> dictMap = intlSysDictService.getLabelValueListByDictCode(dictSysRequest);
        List<LabelValueDTO> labels = dictMap.get(SoRuleDictKey.DEFAULT_ENABLE_RULE);
        if (CollectionUtils.isEmpty(labels)) {
            return Collections.emptyMap();
        }
        return labels.stream().collect(Collectors.toMap(it -> Integer.valueOf(it.getId()), LabelValueDTO::getTitle));
    }

}
