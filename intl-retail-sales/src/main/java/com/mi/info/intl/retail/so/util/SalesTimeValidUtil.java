package com.mi.info.intl.retail.so.util;

import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;

/**
 * 时区转换工具类
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
public class SalesTimeValidUtil {

    // 添加私有构造函数，防止实例化
    private SalesTimeValidUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取当前时间戳（毫秒）
     *
     * @return 当前时间戳
     */
    public static Long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }


    // ==================== IMEI导入相关的时间校验方法 ====================

    // 错误常量
    private static final String ERROR_INVALID_DATE_FORMAT = "Invalid sales time format.";
    private static final String ERROR_FUTURE_DATE = "The sales time can not be filled in with a future date.";
    private static final String ERROR_DATE_WITHIN_45_DAYS = "Only data within 45 days can be reported.";
    private static final String ERROR_STORE_NOT_CREATED = "This store has not been created at the sales time.";


    /**
     * 校验销售时间的通用方法
     *
     * @param salesTimeStr          销售时间字符串
     * @param countryCode           国家代码
     * @param storeCreatedTimestamp 门店创建时间戳（可为null）
     * @return 校验结果，null表示校验通过，非null表示错误信息
     */
    public static String validateSalesTime(String salesTimeStr, String countryCode, Long storeCreatedTimestamp) {
        // 将销售时间（本地时间）转换为时间戳
        Long salesTimeTimestamp = IntlTimeUtil.parseLocalTimeToTimestamp(countryCode, salesTimeStr);
        if (salesTimeTimestamp == null) {
            return ERROR_INVALID_DATE_FORMAT;
        }

        long currentTimestamp = System.currentTimeMillis();

        // 销售时间不能是未来时间
        if (salesTimeTimestamp > currentTimestamp) {
            return ERROR_FUTURE_DATE;
        }

        // 获取销售时间日期
        Date salesDate = IntlTimeUtil.parseTimestampToAreaDate(countryCode, salesTimeTimestamp);
        // 获取当前时间，转换为国家本地日期
        Date currentDate = IntlTimeUtil.parseTimestampToAreaDate(countryCode, getCurrentTimestamp());
        // 判断销售时间是否在45天内（含当天）
        if (salesDate != null && currentDate != null && salesDate.before(new Date(currentDate.getTime() - 45L * 24 * 60 * 60 * 1000))) {
            return ERROR_DATE_WITHIN_45_DAYS;
        }

        // 校验销售时间是否小于门店创建时间（只比较日期）
        if (salesDate != null && storeCreatedTimestamp != null) {
            Date storeCreatedDate = IntlTimeUtil.parseTimestampToAreaDate(countryCode, storeCreatedTimestamp);
            if (storeCreatedDate != null && salesDate.before(storeCreatedDate)) {
                return ERROR_STORE_NOT_CREATED;
            }
        }
        return null; // 校验通过
    }

}
