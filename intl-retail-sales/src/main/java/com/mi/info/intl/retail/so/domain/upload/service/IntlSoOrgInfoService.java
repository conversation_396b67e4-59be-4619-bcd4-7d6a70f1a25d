package com.mi.info.intl.retail.so.domain.upload.service;

import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_org_info(销量阵地信息)】的数据库操作Service
 * @createDate 2025-07-25 16:40:50
 */
public interface IntlSoOrgInfoService extends IService<IntlSoOrgInfo> {

    List<IntlSoOrgInfo> batchGetByIds(List<Long> idList);

    Map<Long, IntlSoOrgInfo> batchGetByIdsMap(List<Long> idList);

    /**
     * 创建门店信息数据
     * @param positionStoreInfo
     * @param countryCode
     * @return
     */
    Long createOrgInfo(PositionStoreInfoDTO positionStoreInfo, String countryCode);

}
