package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 销量qty
 *
 * @TableName intl_so_qty
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "intl_so_qty")
@Data
public class IntlSoQty implements Serializable {

    private static final long serialVersionUID = -7583187993247235764L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联的 rms 标识 ID
     */
    @TableField(value = "rms_id")
    private String rmsId;

    /**
     * 产品 code
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * spu name
     */
    @TableField(value = "spu_name")
    private String spuName;

    /**
     * 数量
     */
    @TableField(value = "quantity")
    private Integer quantity;

    /**
     * 阵地组织信息 ID
     */
    @TableField(value = "org_info_id")
    private Long orgInfoId;

    /**
     * 用户信息 ID
     */
    @TableField(value = "user_info_id")
    private Long userInfoId;

    /**
     * 销售人mid
     */
    @TableField(value = "salesman_mid")
    private Long salesmanMid;

    /**
     * 创建人mid
     */
    @TableField(value = "createdby")
    private Long createdby;

    /**
     * 创建时间毫秒时间戳
     */
    @TableField(value = "createdon")
    private Long createdon;

    /**
     * 修改人mid
     */
    @TableField(value = "modifiedby")
    private Long modifiedby;

    /**
     * 修改时间
     */
    @TableField(value = "modifiedon")
    private Long modifiedon;

    /**
     * 时间毫秒时间戳
     */
    @TableField(value = "sales_time")
    private Long salesTime;

    /**
     * rrp
     */
    @TableField(value = "rrp")
    private BigDecimal rrp;

    /**
     * currency
     */
    @TableField(value = "currency")
    private String currency;

    /**
     * 导入记录ID
     */
    @TableField(value = "batch_id")
    private Integer batchId;

    /**
     * 上报类型
     */
    @TableField(value = "reporting_type")
    private Integer reportingType;

    /**
     * 备注
     */
    @TableField(value = "note")
    private String note;

    /**
     * 是否启用（0：可用；1 停用）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     * 数据来源（0：rms；1 retail）
     */
    @TableField(value = "data_from")
    private Integer dataFrom;

    /**
     * 0:不存在，1：存在
     */
    @TableField(value = "is_photo_exist")
    private Integer isPhotoExist;

    /**
     * store_rms_code
     */
    @TableField(value = "store_rms_code")
    private String storeRmsCode;

    /**
     * position_rms_code
     */
    @TableField(value = "position_rms_code")
    private String positionRmsCode;

    /**
     * retailer_code
     */
    @TableField(value = "retailer_code")
    private String retailerCode;

    @TableField(value = "supplier_code")
    private String supplierCode;

    /**
     * 关联的rrpID (rrp_code字段)
     */
    @TableField(value = "rrp_code")
    private String rrpCode;

    /**
     * 唯一标识Id, 用于和图片关联
     */
    @TableField(value = "detail_id")
    private String detailId;

    /**
     * 存量数据id
     */
    @TableField(value = "stock_id")
    private Long stockId;

    @TableField(value = "batch_id_str")
    private String batchIdStr;

    /**
     * 零售系统的更新时间
     */
    @TableField(value = "update_on_new")
    private Date updateOnNew;

    /**
     * 零售系统的新增时间
     */
    @TableField(value = "created_on_new")
    private Date createdOnNew;

    @TableField(value = "goods_id")
    private String goodsId;

    /**
     * 产品线编码
     */
    @TableField(value = "product_line_code")
    private Long productLineCode;

    /**
     * 产品线英文名称
     */
    @TableField(value = "product_line_en")
    private String productLineEn;
    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    private String productName;
}