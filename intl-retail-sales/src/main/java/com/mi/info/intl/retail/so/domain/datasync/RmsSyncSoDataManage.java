package com.mi.info.intl.retail.so.domain.datasync;

import com.alibaba.fastjson.JSONObject;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.app.mq.SyncSoToEsProducer;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncImeiData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncQtyData;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import com.xiaomi.cnzone.commons.exception.CommonBusinessException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 */
@Slf4j
@Component
public class RmsSyncSoDataManage {
    @Resource
    private DistributionLockService distributionLockService;

    @Resource
    private IntlSoImeiService intlSoImeiService;

    @Resource
    private IntlSoQtyService qtyService;
    @Resource
    private SyncSoToEsProducer syncSoToEsProducer;

    private static final String UNSUPPORTED_SYNC_TYPE = "unsupported sync type>>";
    private static final String UNSUPPORTED_OPERATE_TYPE = "unsupported operateType>>";

    public void saveDb(RmsSyncSoDataReqDto requestData) throws Exception {
        String type = requestData.getType();
        String operateType = requestData.getOperateType();
        JSONObject data = requestData.getData();

        if (type == null || operateType == null || data == null) {
            throw new IllegalArgumentException("operateType and data cannot be null");
        }

        try {
            DataSyncDataTypeEnum dataTypeEnum = DataSyncDataTypeEnum.getEnumByMessage(type);
            if (dataTypeEnum == null) {
                throw new BizException(UNSUPPORTED_SYNC_TYPE + type);
            }

            switch (dataTypeEnum) {
                case IMEI:
                    RmsSyncImeiData imeiData = convertToImeiData(data);
                    doImeiSync(operateType, requestData.getFields(), imeiData);
                    //发送消息同步ES
                    syncSoToEsProducer.sendSyncEsMsg(DataSyncDataTypeEnum.IMEI, imeiData.getId());
                    break;
                case QTY:
                    RmsSyncQtyData qtyData = convertToQtyData(data);
                    doQtySync(operateType, qtyData);
                    //发送消息同步ES
                    syncSoToEsProducer.sendSyncEsMsg(DataSyncDataTypeEnum.QTY, qtyData.getId());
                    break;
                default:
                    throw new BizException(UNSUPPORTED_SYNC_TYPE + type);
            }
        } catch (Exception e) {
            log.error("Error processing sync data request", e);
            throw new Exception(e.getMessage());
        }
    }

    private RmsSyncImeiData convertToImeiData(Object data) {
        if (data instanceof RmsSyncImeiData) {
            return (RmsSyncImeiData) data;
        }
        return JsonUtil.json2bean(JsonUtil.bean2json(data), RmsSyncImeiData.class);
    }

    private List<RmsSyncImeiData> convertToImeiList(Object data) {
        return JsonUtil.jsonArr2beanList(JsonUtil.bean2json(data), RmsSyncImeiData.class);
    }

    private RmsSyncQtyData convertToQtyData(Object data) {
        if (data instanceof RmsSyncQtyData) {
            return (RmsSyncQtyData) data;
        }
        return JsonUtil.json2bean(JsonUtil.bean2json(data), RmsSyncQtyData.class);
    }

    private void doImeiSync(String operateType, List<String> fields, RmsSyncImeiData data) {
        log.info("----------doImeiSync-----------, operatorType: {}", operateType);
        if (data == null) {
            throw new IllegalArgumentException("RmsSyncImeiData cannot be null");
        }
        String rmsId = data.getRmsId();
        String retailId = data.getRetailId();
        log.info("doImeiSync rmsId: {}，retailId：{}", rmsId, retailId);
        Integer dataFrom = data.getDataFrom();

        if (dataFrom == null || (dataFrom.equals(DataFromEnum.RETAIL.getCode()) && StringUtils.isEmpty(retailId))
                || (dataFrom.equals(DataFromEnum.RMS.getCode()) && StringUtils.isEmpty(rmsId))) {
            throw new IllegalArgumentException("Invalid dataFrom or missing id fields");
        }

        String lockId = dataFrom.equals(DataFromEnum.RETAIL.getCode()) ? retailId : rmsId;

        try (DistributionLock ignored = distributionLockService.tryLock(RedisKeyEnum.LOCK_SYNC_DATA_IMEI.getKey(),
                lockId)) {
            if (ignored == null) {
                log.warn("Failed to acquire IMEI sync lock for lockId: {}", lockId);
                throw new BizException("Failed to acquire IMEI sync lock");
            }

            IntlSoImei intlSoImeiExist = findIntlSoImei(dataFrom, retailId, rmsId);
            if (intlSoImeiExist == null) {
                intlSoImeiService.doImeiSave(data);
            } else {
                doImeiUpdate(operateType, fields, data, intlSoImeiExist.getId());
            }
        }
    }

    /**
     * 抽出更新的逻辑判断
     *
     * @param operateType 操作类型
     * @param fields 更新字段
     * @param data 数据
     * @param intlSoImeiId dataId
     */
    private void doImeiUpdate(String operateType, List<String> fields,
                              RmsSyncImeiData data, Long intlSoImeiId) {

        if (operateType == null) {
            throw new BizException(UNSUPPORTED_OPERATE_TYPE + "null");
        }

        DataSyncOperateTypeTypeEnum operateTypeEnum = DataSyncOperateTypeTypeEnum.getEnumByMsg(operateType);

        if (operateTypeEnum == null) {
            throw new BizException(UNSUPPORTED_OPERATE_TYPE + operateType);
        }
        //已存在，但operateType=create，则不处理
        if (operateTypeEnum.equals(DataSyncOperateTypeTypeEnum.CREATE)) {
            return;
        }

        if (operateTypeEnum == DataSyncOperateTypeTypeEnum.REPORT_VERIFICATION ||
                operateTypeEnum == DataSyncOperateTypeTypeEnum.ACTIVATE_VERIFICATION) {
            data.setId(intlSoImeiId);
            intlSoImeiService.doImeiUpdate(operateType, fields, data);
        } else {
            throw new BizException(UNSUPPORTED_OPERATE_TYPE + operateType);
        }
    }

    private IntlSoImei findIntlSoImei(Integer dataFrom, String retailId, String rmsId) {
        if (dataFrom.equals(DataFromEnum.RETAIL.getCode())) {
            return intlSoImeiService.checkImeiExist(Long.valueOf(retailId), null);
        } else if (dataFrom.equals(DataFromEnum.RMS.getCode())) {
            return intlSoImeiService.checkImeiExist(null, rmsId);
        }
        return null;
    }

    public void doQtySync(String operateType, RmsSyncQtyData data) {
        log.info("----------doQtySync-----------, operatorType: {}", operateType);
        if (data == null || data.getRmsId() == null) {
            throw new IllegalArgumentException("RmsSyncQtyData or rmsId cannot be null");
        }
        log.info("----------doQtySync-----------, rmsId: {}", data.getRmsId());

        String rmsId = data.getRmsId();
        try (DistributionLock ignored = distributionLockService.tryLock(RedisKeyEnum.LOCK_SYNC_DATA_QTY.getKey(),
                rmsId)) {
            if (ignored == null) {
                log.warn("Failed to acquire QTY sync lock for rmsId: {}", rmsId);
                throw new BizException("Failed to acquire QTY sync lock");
            }

            DataSyncOperateTypeTypeEnum operateTypeEnum = DataSyncOperateTypeTypeEnum.getEnumByMsg(operateType);
            if (operateTypeEnum == null) {
                throw new BizException(UNSUPPORTED_OPERATE_TYPE + operateType);
            }

            switch (operateTypeEnum) {
                case CREATE:
                    qtyService.doQtySave(data);
                    break;
                case UPDATE:
                    qtyService.doQtyUpdate(data);
                    break;
                default:
                    throw new BizException(UNSUPPORTED_OPERATE_TYPE + operateType);
            }
        }
    }

    public void saveBatchDb(List<RmsSyncSoDataReqDto> rmsSyncSoDataReqDtos) {
        RmsSyncSoDataReqDto requestData = rmsSyncSoDataReqDtos.get(0);
        String type = requestData.getType();
        String operateType = requestData.getOperateType();
        JSONObject data = requestData.getData();
        List<JSONObject> jsonObjectList =
                rmsSyncSoDataReqDtos.stream().map(RmsSyncSoDataReqDto::getData)
                        .collect(Collectors.toList());

        if (type == null || operateType == null || data == null) {
            throw new IllegalArgumentException("operateType and data cannot be null");
        }

        try {
            DataSyncDataTypeEnum dataTypeEnum = DataSyncDataTypeEnum.getEnumByMessage(type);
            if (dataTypeEnum == null) {
                throw new CommonBusinessException(UNSUPPORTED_SYNC_TYPE + type);
            }

            switch (dataTypeEnum) {
                case IMEI:
                    List<RmsSyncImeiData> imeiDatas = convertToImeiList(jsonObjectList);
                    doImeiSyncBatch(operateType, imeiDatas);
                    //发送消息同步ES
                   /* syncSoToEsProducer.sendSyncEsMsg(DataSyncDataTypeEnum.IMEI,
                            imeiDatas.stream().map(IntlSoImei::getId).collect(
                                    Collectors.toList()), false);*/
                    break;
                case QTY:
                    List<RmsSyncQtyData> qtyDataList = convertToQtyList(jsonObjectList);
                    doQtySyncBatch(operateType, qtyDataList);
                    //发送消息同步ES
                    /*syncSoToEsProducer.sendSyncEsMsg(DataSyncDataTypeEnum.QTY,
                            qtyDataList.stream().map(RmsSyncQtyData::getId).collect(Collectors.toList()), false);*/
                    break;
                default:
                    throw new CommonBusinessException(UNSUPPORTED_SYNC_TYPE + type);
            }
        } catch (Exception e) {
            log.error("Error processing sync data request", e);
            throw new CommonBusinessException(e.getMessage());
        }
    }

    private List<RmsSyncQtyData> convertToQtyList(List<JSONObject> jsonObjectList) {
        return JsonUtil.jsonArr2beanList(JsonUtil.bean2json(jsonObjectList), RmsSyncQtyData.class);
    }

    private void doQtySyncBatch(String operateType, List<RmsSyncQtyData> qtyDataList) {
        log.info("----------doImeiSyncBatch-----------, operatorType: {}, dataSize: {}", operateType,
                qtyDataList.size());
        if (CollectionUtils.isEmpty(qtyDataList)) {
            throw new IllegalArgumentException("doQtySyncBatch qtyDataList cannot be null or empty");
        }

        List<String> rmsIds = qtyDataList.stream()
                .filter(data -> data.getRmsId() != null)
                .map(RmsSyncQtyData::getRmsId)
                .collect(Collectors.toList());

        log.info("doQtySyncBatch get rmsIds:{}", rmsIds);
        // 批量检查IMEI是否存在
        List<IntlSoQty> intlSoQties = qtyService.checkQtysExistByRmsIds(rmsIds);

        // 分离需要新增和更新的数据
        Map<Boolean, List<RmsSyncQtyData>> partitionedData = qtyDataList.stream()
                .collect(Collectors.partitioningBy(data -> {
                    String rmsId = data.getRmsId();
                    return intlSoQties.stream()
                            .anyMatch(qty -> (rmsId != null && rmsId.equals(qty.getRmsId())));
                }));

        List<RmsSyncQtyData> newDataList = partitionedData.get(false);
        List<RmsSyncQtyData> updateDataList = partitionedData.get(true);

        // 批量保存新数据
        if (!newDataList.isEmpty()) {
            qtyService.doQtyBatchSave(newDataList);
        }

        // 批量更新已有数据
        if (!updateDataList.isEmpty()) {
            DataSyncOperateTypeTypeEnum operateTypeEnum = DataSyncOperateTypeTypeEnum.getEnumByMsg(operateType);
            if ((operateTypeEnum == DataSyncOperateTypeTypeEnum.REPORT_VERIFICATION ||
                    operateTypeEnum == DataSyncOperateTypeTypeEnum.ACTIVATE_VERIFICATION)) {
                qtyService.doQtyBatchUpdate(updateDataList);
            }
        }
    }

    private void doImeiSyncBatch(String operateType, List<RmsSyncImeiData> imeiDataList) {
        log.info("----------doImeiSyncBatch-----------, operatorType: {}, dataSize: {}", operateType,
                imeiDataList.size());
        if (CollectionUtils.isEmpty(imeiDataList)) {
            throw new IllegalArgumentException("doImeiSyncBatch imeiDataList cannot be null or empty");
        }

        // 批量获取数据来源
        Integer dataFrom = imeiDataList.get(0).getDataFrom();

        // 批量提取ID用于查询
        List<String> retailIds = imeiDataList.stream()
                .filter(data -> data.getRetailId() != null)
                .map(RmsSyncImeiData::getRetailId)
                .collect(Collectors.toList());

        List<String> rmsIds = imeiDataList.stream()
                .filter(data -> data.getRmsId() != null)
                .map(RmsSyncImeiData::getRmsId)
                .collect(Collectors.toList());

        log.info("doImeiSyncBatch get rmsIds:{}", rmsIds);
        // 批量检查IMEI是否存在
        List<IntlSoImei> existingImeis = findIntlSoImeis(dataFrom, retailIds, rmsIds);

        // 分离需要新增和更新的数据
        Map<Boolean, List<RmsSyncImeiData>> partitionedData = imeiDataList.stream()
                .collect(Collectors.partitioningBy(data -> {
                    String retailId = data.getRetailId();
                    String rmsId = data.getRmsId();
                    return existingImeis.stream()
                            .anyMatch(imei -> (retailId != null && retailId.equals(String.valueOf(imei.getId())))
                                    || (rmsId != null && rmsId.equals(imei.getRmsId())));
                }));

        List<RmsSyncImeiData> newDataList = partitionedData.get(false);
        List<RmsSyncImeiData> updateDataList = partitionedData.get(true);

        // 批量保存新数据
        if (!newDataList.isEmpty()) {
            intlSoImeiService.doImeiBatchSave(newDataList);
        }

        // 批量更新已有数据
        if (!updateDataList.isEmpty()) {
            DataSyncOperateTypeTypeEnum operateTypeEnum = DataSyncOperateTypeTypeEnum.getEnumByMsg(operateType);
            if ((operateTypeEnum == DataSyncOperateTypeTypeEnum.REPORT_VERIFICATION ||
                    operateTypeEnum == DataSyncOperateTypeTypeEnum.ACTIVATE_VERIFICATION)) {
                intlSoImeiService.doImeiBatchUpdate(operateType, updateDataList);
            }
        }
    }

    // 修改原有的findIntlSoImei方法，支持批量查询
    private List<IntlSoImei> findIntlSoImeis(Integer dataFrom, List<String> retailIds, List<String> rmsIds) {
        if (dataFrom.equals(DataFromEnum.RETAIL.getCode())) {
            return intlSoImeiService.batchCheckImeiExist(
                    retailIds.stream().map(Long::valueOf).collect(Collectors.toList()),
                    null);
        } else if (dataFrom.equals(DataFromEnum.RMS.getCode())) {
            return intlSoImeiService.batchCheckImeiExist(null, rmsIds);
        }
        return new ArrayList<>();
    }

}
