package com.mi.info.intl.retail.so.domain.imei.service.impl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

import javax.annotation.Resource;

import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.so.app.dto.SnImeiActiveInfoDTO;
import com.mi.info.intl.retail.so.app.dto.SnImeiActiveReq;
import com.mi.info.intl.retail.so.domain.imei.constans.SoImeiDictKey;
import com.mi.info.intl.retail.so.domain.imei.service.IntlSoImeiVerifyService;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.domain.upload.config.ImeiVerifyConfig;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoImeiService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.infra.service.SnImeiActiveQueryService;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;

import lombok.extern.slf4j.Slf4j;

@Slf4j
@Service
public class IntlSoImeiVerifyServiceImpl implements IntlSoImeiVerifyService {

    @Resource
    private ImeiVerifyConfig imeiVerifyConfig;

    @Resource
    private IntlSoImeiService intlSoImeiService;

    @Resource
    private IntlSysDictService intlSysDictService;

    @Resource
    private IntlSoOrgInfoService intlSoOrgInfoService;

    @Resource
    private SnImeiActiveQueryService snImeiActiveQueryService;

    private static final Integer BATCH_QUERY_SIZE = 3000;

    @Override
    public List<Long> verifyActivationStatusOfImei() {
        try {
            //1.获取会用到的所有的枚举值
            Map<String, List<LabelValueDTO>> imeiEnumList = this.getVerifyImeiEnumList();
            if (imeiEnumList.isEmpty()) {
                log.error("激活校验枚举值为空");
                return Collections.emptyList();
            }

            Map<String, String> verifyStateMap = getTargetEnumMap(SoImeiDictKey.SO_VERIFY_STATE, imeiEnumList);
            Map<String, String> verificationResultMap = getTargetEnumMap(SoImeiDictKey.SO_VERIFY_RESULT, imeiEnumList);
            Map<String, String> failedReasonMap = getTargetEnumMap(SoImeiDictKey.SO_FAILED_REASON, imeiEnumList);

            String normalStatus = verifyStateMap.get(SoImeiDictKey.SO_NORMAL_STATE_LABEL);
            String verifyingResult = verificationResultMap.get(SoImeiDictKey.SO_VERIFYING_VERIFY_LABEL);
            if (StringUtils.isBlank(normalStatus) || StringUtils.isBlank(verifyingResult)) {
                log.error("激活校验必要枚举值获取失败");
                return Collections.emptyList();
            }

            //2.获取需要校验的imei，正序3000条
            List<IntlSoImei> needVerifyImeiList = intlSoImeiService.getNeedVerifyImeiList(Integer.parseInt(normalStatus),
                    Integer.parseInt(verifyingResult), BATCH_QUERY_SIZE);
            if (CollectionUtils.isEmpty(needVerifyImeiList)) {
                return Collections.emptyList();
            }

            //3.遍历needVerifyImeiList，获取所有的orgInfoId
            List<Long> orgInfoIdList = needVerifyImeiList.stream().map(IntlSoImei::getOrgInfoId).distinct()
                    .collect(Collectors.toList());

            List<IntlSoOrgInfo> orgInfoList = intlSoOrgInfoService.batchGetByIds(orgInfoIdList);
            Map<Long, String> countryMap = orgInfoList.stream()
                    .collect(Collectors.toMap(IntlSoOrgInfo::getId, IntlSoOrgInfo::getCountryCode));

            //4.读取dayu配置获取哈希国家
            List<String> hashCountryList = imeiVerifyConfig.getHashCountryList();

            //5.构造IMEI激活服务查询参数、区分sn和snHex
            Map<String, List<IntlSoImei>> snAndSnHexMap = new HashMap<>();
            List<SnImeiActiveReq> snImeiActiveReqs = new ArrayList<>();
            List<String> crossExcludeClusters = Collections.singletonList("zjyprc-hadoop");

            for (IntlSoImei intlSoImei : needVerifyImeiList) {
                String country = countryMap.get(intlSoImei.getOrgInfoId());
                String sn = StringUtils.EMPTY;
                String snHexIdx = StringUtils.EMPTY;

                if (StringUtils.isBlank(country) || !hashCountryList.contains(country)) {
                    sn = intlSoImei.getSn();
                    List<IntlSoImei> list = snAndSnHexMap.computeIfAbsent(sn, k -> new ArrayList<>());
                    list.add(intlSoImei);
                } else {
                    snHexIdx = intlSoImei.getSnHash();
                    List<IntlSoImei> list = snAndSnHexMap.computeIfAbsent(snHexIdx, k -> new ArrayList<>());
                    list.add(intlSoImei);
                }

                SnImeiActiveReq req = new SnImeiActiveReq();
                req.setCross(true);
                req.setCrossExcludeClusters(crossExcludeClusters);
                req.setSn(sn);
                req.setSnHexIdx(snHexIdx);
                snImeiActiveReqs.add(req);
            }

            //6.批量查询IMEI激活信息
            List<Long> mqList = new ArrayList<>();
            final List<List<IntlSoImei>> result = snImeiActiveQueryService.batchQueryImeiActiveInfo(snImeiActiveReqs,
                    (req, snImeiInfoDto) -> processImeiActivationInfo(req, snImeiInfoDto, snAndSnHexMap,
                            failedReasonMap, verificationResultMap, countryMap, mqList));

            List<IntlSoImei> combineResult = result.stream()
                    .flatMap(Collection::stream)
                    .collect(Collectors.toList());

            //7.批量更新数据库
            intlSoImeiService.batchSoftUpdateById(combineResult);
            return mqList;
        } catch (Exception e) {
            log.error("IMEI激活状态校验过程中发生异常", e);
            return Collections.emptyList();
        }
    }

    /**
     * 处理IMEI激活信息
     *
     * @param req 请求参数
     * @param snImeiInfoDto 激活信息
     * @param snAndSnHexMap SN和SN哈希映射
     * @param failedReasonMap 失败原因映射
     * @param verificationResultMap 验证结果映射
     * @param countryMap 国家映射
     * @param mqList MQ列表
     * @return IMEI列表
     */
    private List<IntlSoImei> processImeiActivationInfo(SnImeiActiveReq req, SnImeiActiveInfoDTO snImeiInfoDto,
                                                       Map<String, List<IntlSoImei>> snAndSnHexMap, Map<String, String> failedReasonMap,
                                                       Map<String, String> verificationResultMap, Map<Long, String> countryMap, List<Long> mqList) {

        List<IntlSoImei> soImeiList = snAndSnHexMap.get(
                StringUtils.isBlank(req.getSn()) ? req.getSnHexIdx() : req.getSn());

        //接口调用失败的imei处理流程
        if (null == snImeiInfoDto) {
            return this.convertContinueVerifyDetail(soImeiList);
        }

        //个保法不可查询，均为失败
        if (snImeiInfoDto.getInfoExclude() == 1) {
            String failedReason = failedReasonMap.get(SoImeiDictKey.SO_NOT_ACTIVE_FAILED_REASON_LABEL);
            String failedResult = verificationResultMap.get(SoImeiDictKey.SO_FAILED_VERIFY_LABEL);
            String verifyResultDetail = "No verification info obtained";
            return this.convertFailedDetail(failedResult, failedReason, verifyResultDetail, soImeiList);
        }

        if (null == snImeiInfoDto.getInfo() || StringUtils.isBlank(snImeiInfoDto.getInfo().getActiveTime())) {
            //接口没有返回激活信息
            mqList.addAll(convertNoActiveInfo(soImeiList, countryMap));
        } else {
            //接口返回激活信息
            mqList.addAll(this.convertActiveInfo(snImeiInfoDto.getInfo(), soImeiList, countryMap));
        }

        return soImeiList;
    }

    private List<Long> convertNoActiveInfo(List<IntlSoImei> soImeiList, Map<Long, String> countryMap) {
        List<Long> result = new ArrayList<>();

        for (IntlSoImei intlSoImei : soImeiList) {
            // 获取国家代码
            String countryCode = countryMap.get(intlSoImei.getOrgInfoId());
            if (StringUtils.isBlank(countryCode)) {
                continue;
            }

            // 将销售时间和激活时间转换为对应国家时区的日期字符串（格式：yyyyMMdd）
            String salesDateStr = IntlTimeUtil.parseTimestampToFormatAreaTime(
                    IntlTimeUtil.TIME_FORMAT_DAY, countryCode, intlSoImei.getSalesTime());
            String currentDateStr = IntlTimeUtil.parseTimestampToFormatAreaTime(
                    IntlTimeUtil.TIME_FORMAT_DAY, countryCode, System.currentTimeMillis());

            // 判断激活时间是否在销售时间前后规则范围内
            if (!isActivationTimeWithinRule(salesDateStr, currentDateStr, intlSoImei.getImeiRuleBefore(),
                    intlSoImei.getImeiRuleAfter() + 3)) {
                // 不在范围内，激活校验失败
                intlSoImei.setVerificationResult(100000001);
                intlSoImei.setVerifyResultDetail("No verification info obtained");
                intlSoImei.setFailedReason(100000008);
                result.add(intlSoImei.getId());
            }

            int frequency = getDefaultIfZero(intlSoImei.getActivationFrequency()) + 1;
            intlSoImei.setActivationFrequency(frequency);
            intlSoImei.setActivationVerificationTime(System.currentTimeMillis());
        }

        return result;
    }

    private List<Long> convertActiveInfo(SnImeiActiveInfoDTO.Info activeInfo, List<IntlSoImei> soImeiList,
                                         Map<Long, String> countryMap) {
        // 将激活时间字符串转换为长整型时间戳
        long activeTime = (long) Double.parseDouble(activeInfo.getActiveTime()) * 1000;

        for (IntlSoImei intlSoImei : soImeiList) {
            // 获取国家代码
            String countryCode = countryMap.get(intlSoImei.getOrgInfoId());
            if (StringUtils.isBlank(countryCode)) {
                continue;
            }

            // 将销售时间和激活时间转换为对应国家时区的日期字符串（格式：yyyyMMdd）
            String salesDateStr = IntlTimeUtil.parseTimestampToFormatAreaTime(
                    IntlTimeUtil.TIME_FORMAT_DAY, countryCode, intlSoImei.getSalesTime());
            String activeDateStr = IntlTimeUtil.parseTimestampToFormatAreaTime(
                    IntlTimeUtil.TIME_FORMAT_DAY, countryCode, activeTime);

            // 判断激活时间是否在销售时间前后规则范围内
            if (isActivationTimeWithinRule(salesDateStr, activeDateStr, intlSoImei.getImeiRuleBefore(),
                    intlSoImei.getImeiRuleAfter())) {
                // 在范围内，激活校验成功
                intlSoImei.setVerificationResult(100000002);
                intlSoImei.setActivationTime(activeTime);
                intlSoImei.setActivationSite(activeInfo.getActiveCountry());
                intlSoImei.setVerifyResultDetail("Success");
            } else {
                // 不在范围内，激活校验失败
                intlSoImei.setVerificationResult(100000001);
                intlSoImei.setActivationTime(activeTime);
                intlSoImei.setActivationSite(activeInfo.getActiveCountry());
                intlSoImei.setVerifyResultDetail("Activation Time Exceeds the Rule");
                intlSoImei.setFailedReason(100000008);
            }

            int frequency = getDefaultIfZero(intlSoImei.getActivationFrequency()) + 1;
            intlSoImei.setActivationFrequency(frequency);
            intlSoImei.setActivationVerificationTime(System.currentTimeMillis());
        }

        return soImeiList.stream().map(IntlSoImei::getId).collect(Collectors.toList());
    }

    /**
     * 获取默认值，如果原值为null或0
     *
     * @param value 原值
     * @return 如果原值为null或0则返回默认值0，否则返回原值
     */
    private int getDefaultIfZero(Integer value) {
        return value != null ? value : 0;
    }

    /**
     * 判断激活时间是否在销售时间前后规则范围内
     *
     * @param salesDateStr 销售日期字符串（格式：yyyyMMdd）
     * @param targetDateStr 激活日期/目标日期字符串（格式：yyyyMMdd）
     * @param before 激活时间与销售时间之间的最小天数差
     * @param after 激活时间与销售时间之间最大的天数差
     * @return 是否在规则范围内
     */
    private boolean isActivationTimeWithinRule(String salesDateStr, String targetDateStr, Integer before, Integer after) {
        try {
            // 解析日期字符串为LocalDate对象
            LocalDate salesDate = LocalDate.parse(salesDateStr, DateTimeFormatter.ofPattern(IntlTimeUtil.TIME_FORMAT_DAY));
            LocalDate activeDate = LocalDate.parse(targetDateStr, DateTimeFormatter.ofPattern(IntlTimeUtil.TIME_FORMAT_DAY));

            // 计算激活日期与销售日期之间的天数差
            long daysBetween = ChronoUnit.DAYS.between(salesDate, activeDate);

            // 设置默认值
            if (before == null) {
                before = 0;
            }
            if (after == null) {
                after = 0;
            }

            // 判断激活时间是否在销售时间前后规则范围内
            return daysBetween >= -before && daysBetween <= after;
        } catch (Exception e) {
            log.error("判断激活时间是否在规则范围内时发生异常: {}", e.getMessage());
            return false;
        }
    }

    private Map<String, List<LabelValueDTO>> getVerifyImeiEnumList() {
        DictSysRequest dictRequest = new DictSysRequest();
        List<DictSysDTO> dictCodeList = new ArrayList<>();
        dictCodeList.add(new DictSysDTO(SoImeiDictKey.SO_IMEI_RULE, SoImeiDictKey.SO_VERIFY_RESULT));
        dictCodeList.add(new DictSysDTO(SoImeiDictKey.SO_IMEI_RULE, SoImeiDictKey.SO_VERIFY_STATE));
        dictCodeList.add(new DictSysDTO(SoImeiDictKey.SO_IMEI_RULE, SoImeiDictKey.SO_FAILED_REASON));
        dictRequest.setDictCodeList(dictCodeList);
        return intlSysDictService.getLabelValueListByDictCode(dictRequest);
    }

    private Map<String, String> getTargetEnumMap(String code, Map<String, List<LabelValueDTO>> imeiEnumList) {
        return imeiEnumList.get(code).stream().collect(Collectors.toMap(LabelValueDTO::getTitle, LabelValueDTO::getId));
    }

    private List<IntlSoImei> convertContinueVerifyDetail(List<IntlSoImei> soImeiList) {
        for (IntlSoImei detail : soImeiList) {
            int frequency = detail.getActivationFrequency() + 1;
            detail.setActivationFrequency(frequency);
            detail.setActivationVerificationTime(System.currentTimeMillis());
        }
        return soImeiList;
    }

    private List<IntlSoImei> convertFailedDetail(String failedResult, String failedReason, String resultDetail, List<IntlSoImei> soImeiList) {
        for (IntlSoImei detail : soImeiList) {
            int frequency = detail.getActivationFrequency() + 1;
            detail.setActivationFrequency(frequency);
            detail.setActivationVerificationTime(System.currentTimeMillis());
            detail.setVerificationResult(Integer.parseInt(failedResult));
            detail.setVerifyResultDetail(resultDetail);
            detail.setFailedReason(Integer.parseInt(failedReason));
        }
        return soImeiList;
    }
}