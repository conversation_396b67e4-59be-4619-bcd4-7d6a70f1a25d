package com.mi.info.intl.retail.so.app.provider.upload;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.StoreValidationInfoDTO;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.intlretail.service.api.fds.FdsService;
import com.mi.info.intl.retail.intlretail.service.api.fds.dto.FdsUploadResult;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.SubmitQtyReq;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.QtyImportService;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.upload.dto.QtyImportRowData;
import com.mi.info.intl.retail.so.app.IntlQtyServiceImpl;
import com.mi.info.intl.retail.so.app.provider.enums.StoreOperationStatusEnums;
import com.mi.info.intl.retail.so.app.provider.upload.listener.QtyImportExcelListener;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import com.mi.info.intl.retail.so.domain.upload.service.IntlImportLogService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.ImeiImportMapper;
import com.mi.info.intl.retail.so.util.ExcelFileUtil;
import com.mi.info.intl.retail.so.util.SalesTimeValidUtil;
import com.mi.info.intl.retail.so.util.dto.ExcelFileResource;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import com.mi.info.intl.retail.model.CommonApiResponse;


/**
 * QTY数据导入服务实现类
 *
 * <AUTHOR>
 * @date 2025/1/28
 */
@Slf4j
@Service
public class IntlQtyImportServiceImpl implements QtyImportService {

    @Resource
    private IntlImportLogService intlImportLogService;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private ProductApiService productApiService;

    @Resource
    private FdsService fdsService;

    @Resource
    private IntlQtyServiceImpl intlQtyService;

    @Resource
    private ImeiImportMapper imeiImportMapper;

    private final OkHttpClient httpClient = new OkHttpClient();

    // 最大导入行数限制
    private static final int MAX_IMPORT_ROWS = 5000;


    // 文件前缀/后缀
    private static final String FILE_PREFIX = "qty_import_error_";
    private static final String FILE_SUFFIX = ".xlsx";
    // 错误信息常量
    private static final String ERROR_EXCEED_MAX_ROWS = "The number of import rows exceeds the maximum limit of 5000";
    private static final String ERROR_EXCEED_MIN_ROWS = "There is no data available for uploading";
    private static final String ERROR_VALIDATION_FAILED = "Validation failed";
    private static final String ERROR_MANDATORY_FIELDS = "Items in red font are mandatory fields.";
    private static final String ERROR_DATE_WITHIN_45_DAYS = "Only data within 45 days can be reported.";
    private static final String ERROR_REPEATED_DATA = "Repeated data.";
    private static final String ERROR_NOT_IN_STORE = "You are not in this store.";
    private static final String ERROR_INVALID_DATE_FORMAT = "Invalid sales time format";
    private static final String ERROR_STORE_NOT_FOUND = "Store Code was not found.";
    private static final String ERROR_STORE_NOT_OPENING = "This store is not open.";



    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<QtyImportResponse> importQtyData(QtyImportRequest request) {
        log.info("importQtyData start, request: {}", JSON.toJSONString(request));
        int totalCount = 0;
        int failedCount = 0;

        try {
            // 1. 参数校验
            if (request.getImportLogId() == null || request.getMiId() == null) {
                return buildErrorResponse(new QtyImportResponse(), "importLogId and miId are required");
            }

            // 2. 获取源文件URL
            String sourceFileUrl = getSourceFileUrl(request);
            if (StringUtils.isBlank(sourceFileUrl)) {
                return buildErrorResponse(new QtyImportResponse(), "Source file URL is required");
            }

            // 3. 下载并解析Excel文件
            List<QtyImportRowData> excelDataList = downloadAndParseExcel(sourceFileUrl);

            // 4. 检查数据量是否超过限制
            if (excelDataList.size() > MAX_IMPORT_ROWS) {
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_EXCEED_MAX_ROWS, null);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_EXCEED_MAX_ROWS, null);
            }
            if (excelDataList.size() == 0) {
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_EXCEED_MIN_ROWS, null);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_EXCEED_MIN_ROWS, null);
            }

            // 5. 数据校验
            List<QtyImportRowData> validDataList = validateData(excelDataList, request.getMiId());

            // 6. 统计数据
             totalCount = excelDataList.size();
            failedCount = totalCount - validDataList.size();

            // 7. 检查是否有校验失败的数据
            boolean hasErrors = failedCount > 0;

            if (hasErrors) {
                // 生成错误文件并上传
                String errorFileUrl = generateResultFile(excelDataList);
                updateImportLogStatus(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, errorFileUrl, totalCount);
                return createErrorResponse(request.getImportLogId(), 2, ERROR_VALIDATION_FAILED, errorFileUrl, totalCount, failedCount);
            }

            // 8. 创建QTY数据
            boolean createSuccess = executeImport(validDataList, request.getMiId(), request.getImportLogId());

            if (createSuccess) {
                updateImportLogStatus(request.getImportLogId(), 1, null, null, totalCount);
                return createSuccessResponse(request.getImportLogId(), totalCount, failedCount);
            } else {
                updateImportLogStatus(request.getImportLogId(), 2, "QTY creation failed", null, totalCount);
                return createErrorResponse(request.getImportLogId(), 2, "QTY creation failed", null, totalCount, failedCount);
            }

        } catch (Exception e) {
            log.error("importQtyData error", e);
            updateImportLogStatus(request.getImportLogId(), 2, "System error: " + e.getMessage(), null, totalCount);
            return createErrorResponse(request.getImportLogId(), 2, "System error: " + e.getMessage(), null, totalCount, failedCount);
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonApiResponse<QtyImportResponse> importData(QtyImportRequest request) {
        // 调用原有的importQtyData方法，保持逻辑一致性
        return importQtyData(request);
    }

    /**
     * 获取源文件URL
     */
    private String getSourceFileUrl(QtyImportRequest request) {
        if (StringUtils.isNotBlank(request.getSourceFileUrl())) {
            return request.getSourceFileUrl();
        }

        // 根据importLogId查询
        IntlImportLog importLog = intlImportLogService.getById(request.getImportLogId());
        return importLog != null ? importLog.getSourceFileUrl() : null;
    }

    /**
     * 下载并解析Excel文件
     */
    private List<QtyImportRowData> downloadAndParseExcel(String fileUrl) throws IOException {
        List<QtyImportRowData> rowDataList = new ArrayList<>();
        File tempFile = null;

        try {
            // 下载文件到临时目录
            tempFile = downloadFile(fileUrl);
            if (tempFile == null || !tempFile.exists()) {
                throw new IOException("Failed to download file from: " + fileUrl);
            }

            // 使用EasyExcel解析
            AtomicInteger rowIndex = new AtomicInteger(1);
            EasyExcel.read(tempFile, QtyImportRowData.class, new QtyImportExcelListener(rowDataList, rowIndex))
                    .sheet()
                    .doRead();

        } finally {
            // 清理临时文件
            if (tempFile != null && tempFile.exists()) {
                Files.delete(tempFile.toPath());
            }
        }

        log.info("Excel文件解析完成，共解析{}行数据", rowDataList.size());
        return rowDataList;
    }

    /**
     * 下载文件到临时目录
     */
    private File downloadFile(String fileUrl) throws IOException {
        Request request = new Request.Builder().url(fileUrl).build();
        try (Response response = httpClient.newCall(request).execute()) {
            if (!response.isSuccessful()) {
                throw new IOException("Failed to download file: " + response.code());
            }

            File tempFile = File.createTempFile("qty_import_", ".xlsx");
            try (FileOutputStream fos = new FileOutputStream(tempFile);
                 InputStream is = response.body().byteStream()) {
                byte[] buffer = new byte[8192];
                int bytesRead;
                while ((bytesRead = is.read(buffer)) != -1) {
                    fos.write(buffer, 0, bytesRead);
                }
            }
            return tempFile;
        }
    }



    /**
     * 数据校验
     */
    private List<QtyImportRowData> validateData(List<QtyImportRowData> rowDataList, Long miId) {
        log.info("开始数据校验，数据量: {}", rowDataList.size());

        // 1. 必填项校验和合法性校验
        validateMandatoryAndLegality(rowDataList);

        // 2. 重复性校验
        validateDuplicates(rowDataList);

        // 3. 门店和用户权限校验
        validateStoreAndUserPermissions(rowDataList, miId);

        log.info("数据校验完成");

        return rowDataList.stream()
                .filter(QtyImportRowData::getValid)
                .collect(Collectors.toList());
    }

    /**
     * 必填项校验和合法性校验
     */
    private void validateMandatoryAndLegality(List<QtyImportRowData> dataList) {
        for (QtyImportRowData data : dataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }

            // 必填字段校验
            if (StringUtils.isBlank(data.getStoreCode()) ||
                StringUtils.isBlank(data.getSalesTime()) ||
                StringUtils.isBlank(data.getQty()) ||
                (StringUtils.isBlank(data.getSku()) && StringUtils.isBlank(data.getProductId()) && StringUtils.isBlank(data.getCode69()))) {
                data.setValid(false);
                data.setFailedReason(ERROR_MANDATORY_FIELDS);
                continue;
            }

            // Qty字段长度校验 - 不能超过200字符
            if (StringUtils.isNotBlank(data.getQty()) && data.getQty().length() > 200) {
                data.setValid(false);
                data.setFailedReason("Qty length exceeds 200 characters");
                continue;
            }

            // 销售时间格式校验（这里只做基本格式校验，时区相关校验在门店校验阶段进行）
            if (!isValidTimeFormat(data.getSalesTime())) {
                data.setValid(false);
                data.setFailedReason(ERROR_INVALID_DATE_FORMAT);
                continue;
            }
            // 数量校验 - 销售数量必须大于0
            if (StringUtils.isBlank(data.getQty()) || Integer.parseInt(data.getQty()) <= 0) {
                data.setValid(false);
                data.setFailedReason("The sales quantity must be greater than 0");
            }

            // Qty字段长度校验 - 不能超过200字符
            if (StringUtils.isNotBlank(data.getRemark()) && data.getRemark().length() > 200) {
                data.setValid(false);
                data.setFailedReason("Qty length exceeds 200 characters");
            }
        }
    }

    /**
     * 检查时间格式是否有效
     */
    private boolean isValidTimeFormat(String timeStr) {
        if (StringUtils.isBlank(timeStr)) {
            return false;
        }

        String[] patterns = {
            "yyyy-MM-dd HH:mm:ss",
            "yyyy-MM-dd",
            "yyyy/MM/dd HH:mm:ss",
            "yyyy/MM/dd",
            "yyyy/M/dd",
            "yyyy/M/d",
            "MM/dd/yyyy",
            "M/dd/yyyy",
            "M/d/yyyy",
            "dd/MM/yyyy",
            "d/MM/yyyy",
            "d/M/yyyy"
        };

        for (String pattern : patterns) {
            try {
                DateTimeFormatter formatter = DateTimeFormatter.ofPattern(pattern);
                if (pattern.contains("HH:mm:ss")) {
                    LocalDateTime.parse(timeStr, formatter);
                } else {
                    LocalDate.parse(timeStr, formatter);
                }
                return true;
            } catch (DateTimeParseException ignored) {
                // 继续尝试下一个格式
            }
        }
        return false;
    }




    /**
     * 校验SKU
     */
    private String validateSku(QtyImportRowData rowData) {
        // 三选一：Product ID、SKU、69 Code 只要有一个有效即可
        String productId = rowData.getProductId();
        String sku = rowData.getSku();
        String code69 = rowData.getCode69();

        // 检查Product ID
        if (StringUtils.isNotBlank(productId)) {
            if (validateProductById(productId)) {
                return productId;
            }
            else {
                return null;
            }
        }

        // 检查SKU
        if (StringUtils.isNotBlank(sku)) {
            String goodsId = validateProductBySku(sku);
            if (goodsId != null) {
                return goodsId;  // 返回goods_id作为productId
            }
            else  {
                return null;
            }
        }

        // 检查69 Code
        if (StringUtils.isNotBlank(code69)) {
            String goodsId = validateProductByCode69(code69);
            if (goodsId != null) {
                return goodsId;  // 返回goods_id作为productId
            }
            else
            {
                return null;
            }
        }

        // 所有字段都无效或校验失败
        return null;
    }

    /**
     * 根据Product ID校验产品
     */
    private boolean validateProductById(String productId) {
        try {
            // 通过Product ID查询产品信息
            List<String> productIds = Arrays.asList(productId);
            Map<Long, ProductInfoDTO> productMap = productApiService.queryProductsByGoodIds(productIds);
            if (!productMap.isEmpty()) {
                return true;
            }
            return false;
        } catch (Exception e) {
            log.warn("校验Product ID失败: {}", productId, e);
            return false;
        }
    }

    /**
     * 根据SKU校验产品
     */
    private String validateProductBySku(String sku) {
        try {
            // 通过SKU查询产品信息
            return imeiImportMapper.validateProductBySku(sku);
        } catch (Exception e) {
            log.warn("校验SKU失败: {}", sku, e);
            return null;
        }
    }

    /**
     * 根据69 Code校验产品
     */
    private String validateProductByCode69(String code69) {
        try {
            // 通过69 Code查询产品信息
            return imeiImportMapper.validateProductByCode69(code69);
        } catch (Exception e) {
            log.warn("校验69 Code失败: {}", code69, e);
            return null;
        }
    }


    /**
     * 重复性校验
     */
    private void validateDuplicates(List<QtyImportRowData> dataList) {
        // 记录已出现的SKU/Product ID及其第一次出现的信息，用于检测重复
        Map<String, String> seenSkuProductMap = new HashMap<>();

        for (QtyImportRowData data : dataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue; // 已经有错误的跳过
            }

            String skuProduct = StringUtils.isNotBlank(data.getSku()) ? data.getSku() : data.getProductId();
            if (StringUtils.isNotBlank(skuProduct)) {
                String upperSkuProduct = skuProduct.toUpperCase();
                if (seenSkuProductMap.containsKey(upperSkuProduct)) {
                    // 发现重复，设置错误信息，包含第一次出现的SKU/Product ID
                    String firstOccurrence = seenSkuProductMap.get(upperSkuProduct);
                    data.setValid(false);
                    data.setFailedReason(ERROR_REPEATED_DATA + " First occurrence: " + firstOccurrence);
                } else {
                    seenSkuProductMap.put(upperSkuProduct, skuProduct);
                }
            }
        }
    }


    /**
     * 生成错误文件
     */
    private String generateResultFile(List<QtyImportRowData> dataList) throws IOException {
        try (ExcelFileResource excelResource = ExcelFileUtil.generateExcelFile(
                dataList,
                QtyImportRowData.class,
                "数据报表")) {

            File excelFile = excelResource.getFile();
            // 上传到FDS服务器
            String timestamp = String.valueOf(System.currentTimeMillis() / 1000L);
            String fileName = FILE_PREFIX + timestamp + FILE_SUFFIX;
            FdsUploadResult uploadResult = fdsService.upload(fileName, excelFile, true);
            String fileUrl = uploadResult.getUrl();

            log.info("错误数据文件已上传到FDS: {}", fileUrl);
            return fileUrl;
        }
    }


    /**
     * 执行数据导入
     */
    private boolean executeImport(List<QtyImportRowData> validDataList, Long miId, Long importLogId) {
        try {
            // 按门店分组
            Map<String, List<QtyImportRowData>> storeGroups = validDataList.stream()
                    .collect(Collectors.groupingBy(QtyImportRowData::getStoreCode));

            for (Map.Entry<String, List<QtyImportRowData>> entry : storeGroups.entrySet()) {
                String storeCode = entry.getKey();
                List<QtyImportRowData> groupData = entry.getValue();

                // 1. 一次性查询门店信息、用户门店关系和阵地信息
                Optional<StoreValidationInfoDTO> storeValidationInfoOpt = intlPositionApiService.getStoreValidationInfo(storeCode, miId);
                if (!storeValidationInfoOpt.isPresent()) {
                    // 查询门店，进一步判断门店状态
                    RmsStoreInfoDto store = intlPositionApiService.getStoreByCode(storeCode);
                    if (store == null) {
                        log.error("门店不存在: {}", storeCode);
                    } else if (store.getOperationStatus() != StoreOperationStatusEnums.OPENING.getValue()) {
                        log.error("门店未开店: {}", storeCode);
                    } else {
                        log.error(ERROR_NOT_IN_STORE);
                    }
                    continue;
                }

                StoreValidationInfoDTO storeValidationInfo = storeValidationInfoOpt.get();

                // 3. 检查是否有最优阵地
                if (storeValidationInfo.getBestPosition() == null) {
                    log.error("门店下没有可用阵地: {}", storeCode);
                    continue;
                }

                // 转换为SubmitQtyReq并提交
                SubmitQtyReq submitReq = convertToSubmitQtyReq(groupData, storeValidationInfo, storeValidationInfo.getBestPosition(), miId, importLogId);
                CommonResponse<Object> submitResult = intlQtyService.submitQty(submitReq);

                if (submitResult.getCode() != 0) {
                    log.error("QTY提交失败: {}", submitResult.getMessage());
                    return false;
                }
            }

            return true;
        } catch (Exception e) {
            log.error("执行数据导入失败", e);
            return false;
        }
    }

    /**
     * 转换为SubmitQtyReq
     */
    private SubmitQtyReq convertToSubmitQtyReq(List<QtyImportRowData> groupData,
                                               StoreValidationInfoDTO store,
                                               RmsPositionInfoRes position,
                                               Long miId,
                                               Long importLogId) {
        SubmitQtyReq submitReq = new SubmitQtyReq();
        submitReq.setMiId(miId);
        submitReq.setCountryCode(store.getCountryShortcode());
        submitReq.setPositionCode(position.getCode());
        submitReq.setUserId("user_" + miId); // 设置用户ID
        submitReq.setImportLogId(importLogId); // 设置导入日志ID

        // 转换明细数据
        List<SubmitQtyReq.QtyDetailDto> detailList = new ArrayList<>();
        for (QtyImportRowData rowData : groupData) {
            SubmitQtyReq.QtyDetailDto detail = new SubmitQtyReq.QtyDetailDto();
            detail.setDetailId(""); // 赋值为空

            detail.setProductId(rowData.getProductId());
            
            detail.setQuantity(rowData.getQty());
            detail.setSalesTime(convertSalesTime(rowData.getSalesTime(), store.getCountryShortcode()));
            detail.setNote(rowData.getNote());
            detailList.add(detail);
        }
        submitReq.setDetailList(detailList);

        return submitReq;
    }

    /**
     * 转换销售时间
     */
    private Long convertSalesTime(String salesTime, String countryCode) {
        try {
            // 参考IMEI导入，使用parseLocalTimeToTimestamp方法
            return IntlTimeUtil.parseLocalTimeToTimestamp(countryCode, salesTime);
        } catch (Exception e) {
            log.warn("转换销售时间失败: {}, countryCode: {}", salesTime, countryCode, e);
            return System.currentTimeMillis();
        }
    }

    /**
     * 更新导入日志
     */
    private void updateImportLog(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        updateImportLog(importLogId, status, errorMsg, resultFileUrl, null);
    }

    /**
     * 更新导入日志（包含总数）
     */
    private void updateImportLog(Long importLogId, Integer status, String errorMsg, String resultFileUrl, Integer totalCount) {
        try {
            IntlImportLog importLog = intlImportLogService.getById(importLogId);
            if (importLog != null) {
                importLog.setStatus(status);
                importLog.setErrorMsg(errorMsg);
                importLog.setResultFileUrl(resultFileUrl);
                if (totalCount != null) {
                    importLog.setTotalCount(totalCount);
                }
                importLog.setUpdatedAt(System.currentTimeMillis());
                intlImportLogService.updateById(importLog);
            }
        } catch (Exception e) {
            log.error("更新导入日志失败, importLogId: {}", importLogId, e);
        }
    }

    /**
     * 构建错误响应
     */
    private CommonApiResponse<QtyImportResponse> buildErrorResponse(QtyImportResponse response, String errorMsg) {
        return buildErrorResponse(response, errorMsg, null);
    }

    /**
     * 构建错误响应（带结果文件）
     */
    private CommonApiResponse<QtyImportResponse> buildErrorResponse(QtyImportResponse response, String errorMsg, String resultFileUrl) {
        response.setStatus(2);
        response.setErrorMsg(errorMsg);
        if (StringUtils.isNotBlank(resultFileUrl)) {
            response.setResultFileUrl(resultFileUrl);
        }
        return new CommonApiResponse<>(response);
    }

    /**
     * 构建错误响应（重载方法，包含统计信息）
     */
    private CommonApiResponse<QtyImportResponse>
    buildErrorResponse(QtyImportResponse response, String errorMsg, String resultFileUrl, Integer totalCount, Integer failedCount) {
        response.setStatus(2);
        response.setErrorMsg(errorMsg);
        if (StringUtils.isNotBlank(resultFileUrl)) {
            response.setResultFileUrl(resultFileUrl);
        }
        if (totalCount != null) {
            response.setTotalCount(totalCount);
        }
        if (failedCount != null) {
            response.setFailedCount(failedCount);
        }
        return new CommonApiResponse<>(response);
    }

    /**
     * 构建成功响应
     */
    private CommonApiResponse<QtyImportResponse> createSuccessResponse(Long importLogId, int totalCount, int failedCount) {
        QtyImportResponse response = new QtyImportResponse();
        response.setImportLogId(importLogId);
        response.setStatus(1); // 导入成功
        response.setTotalCount(totalCount);
        response.setFailedCount(failedCount);
        return new CommonApiResponse<>(response);
    }

    /**
     * 门店和用户权限校验
     */
    private void validateStoreAndUserPermissions(List<QtyImportRowData> dataList, Long miId) {
        // 按Store Code分组
        Map<String, List<QtyImportRowData>> storeGroups = dataList.stream()
                .filter(data -> StringUtils.isBlank(data.getFailedReason()))
                .collect(Collectors.groupingBy(QtyImportRowData::getStoreCode));

        for (Map.Entry<String, List<QtyImportRowData>> entry : storeGroups.entrySet()) {
            String storeCode = entry.getKey();
            List<QtyImportRowData> storeDataList = entry.getValue();

            try {
                // 1. 一次性查询门店信息、用户门店关系和阵地信息
                Optional<StoreValidationInfoDTO> storeValidationInfoOpt = intlPositionApiService.getStoreValidationInfo(storeCode, miId);
                if (!storeValidationInfoOpt.isPresent()) {
                    // 查询门店，进一步判断门店状态
                    RmsStoreInfoDto store = intlPositionApiService.getStoreByCode(storeCode);
                    if (store == null) {
                        qtyMarkStoreDataAsFailed(storeDataList, ERROR_STORE_NOT_FOUND);
                    } else if (store.getOperationStatus() != StoreOperationStatusEnums.OPENING.getValue()) {
                        qtyMarkStoreDataAsFailed(storeDataList, ERROR_STORE_NOT_OPENING);
                    } else {
                        qtyMarkStoreDataAsFailed(storeDataList, ERROR_NOT_IN_STORE);
                    }
                    continue;
                }
                StoreValidationInfoDTO storeValidationInfo = storeValidationInfoOpt.get();
                // 2. 校验销售时间
                validateSalesTimeForStore(storeDataList, storeValidationInfo);

                // 3. 检查是否有最优阵地
                if (storeValidationInfo.getBestPosition() == null) {
                    qtyMarkStoreDataAsFailed(storeDataList, "No valid position found for store: " + storeCode);
                    continue;
                }

                // 5. 校验SKU
                validateSkuForStore(storeDataList);

            } catch (Exception e) {
                log.error("门店{}校验失败", storeCode, e);
                qtyMarkStoreDataAsFailed(storeDataList, "Store validation failed: " + e.getMessage());
            }
        }
    }

    /**
     * 标记门店下所有数据为失败
     */
    private void qtyMarkStoreDataAsFailed(List<QtyImportRowData> storeDataList, String errorMsg) {
        storeDataList.forEach(data -> {
            if (StringUtils.isBlank(data.getFailedReason())) {
                data.setValid(false);
               data.setFailedReason(errorMsg);
            }
        });
    }

    /**
     * 校验门店销售时间
     */
    private void validateSalesTimeForStore(List<QtyImportRowData> storeDataList, StoreValidationInfoDTO storeValidationInfo) {
        String countryCode = storeValidationInfo.getCountryShortcode();
        Long storeCreatedTimestamp = null;

        // 将门店创建时间（UTC时间字符串）转换为时间戳
        if (storeValidationInfo.getStoreCreatedOn() != null) {
            storeCreatedTimestamp = IntlTimeUtil.parseUtcDateTimeStringToTimestamp(storeValidationInfo.getStoreCreatedOn());
        }

        for (QtyImportRowData data : storeDataList) {
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue;
            }

            // 使用通用的校验方法
            String validationError = SalesTimeValidUtil.validateSalesTime(data.getSalesTime(), countryCode, storeCreatedTimestamp);
            if (validationError != null) {
                data.setValid(false);
                data.setFailedReason(validationError);

            }
        }
    }

    /**
     * 校验门店SKU
     */
    private void validateSkuForStore(List<QtyImportRowData> storeDataList) {
        for (QtyImportRowData data : storeDataList) {
            // 如果已经有错误，跳过校验
            if (StringUtils.isNotBlank(data.getFailedReason())) {
                continue;
            }

            String validatedProductId = validateSku(data);
            if (validatedProductId == null) {
                data.setValid(false);
                data.setFailedReason("SKU was not found");
            } else {
                data.setProductId(validatedProductId);
            }
        }
    }

    /**
     * 更新导入日志状态
     */
    private void updateImportLogStatus(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        updateImportLogStatus(importLogId, status, errorMsg, resultFileUrl, null);
    }

    /**
     * 更新导入日志状态（包含总数）
     */
    private void updateImportLogStatus(Long importLogId, Integer status, String errorMsg, String resultFileUrl, Integer totalCount) {
        IntlImportLog importLog = new IntlImportLog();
        importLog.setId(importLogId);
        importLog.setStatus(status);
        importLog.setErrorMsg(errorMsg);
        importLog.setResultFileUrl(resultFileUrl);
        if (totalCount != null) {
            importLog.setTotalCount(totalCount);
        }
        importLog.setUpdatedAt(System.currentTimeMillis());
        intlImportLogService.updateById(importLog);
    }

    /**
     * 创建错误响应
     */
    private CommonApiResponse<QtyImportResponse> createErrorResponse(Long importLogId, Integer status, String errorMsg, String resultFileUrl) {
        return createErrorResponse(importLogId, status, errorMsg, resultFileUrl, null, null);
    }

    /**
     * 创建错误响应（包含统计信息）
     */
    private CommonApiResponse<QtyImportResponse> createErrorResponse(Long importLogId, Integer status, String errorMsg, String resultFileUrl,
                                                                      Integer totalCount, Integer failedCount) {
        QtyImportResponse response = new QtyImportResponse();
        response.setImportLogId(importLogId);
        response.setStatus(status);
        response.setErrorMsg(errorMsg);
        response.setResultFileUrl(resultFileUrl);
        response.setTotalCount(totalCount);
        response.setFailedCount(failedCount);
        return new CommonApiResponse<>(response);
    }
} 