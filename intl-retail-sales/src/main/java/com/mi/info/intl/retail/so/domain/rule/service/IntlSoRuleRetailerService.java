package com.mi.info.intl.retail.so.domain.rule.service;

import java.util.List;

import org.apache.ibatis.session.ResultHandler;

import com.baomidou.mybatisplus.extension.service.IService;
import com.mi.info.intl.retail.api.so.rule.req.GetRetailerSoRuleReq;
import com.mi.info.intl.retail.bean.PageDTO;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleRetailerStatisticsDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.request.QuerySuRuleRetailerReq;
import com.mi.info.intl.retail.so.domain.rule.bean.GetRetailerSoRuleResp;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;

/**
 * <AUTHOR>
 * @date 2025/7/25 18:01
 */
public interface IntlSoRuleRetailerService extends IService<IntlSoRuleRetailer> {

    /**
     * 根据国家编码和类别查询零售商规则列表
     *
     * @param ruleId 规则ID
     * @param countryCode 国家编码
     * @param category    类别
     * @return 返回当前国家下零售商规则列表，根据类别查询是否主数据，或是变更时创建的记录
     */
    List<IntlSoRuleRetailer> getByCountryCodeAndCategoryWithRuleId(Long ruleId, String countryCode, Integer category);

    /**
     * 获取零售商规则
     *
     * @param req req
     * @return {@link GetRetailerSoRuleResp }
     */
    GetRetailerSoRuleResp getRetailerSoRule(GetRetailerSoRuleReq req);

    /**
     * 根据国家编码和创建人id，及传入的零售商编码查询零售商规则
     *
     * @param countryCode 国家编码
     * @param category 类别
     * @param retailerCodes 零售商编码列表
     * @return 零售商规则列表
     */
    List<IntlSoRuleRetailer> getByCountryCodeAndRetailerCodes(String countryCode, Integer category,
        List<String> retailerCodes);

    /**
     * 根据条件获取零售商
     *
     * @param req req
     * @return {@link List }<{@link SoRuleRetailerDTO }>
     */
    PageDTO<SoRuleRetailerDTO> getRetailerByCondition(QuerySuRuleRetailerReq req);

    /**
     * 出口零售商列表
     *
     * @param req req
     * @return {@link String }
     */
    String exportRetailerList(QuerySuRuleRetailerReq req);

    /**
     * 根据传入的零售商规则创建的type值，返回SoRuleEnum枚举值 1：代表新增，创建规则场景，将零售商规则分类设置为MASTER 2：代表修改场景下新增，修改规则场景，将零售商规则分类设置为COPY
     *
     * @param type 零售商规则创建的type值
     * @return SoRuleEnum
     */
    SoRuleEnum getRetailerCategoryByCreateType(Integer type);

    /**
     * 启动所有国家零售商
     */
    void initAllCountryRetailer();

    /**
     * 处理增量零售商
     */
    void dealIncrementRetailer();

    /**
     * 根据国家编码、类别、更新人，统计retailer规则总数、启用的imei上报总数、启用的qty上报总数，以及禁用imei & qty上报总数
     * 
     * @param countryCode 国家编码
     * @param category 类别
     * @param operatorId 操作人ID。创建规则时为null，修改规则时为当前登录人ID
     * @return {@link SoRuleRetailerStatisticsDTO }
     */
    SoRuleRetailerStatisticsDTO getRetailerStatistics(String countryCode, Integer category, String operatorId);

    /**
     * 根据国家编码、类别、更新人，统计retailer规则总数、启用的imei上报总数、启用的qty上报总数，以及禁用imei & qty上报总数
     *
     * @param countryCode 国家编码
     * @param category 类别
     * @param excludeRetailerCodes 排除的零售商编码列表
     * @return {@link SoRuleRetailerStatisticsDTO }
     */
    SoRuleRetailerStatisticsDTO getRetailerStatisticsWithExcludes(String countryCode, Integer category,
        List<String> excludeRetailerCodes);

    /**
     * 根据soRuleDetailLogId导出当前变更的零售商列表，生成excel并上传到fds，用于审批流程
     *
     * @param soRuleDetailLogId 规则变更日志id
     * @param countryCode 国家编码
     * @return 上传到fds的excel文件的url
     */
    String exportRetailerForApproveFlow(Long soRuleDetailLogId, String countryCode);

    /**
     * 根据国家编码、类别、imei开关、qty开关更新零售商规则
     *
     * @param countryCode 国家编码
     * @param category 类别
     * @param imeiSwitch imei开关，可为空
     * @param qtySwitch qty开关，可为空
     * @param updatedBy 更新人
     */
    void updateImeiAndQtySwitch(String countryCode, Integer category, SwitchEnum imeiSwitch, SwitchEnum qtySwitch,
        String updatedBy);

    /**
     * 根据国家编码，查询是否全量初始化了规则变更的零售商记录，若未全量初始化，返回false，否则返回true
     * 
     * @param countryCode 国家编码
     * @return true:存在 false:不存在
     */
    boolean isInitializedModifyRetailers(String countryCode);

    /**
     * 规则变更场景，初始化零售商规则列表。使用流式查询处理方式，避免加载全部数据到内存
     * 
     * @param countryCode 国家编码
     * @param excludeRetailerCodes 排除的零售商编码列表
     * @param resultHandler 结果处理器
     */
    void initModifyRetailers(String countryCode, List<String> excludeRetailerCodes,
        ResultHandler<IntlSoRuleRetailer> resultHandler);

    /**
     * 更新当前国家下零售商，绑定规则ID
     * 
     * @param countryCode 国家编码
     * @param category 类别
     * @param ruleId 规则ID。 若category=MASTER,则ruleId为主规则ID（intl_so_rule_detail.id）;若category=COPY,则ruleId为变更规则ID
     *            (intl_so_rule_detail_log.id)
     * @param createdBy 创建人，创建规则场景，传null； 修改规则场景，传当前登录人ID
     * @param updatedBy 更新人
     */
    void updateRuleId(String countryCode, int category, Long ruleId, String createdBy, String updatedBy);

    /**
     * 按规则ID获取零售商列表
     *
     * @param ruleId 规则ID
     * @return {@link List }<{@link IntlSoRuleRetailer }>
     */
    List<IntlSoRuleRetailer> getRetailerListByRuleId(Long ruleId);

    /**
     * 获取本次修改的零售商已初始化的信息列表
     * @param countryCode 国家编码
     * @param retailerCodes 本次修改的零售商编码列表，可为空
     * @return 本次修改的零售商已初始化的信息列表
     */
    List<IntlSoRuleRetailer> getCurrentInitRetailers(String countryCode, List<String> retailerCodes);

}
