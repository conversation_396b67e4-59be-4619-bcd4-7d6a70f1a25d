package com.mi.info.intl.retail.so.domain.rule.aggregate;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

import javax.annotation.Resource;
import javax.validation.constraints.NotNull;

import com.mi.info.intl.retail.utils.AESGCMUtil;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.extension.service.IService;
import com.google.common.collect.Lists;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.IntlPositionDTO;
import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.constant.CacheType;
import com.mi.info.intl.retail.core.utils.CacheUtils;
import com.mi.info.intl.retail.enums.SwitchEnum;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmUser;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.*;
import com.mi.info.intl.retail.model.UserInfo;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleEnum;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleChangeLogService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleDetailService;
import com.mi.info.intl.retail.so.domain.rule.service.IntlSoRuleRetailerService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetailLog;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleRetailer;
import com.mi.info.intl.retail.so.util.ConvertUtils;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.xiaomi.cnzone.commons.utils.DateUtils;

import cn.hutool.core.lang.Tuple;
import cn.hutool.core.thread.ThreadUtil;

/**
 * 规则创建聚合服务，处理事务逻辑
 *
 * <AUTHOR>
 * @date 2025/7/31 17:53
 */
@Service
public class SoRuleTransactionalAggregateService {

    @Resource
    private IntlSoRuleDetailService intlSoRuleDetailService;

    @Resource
    private IntlSoRuleRetailerService intlSoRuleRetailerService;

    @Resource
    private CountryTimeZoneApiService countryTimeZoneApiService;

    @Resource
    private IntlSoRuleChangeLogService intlSoRuleChangeLogService;

    @Resource
    private DistributionLockService distributionLockService;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Long createRule(SoRuleDetailCreateDTO soRuleDetailCreateDto) {
        UserInfo userInfo = UserInfoUtil.getUserContext();
        // 根据是否全部启用imei、qty开关参数、及单个修改的零售商规则列表，更新零售商规则
        doCreateRuleWithRetailers(soRuleDetailCreateDto, userInfo);
        // 创建规则明细记录
        IntlSoRuleDetail intlSoRuleDetail = buildSoRuleDetail(soRuleDetailCreateDto, userInfo);
        // 保存规则，获取ID
        intlSoRuleDetailService.save(intlSoRuleDetail);
        Long ruleId = intlSoRuleDetail.getId();
        // 零售商规则，绑定规则ID为当前提交的规则ID
        intlSoRuleRetailerService.updateRuleId(soRuleDetailCreateDto.getCountryCode(), SoRuleEnum.MASTER.getValue(),
            ruleId, null, String.valueOf(userInfo.getMiID()));
        return ruleId;
    }

    /**
     * 修改规则逻辑，创建规则副本，事务方法
     *
     * @param soRuleDetailModifyDto 规则修改参数
     * @return 规则副本ID
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public Long modifyRule(@NotNull SoRuleDetailModifyDTO soRuleDetailModifyDto) {
        // 若存在草稿状态的规则修改记录，则可返回，可以重新发起流程。
        String countryCode = soRuleDetailModifyDto.getCountryCode();
        IntlSoRuleDetailLog ruleDetailLog = intlSoRuleChangeLogService.getDraftModifyRule(countryCode);
        if (Objects.nonNull(ruleDetailLog)) {
            return ruleDetailLog.getId();
        }
        IntlSoRuleDetail masterRule =
            getMasterRule(soRuleDetailModifyDto.getRuleId(), soRuleDetailModifyDto.getCountryCode());
        UserInfo userInfo = UserInfoUtil.getUserContext();
        // 创建零售商规则副本，用于修改审批流程
        doModifyRuleWithRetailers(soRuleDetailModifyDto, userInfo);
        // 创建规则明细记录副本，用于修改审批流程
        IntlSoRuleDetail intlSoRuleDetail = buildSoRuleDetail(soRuleDetailModifyDto, userInfo);
        IntlSoRuleDetailLog intlSoRuleDetailLog =
            ComponentLocator.getConverter().convert(intlSoRuleDetail, IntlSoRuleDetailLog.class);
        // 绑定主规则ID，并重置状态为新建
        intlSoRuleDetailLog.setMasterId(masterRule.getId());
        intlSoRuleDetailLog.setStatus(SoRuleDetailApproveStatus.CREATE.getValue());
        intlSoRuleDetailLog.setEffectiveTime(null);
        // 设置审批节点信息，初始化审批状态，对用户名称进行加密
        initApproverList(soRuleDetailModifyDto, intlSoRuleDetailLog);
        // 保存规则副本，获取ID
        intlSoRuleChangeLogService.save(intlSoRuleDetailLog);
        Long ruleCopyId = intlSoRuleDetailLog.getId();
        // 零售商规则副本，绑定规则ID为当前提交的规则副本ID
        String operatorId = String.valueOf(userInfo.getMiID());
        intlSoRuleRetailerService.updateRuleId(countryCode, SoRuleEnum.COPY.getValue(), ruleCopyId, operatorId,
            operatorId);
        removeCache(masterRule.getCountryCode());
        return ruleCopyId;
    }

    /**
     * 规则修改，获取主规则信息
     * 
     * @param ruleId 规则ID
     * @param countryCode 国家编码
     * @return 主规则信息
     */
    public IntlSoRuleDetail getMasterRule(Long ruleId, String countryCode) {
        IntlSoRuleDetail masterRule;
        if (Objects.nonNull(ruleId)) {
            masterRule = intlSoRuleDetailService.getById(ruleId);
        } else {
            Optional<IntlSoRuleDetail> optional = intlSoRuleDetailService.getByCountryCode(countryCode);
            masterRule = optional.orElse(null);
        }
        if (Objects.isNull(masterRule)) {
            throw new BizException(ErrorCodes.SO_RULE_NOT_EXIST, ruleId);
        }
        return masterRule;
    }

    /**
     * 发起审批流程成功，更新审批状态
     *
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param approveId 审批人ID
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleLaunch(IntlSoRuleDetailLog ruleDetailLog, String approveId) {
        IntlSoRuleDetail masterRule = intlSoRuleDetailService.getById(ruleDetailLog.getMasterId());
        updateRuleApproveStatus(approveId, ruleDetailLog, SoRuleDetailApproveStatus.PENDING, masterRule,
            SoRuleDetailApproveStatus.PENDING);
    }

    /**
     * 删除缓存
     *
     * @param countryCode countryCode
     */
    public void removeCache(String countryCode) {
        ThreadUtil.execAsync(() -> {
            Optional<CountryDTO> optional = countryTimeZoneApiService.getCountryInfoFromCache(countryCode);
            if (!optional.isPresent()) {
                return;
            }
            List<IntlPositionDTO> positions =
                intlPositionApiService.getPositionsByCountryName(optional.get().getCountryName());
            if (CollectionUtils.isEmpty(positions)) {
                return;
            }
            positions.forEach(intlPositionDTO -> CacheUtils.removeCache(CacheType.SO_RULE_DETAIL_POSITION,
                intlPositionDTO.getPositionCode()));
        });
    }

    /**
     * 审批完成回调，事务处理。 将当前修改规则的副本数据，更新到主规则数据中。并更新审批状态。
     *
     * @param approveCompletedDTO 审批完成回调参数
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param masterRule 主规则记录（db查询）
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleCompleted(SoRuleApproveCallbackDTO approveCompletedDTO, IntlSoRuleDetailLog ruleDetailLog,
        IntlSoRuleDetail masterRule) {
        // 审批人id
        BpmUser operator = getApproveId(approveCompletedDTO);
        String approveId = operator.getPersonId();
        String approveName = operator.getUserName();
        Long current = DateUtils.getNowDate().getTime();
        String countryCode = ruleDetailLog.getCountryCode();
        // 更新审批状态为审批通过
        ruleDetailLog.setEffectiveTime(current);
        ruleDetailLog.setStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
        ruleDetailLog.setUpdatedBy(approveId);
        ruleDetailLog.setUpdatedAt(current);
        intlSoRuleChangeLogService.updateById(ruleDetailLog);

        // 将当前修改的零售商规则副本数据，更新到零售商规则主数据中。

        // 本次修改的零售商规则副本数据
        List<IntlSoRuleRetailer> ruleRetailerCopyList = intlSoRuleRetailerService
            .getByCountryCodeAndCategoryWithRuleId(ruleDetailLog.getId(), countryCode, SoRuleEnum.COPY.getValue());
        Map<String, IntlSoRuleRetailer> ruleRetailerCopyMap = ruleRetailerCopyList.stream()
            .collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));

        // 零售商规则主数据
        List<String> retailerCodes =
            ruleRetailerCopyList.stream().map(IntlSoRuleRetailer::getRetailerCode).collect(Collectors.toList());
        List<IntlSoRuleRetailer> ruleRetailerList = intlSoRuleRetailerService
            .getByCountryCodeAndRetailerCodes(countryCode, SoRuleEnum.MASTER.getValue(), retailerCodes);
        Map<String, IntlSoRuleRetailer> ruleRetailerMap = ruleRetailerList.stream()
            .collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));

        List<IntlSoRuleRetailer> toUpdate = Lists.newArrayList();
        List<IntlSoRuleRetailer> toAdd = Lists.newArrayList();
        // 处理需要更新的
        for (IntlSoRuleRetailer soRuleRetailer : ruleRetailerList) {
            IntlSoRuleRetailer ruleRetailerCopy = ruleRetailerCopyMap.get(soRuleRetailer.getRetailerCode());
            if (Objects.nonNull(ruleRetailerCopy)) {
                // 副本规则数据复制给主数据
                soRuleRetailer.setImeiSwitch(ruleRetailerCopy.getImeiSwitch());
                soRuleRetailer.setQtySwitch(ruleRetailerCopy.getQtySwitch());
                soRuleRetailer.setUpdatedAt(current);
                soRuleRetailer.setUpdatedBy(approveId);
                toUpdate.add(soRuleRetailer);
            }
        }
        // 处理需要新增的
        for (IntlSoRuleRetailer ruleRetailerCopy : ruleRetailerCopyList) {
            if (!ruleRetailerMap.containsKey(ruleRetailerCopy.getRetailerCode())) {
                // 副本数据转换为主数据
                IntlSoRuleRetailer toMaster =
                    ComponentLocator.getConverter().convert(ruleRetailerCopy, IntlSoRuleRetailer.class);
                toMaster.setId(null);
                toMaster.setRuleId(masterRule.getId());
                toMaster.setCategory(SoRuleEnum.MASTER.getValue());
                toAdd.add(toMaster);
            }
        }
        if (CollectionUtils.isNotEmpty(toAdd)) {
            intlSoRuleRetailerService.saveBatch(toAdd);
        }
        if (CollectionUtils.isNotEmpty(toUpdate)) {
            intlSoRuleRetailerService.updateBatchById(toUpdate);
        }

        // 统计零售商规则启用imei、qty开关数量
        SoRuleRetailerStatisticsDTO retailerStatistics =
            intlSoRuleRetailerService.getRetailerStatistics(countryCode, SoRuleEnum.MASTER.getValue(), null);

        // 将当前修改规则的副本数据，更新到主规则数据中。
        masterRule.setEffectiveTime(ruleDetailLog.getEffectiveTime());
        masterRule.setImeiRuleList(ruleDetailLog.getImeiRuleList());
        masterRule.setPhotoRuleList(ruleDetailLog.getPhotoRuleList());
        masterRule.setStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
        masterRule.setDefaultRetailersSwitch(ruleDetailLog.getDefaultRetailersSwitch());
        masterRule.setTotalRetailersCount(retailerStatistics.getTotalRetailersCount());
        masterRule.setNoRuleRetailersCount(retailerStatistics.getNoRuleRetailersCount());
        masterRule.setImeiRetailersCount(retailerStatistics.getImeiRetailersCount());
        masterRule.setQtyRetailersCount(retailerStatistics.getQtyRetailersCount());
        masterRule.setUpdatedAt(ruleDetailLog.getUpdatedAt());
        masterRule.setUpdatedBy(approveId);
        masterRule.setUpdatedByName(AESGCMUtil.encryptGCM(approveName));
        // 更新主规则数据
        intlSoRuleDetailService.updateById(masterRule);
    }

    /**
     * 审批拒绝回调，事务处理。
     *
     * @param approveRejectedDTO 审批拒绝回调参数
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param masterRule 主规则记录（db查询）
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleRejected(SoRuleApproveCallbackDTO approveRejectedDTO, IntlSoRuleDetailLog ruleDetailLog,
        IntlSoRuleDetail masterRule) {
        updateRuleApproveStatus(getApproveId(approveRejectedDTO).getPersonId(), ruleDetailLog,
            SoRuleDetailApproveStatus.REJECTED, masterRule, SoRuleDetailApproveStatus.APPROVED);
        removeCache(masterRule.getCountryCode());
    }

    /**
     * 审批撤回回调，事务处理。
     *
     * @param approveRecalledDTO 审批撤回回调参数
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param masterRule 主规则记录（db查询）
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleRecalled(SoRuleApproveCallbackDTO approveRecalledDTO, IntlSoRuleDetailLog ruleDetailLog,
        IntlSoRuleDetail masterRule) {
        updateRuleApproveStatus(getApproveId(approveRecalledDTO).getPersonId(), ruleDetailLog,
            SoRuleDetailApproveStatus.RECALLED, masterRule, SoRuleDetailApproveStatus.APPROVED);
        removeCache(masterRule.getCountryCode());
    }

    /**
     * 中间节点审批通过
     *
     * @param approveCallback 审批回调参数
     * @param ruleDetailLog 审批回调参数
     * @param masterRule 主规则记录（db查询）
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void approveRuleAgree(SoRuleApproveCallbackDTO approveCallback, IntlSoRuleDetailLog ruleDetailLog,
        IntlSoRuleDetail masterRule) {
        updateRuleApproveStatus(getApproveId(approveCallback).getPersonId(), ruleDetailLog,
            SoRuleDetailApproveStatus.PENDING, masterRule, SoRuleDetailApproveStatus.PENDING);
        removeCache(masterRule.getCountryCode());
    }

    /**
     * 创建规则场景：处理零售商规则 <br/>
     * 1、若需要全量开启或关闭imei、qty，则需要根据国家编码全量更新该国家下零售商规则的imei、qty开关<br/>
     * 2、若retailerList不为空，则根据retailerList来更新开关<br/>
     *
     * @param retailerBatchCreateDto 创建规则参数
     * @param userInfo 登录用户
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void doCreateRuleWithRetailers(SoRuleBaseReqDTO retailerBatchCreateDto, UserInfo userInfo) {
        Tuple imeiQtySwitch =
            getImeiAndQtySwitch(retailerBatchCreateDto.getAllAddImei(), retailerBatchCreateDto.getAllAddQty());
        SwitchEnum imeiSwitch = imeiQtySwitch.get(0);
        SwitchEnum qtySwitch = imeiQtySwitch.get(1);
        // 若需要全量开启或关闭imei、qty，则根据国家编码更新零售商副本数据的imei、qty开关
        if (Objects.nonNull(imeiSwitch) || Objects.nonNull(qtySwitch)) {
            intlSoRuleRetailerService.updateImeiAndQtySwitch(retailerBatchCreateDto.getCountryCode(),
                SoRuleEnum.MASTER.getValue(), imeiSwitch, qtySwitch, String.valueOf(userInfo.getMiID()));
        }
        // 若retailerList不为空，则根据retailerList来更新开关
        List<SoRuleRetailerItemDTO> retailerList = retailerBatchCreateDto.getRetailerList();
        if (CollectionUtils.isEmpty(retailerList)) {
            return;
        }
        List<String> retailerCodes = retailerBatchCreateDto.getRetailerList().stream()
            .map(SoRuleRetailerItemDTO::getRetailerCode).collect(Collectors.toList());
        List<IntlSoRuleRetailer> dbRetailerList = intlSoRuleRetailerService.getByCountryCodeAndRetailerCodes(
            retailerBatchCreateDto.getCountryCode(), SoRuleEnum.MASTER.getValue(), retailerCodes);
        Map<String, IntlSoRuleRetailer> dbRetailerMap =
            dbRetailerList.stream().collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));
        long currentTime = DateUtils.getNowDate().getTime();
        retailerBatchCreateDto.getRetailerList().forEach(retailer -> {
            IntlSoRuleRetailer dbRetailer = dbRetailerMap.get(retailer.getRetailerCode());
            if (Objects.nonNull(dbRetailer)) {
                dbRetailer.setImeiSwitch(retailer.getImeiSwitch());
                dbRetailer.setQtySwitch(retailer.getQtySwitch());
                dbRetailer.setUpdatedAt(currentTime);
                dbRetailer.setUpdatedBy(String.valueOf(userInfo.getMiID()));
            }
        });
        // 批量更新
        intlSoRuleRetailerService.updateBatchById(dbRetailerList);
    }

    /**
     * 处理修改规则时，初始化零售商规则<br/>
     * 若allAddImei或allAddQty不为空，则需要全量初始化一次零售商副本数据。再根据传入的规则更新零售商规则<br/>
     *
     * @param retailerBatchCreateDto 零售商规则配置对象
     * @param userInfo 登录用户
     */
    @Transactional(propagation = Propagation.REQUIRED, rollbackFor = Exception.class)
    public void doModifyRuleWithRetailers(SoRuleBaseReqDTO retailerBatchCreateDto, UserInfo userInfo) {
        String countryCode = retailerBatchCreateDto.getCountryCode();
        // 若需要全量开启或关闭imei、qty，则需要全量初始化零售商副本数据
        Tuple imeiAndQtySwitch =
            getImeiAndQtySwitch(retailerBatchCreateDto.getAllAddImei(), retailerBatchCreateDto.getAllAddQty());
        boolean needInit = Objects.nonNull(imeiAndQtySwitch.get(0)) || Objects.nonNull(imeiAndQtySwitch.get(1));
        // 查询出当前初始化的副本数据
        List<String> retailerCodes = retailerBatchCreateDto.getRetailerList().stream()
            .map(SoRuleRetailerItemDTO::getRetailerCode).collect(Collectors.toList());
        List<IntlSoRuleRetailer> dbRetailerList = Lists.newArrayList();
        if (needInit) {
            initModifyRetailerList(retailerBatchCreateDto, userInfo);
            dbRetailerList = intlSoRuleRetailerService.getCurrentInitRetailers(countryCode, retailerCodes);
        }

        List<SoRuleRetailerItemDTO> retailerList = retailerBatchCreateDto.getRetailerList();
        if (CollectionUtils.isEmpty(retailerList)) {
            return;
        }
        // 若retailerList不为空，则根据retailerList来更新开关
        Map<String, IntlSoRuleRetailer> dbRetailerMap =
            dbRetailerList.stream().collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));
        final long currentTime = DateUtils.getNowDate().getTime();
        // 此部分表示本次修改流程中，只修改了部分零售商的开关
        List<SoRuleRetailerItemDTO> newSoRuleRetailers = Lists.newArrayList();
        retailerList.forEach(retailer -> {
            IntlSoRuleRetailer dbRetailer = dbRetailerMap.get(retailer.getRetailerCode());
            if (Objects.nonNull(dbRetailer)) {
                dbRetailer.setImeiSwitch(retailer.getImeiSwitch());
                dbRetailer.setQtySwitch(retailer.getQtySwitch());
                dbRetailer.setUpdatedAt(currentTime);
                dbRetailer.setUpdatedBy(String.valueOf(userInfo.getMiID()));
            } else {
                newSoRuleRetailers.add(retailer);
            }
        });
        // 更新部分
        if (CollectionUtils.isNotEmpty(dbRetailerList)) {
            intlSoRuleRetailerService.updateBatchById(dbRetailerList);
        }
        // 新增部分
        if (CollectionUtils.isNotEmpty(newSoRuleRetailers)) {
            List<IntlSoRuleRetailer> dbMasterRetailers = intlSoRuleRetailerService
                .getByCountryCodeAndRetailerCodes(countryCode, SoRuleEnum.MASTER.getValue(), retailerCodes);
            Map<String, IntlSoRuleRetailer> retailerMap = dbMasterRetailers.stream()
                .collect(Collectors.toMap(IntlSoRuleRetailer::getRetailerCode, Function.identity()));
            List<IntlSoRuleRetailer> newRetailerList = newSoRuleRetailers.stream().map(it -> {
                IntlSoRuleRetailer dbRetailer = retailerMap.get(it.getRetailerCode());
                if (Objects.isNull(dbRetailer)) {
                    return null;
                }
                return ConvertUtils.copySoRuleRetailer(dbRetailer, it.getImeiSwitch(), it.getQtySwitch(),
                    String.valueOf(userInfo.getMiID()), currentTime);
            }).filter(Objects::nonNull).collect(Collectors.toList());
            intlSoRuleRetailerService.saveBatch(newRetailerList);
        }
    }

    /**
     * 修改场景：初始化零售商规则副本数据:<br/>
     * > 要保持主数据不动，对主规则的修改及零售商规则的修改，都生成副本数据，保存到表intl_so_rule_retailer中，category=COPY；<br/>
     * > 同时也保持一个国家一套零售商规则副本数据；<br/>
     * > 主规则副本保存于intl_so_rule_detail_log中；<br/>
     * > 当审批通过后，将本次修改的规则数据更新到主规则；将零售商规则副本数据，更新到零售商主规则intl_so_rule_retailer中，category=MASTER；<br/>
     *
     * @param retailerBatchCreateDto 批量创建零售商请求参数
     * @param userInfo 登录用户
     */
    private void initModifyRetailerList(SoRuleBaseReqDTO retailerBatchCreateDto, UserInfo userInfo) {
        String countryCode = retailerBatchCreateDto.getCountryCode();
        try (DistributionLock ignore = distributionLockService.tryLock("so.rule.retailer.init.lock-{0}", countryCode)) {
            Tuple imeiQtySwitch =
                getImeiAndQtySwitch(retailerBatchCreateDto.getAllAddImei(), retailerBatchCreateDto.getAllAddQty());
            SwitchEnum imeiSwitchEnum = imeiQtySwitch.get(0);
            SwitchEnum qtySwitchEnum = imeiQtySwitch.get(1);
            Integer imeiSwitch =
                Objects.nonNull(imeiSwitchEnum) ? imeiSwitchEnum.getValue() : SwitchEnum.OFF.getValue();
            Integer qtySwitch = Objects.nonNull(qtySwitchEnum) ? qtySwitchEnum.getValue() : SwitchEnum.OFF.getValue();
            // 查询当前国家下规则变更，零售商规则副本是否有全量初始化，若没有则进行初始化
            boolean isInitialized = intlSoRuleRetailerService.isInitializedModifyRetailers(countryCode);
            if (isInitialized) {
                return;
            }
            long current = DateUtils.getNowDate().getTime();
            // 流式查询零售商规则主数据，并生成变更副本；分批次保存
            List<IntlSoRuleRetailer> batch = Lists.newArrayList();
            List<String> excludeRetailerCodes = retailerBatchCreateDto.getRetailerList().stream()
                .map(SoRuleRetailerItemDTO::getRetailerCode).collect(Collectors.toList());
            intlSoRuleRetailerService.initModifyRetailers(countryCode, excludeRetailerCodes, resultContext -> {
                // 复制零售商规则主数据，生成副本数据
                IntlSoRuleRetailer masterRetailer = resultContext.getResultObject();
                IntlSoRuleRetailer copy = ConvertUtils.copySoRuleRetailer(masterRetailer, imeiSwitch, qtySwitch,
                    String.valueOf(userInfo.getMiID()), current);
                batch.add(copy);
                if (batch.size() >= IService.DEFAULT_BATCH_SIZE) {
                    intlSoRuleRetailerService.saveBatch(batch);
                    batch.clear();
                }
            });
            // 最后一个批次保存
            if (CollectionUtils.isNotEmpty(batch)) {
                intlSoRuleRetailerService.saveBatch(batch);
            }
        }
    }

    /**
     * 根据 addAllImei、addAllQty 来获取开关值
     *
     * @param allAddImei 全量添加imei规则
     * @param allAddQty 全量添加qty规则
     */
    public Tuple getImeiAndQtySwitch(Integer allAddImei, Integer allAddQty) {
        SwitchEnum imeiSwitch = null;
        SwitchEnum qtySwitch = null;
        if (Objects.equals(allAddImei, 1)) {
            // 生效所有零售商的imei规则
            imeiSwitch = SwitchEnum.ON;
        } else if (Objects.equals(allAddImei, 2)) {
            // 关闭所有零售商的imei规则
            imeiSwitch = SwitchEnum.OFF;
        }
        if (Objects.equals(allAddQty, 1)) {
            // 生效所有零售商的qty规则
            qtySwitch = SwitchEnum.ON;
        } else if (Objects.equals(allAddQty, 2)) {
            // 关闭所有零售商的qty规则
            qtySwitch = SwitchEnum.OFF;
        }
        return new Tuple(imeiSwitch, qtySwitch);
    }

    private BpmUser getApproveId(SoRuleApproveCallbackDTO approveCallback) {
        BpmUser operator = Objects.nonNull(approveCallback.getAssignee()) ? approveCallback.getAssignee()
            : approveCallback.getOperator();
        return Objects.nonNull(operator) ? operator : new BpmUser();
    }

    /**
     * 审批回调，根据审批状态，更新规则副本记录和主规则记录的审批状态。
     *
     * @param approveId 审批人ID
     * @param ruleDetailLog 规则副本记录（db查询）
     * @param ruleDetailLogStatus 审批状态
     * @param masterRule 主规则记录（db查询）
     * @param mastRuleStatus 主规则记录审批状态
     */
    private void updateRuleApproveStatus(String approveId, IntlSoRuleDetailLog ruleDetailLog,
        SoRuleDetailApproveStatus ruleDetailLogStatus, IntlSoRuleDetail masterRule,
        SoRuleDetailApproveStatus mastRuleStatus) {
        // 更新修改规则副本记录审批状态为审批拒绝
        Long current = DateUtils.getNowDate().getTime();
        ruleDetailLog.setStatus(ruleDetailLogStatus.getValue());
        ruleDetailLog.setUpdatedAt(current);
        ruleDetailLog.setUpdatedBy(approveId);
        intlSoRuleChangeLogService.updateById(ruleDetailLog);
        // 更新规则主数据审批状态为审批通过（还原状态）
        masterRule.setStatus(mastRuleStatus.getValue());
        masterRule.setUpdatedAt(current);
        masterRule.setUpdatedBy(approveId);
        intlSoRuleDetailService.updateById(masterRule);
    }

    /**
     * 构建规则明细记录
     *
     * @param soRuleDetailCreateDto 规则创建参数
     * @param userInfo 登录用户账号
     * @return 规则明细记录
     */
    private IntlSoRuleDetail buildSoRuleDetail(SoRuleDetailCreateDTO soRuleDetailCreateDto, UserInfo userInfo) {
        SoRuleEnum soRuleEnum =
            intlSoRuleRetailerService.getRetailerCategoryByCreateType(soRuleDetailCreateDto.getType());
        // 国家信息，前面已经校验过，此处不可能为null。
        CountryDTO country = countryTimeZoneApiService.getCountryInfoFromCache(soRuleDetailCreateDto.getCountryCode())
            .orElse(new CountryDTO());

        // 格式化规则为json数据
        String defaultRetailerSwitchJson = JSON.toJSONString(soRuleDetailCreateDto.getDefaultRetailersSwitch());
        String imeiRuleJson = JSON.toJSONString(soRuleDetailCreateDto.getImeiRuleList());
        String photoRuleJson = JSON.toJSONString(soRuleDetailCreateDto.getPhotoRuleList());
        // 零售商规则统计
        String operatorId = Objects.equals(soRuleEnum, SoRuleEnum.MASTER) ? null : String.valueOf(userInfo.getMiID());
        SoRuleRetailerStatisticsDTO retailerStatistics = intlSoRuleRetailerService
            .getRetailerStatistics(soRuleDetailCreateDto.getCountryCode(), soRuleEnum.getValue(), operatorId);

        long currentTime = DateUtils.getNowDate().getTime();

        // 创建规则明细记录
        IntlSoRuleDetail intlSoRuleDetail =
            ComponentLocator.getConverter().convert(soRuleDetailCreateDto, IntlSoRuleDetail.class);
        // 新创建的规则，默认状态为已审批
        intlSoRuleDetail.setRegionCode(country.getAreaCode());
        intlSoRuleDetail.setCountryCode(country.getCountryCode());
        intlSoRuleDetail.setEffectiveTime(currentTime);
        intlSoRuleDetail.setStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
        intlSoRuleDetail.setImeiRuleList(imeiRuleJson);
        intlSoRuleDetail.setPhotoRuleList(photoRuleJson);
        intlSoRuleDetail.setDefaultRetailersSwitch(defaultRetailerSwitchJson);
        intlSoRuleDetail.setTotalRetailersCount(retailerStatistics.getTotalRetailersCount());
        intlSoRuleDetail.setNoRuleRetailersCount(retailerStatistics.getNoRuleRetailersCount());
        intlSoRuleDetail.setImeiRetailersCount(retailerStatistics.getImeiRetailersCount());
        intlSoRuleDetail.setQtyRetailersCount(retailerStatistics.getQtyRetailersCount());
        intlSoRuleDetail.setCreatedAt(currentTime);
        intlSoRuleDetail.setCreatedBy(String.valueOf(userInfo.getMiID()));
        intlSoRuleDetail.setCreatedByName(AESGCMUtil.encryptGCM(userInfo.getUserName()));
        intlSoRuleDetail.setUpdatedAt(currentTime);
        intlSoRuleDetail.setUpdatedBy(String.valueOf(userInfo.getMiID()));
        intlSoRuleDetail.setUpdatedByName(AESGCMUtil.encryptGCM(userInfo.getUserName()));

        return intlSoRuleDetail;
    }

    /**
     * 初始化审批节点状态，审批人名称加密
     *
     * @param soRuleDetailModifyDto 修改参数
     * @param intlSoRuleDetailLog 变更记录
     */
    private void initApproverList(SoRuleDetailModifyDTO soRuleDetailModifyDto,
        IntlSoRuleDetailLog intlSoRuleDetailLog) {
        List<ApproverDTO> approverList = soRuleDetailModifyDto.getApproverList();
        if (CollectionUtils.isEmpty(approverList)) {
            return;
        }
        for (int i = 0; i < approverList.size(); i++) {
            ApproverDTO approver = approverList.get(i);
            if (i == 0) {
                // 第一个节点为发起节点，状态自动为审批通过
                approver.setStatus(SoRuleDetailApproveStatus.APPROVED.getValue());
                approver.setStatusLabel(SoRuleDetailApproveStatus.APPROVED.getDesc());
                approver.setApproveTime(intlSoRuleDetailLog.getCreatedAt());
            } else {
                approver.setStatus(SoRuleDetailApproveStatus.PENDING.getValue());
                approver.setStatusLabel(SoRuleDetailApproveStatus.PENDING.getDesc());
            }
            for (ApproverInfo approverInfo : approver.getApproverList()) {
                approverInfo.setUserName(AESGCMUtil.encryptGCM(approverInfo.getUserName()));
            }
        }
        intlSoRuleDetailLog.setApproverList(JSON.toJSONString(approverList));
    }
}
