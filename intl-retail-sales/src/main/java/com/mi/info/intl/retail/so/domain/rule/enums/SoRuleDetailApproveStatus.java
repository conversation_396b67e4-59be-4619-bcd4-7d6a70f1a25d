package com.mi.info.intl.retail.so.domain.rule.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import com.google.common.collect.Lists;

import lombok.Getter;

/**
 * 规则审批状态枚举
 *
 * <AUTHOR>
 * @date 2025/7/24 10:10
 */
@Getter
public enum SoRuleDetailApproveStatus {

    /**
     * 草稿，新建
     */
    CREATE(0, "Create"),

    /**
     * 审批中
     */
    PENDING(1, "Pending"),

    /**
     * 审批通过
     */
    APPROVED(2, "Approved"),

    /**
     * 审批拒绝
     */
    REJECTED(3, "Rejected"),

    /**
     * 审批撤回
     */
    RECALLED(4, "Recalled"),;

    /**
     * 审批状态值
     */
    private final int value;

    /**
     * 审批状态描述
     */
    private final String desc;

    SoRuleDetailApproveStatus(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    /**
     * 审批中状态列表
     */
    public static final List<Integer> HAS_PENDING_APPROVAL_YES = Lists.newArrayList(PENDING.value);

    /**
     * 非审批中状态列表
     */
    public static final List<Integer> HAS_PENDING_APPROVAL_NO =
            Lists.newArrayList(APPROVED.value, REJECTED.value, RECALLED.value);

    /**
     * 根据审批状态值获取审批状态枚举
     *
     * @param approveStatus 审批状态值
     * @return 审批状态枚举
     */
    public static SoRuleDetailApproveStatus parse(Integer approveStatus) {
        return Arrays.stream(SoRuleDetailApproveStatus.values())
                .filter(it -> Objects.equals(it.getValue(), approveStatus)).findFirst().orElse(null);
    }

    /**
     * 根据审批状态描述获取审批状态枚举
     *
     * @param desc 审批状态描述
     * @return 审批状态枚举
     */
    public static SoRuleDetailApproveStatus parseByDesc(String desc) {
        if (desc == null) {
            return null;
        }
        return Arrays.stream(SoRuleDetailApproveStatus.values()).filter(it -> Objects.equals(it.getDesc(), desc))
                .findFirst().orElse(null);
    }

    /**
     * 根据审批状态值获取审批状态描述
     *
     * @param approveStatus 审批状态值
     * @return 审批状态描述
     */
    public static String getDescByValue(Integer approveStatus) {
        SoRuleDetailApproveStatus status = parse(approveStatus);
        return status != null ? status.getDesc() : null;
    }

    /**
     * 根据审批状态描述获取审批状态值
     *
     * @param desc 审批状态描述
     * @return 审批状态值
     */
    public static Integer getValueByDesc(String desc) {
        SoRuleDetailApproveStatus status = parseByDesc(desc);
        return status != null ? status.getValue() : null;
    }

}
