package com.mi.info.intl.retail.so.app.mq;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.mi.info.intl.retail.constant.CommonConstant;
import com.mi.info.intl.retail.service.DistributionLock;
import com.mi.info.intl.retail.service.DistributionLockService;
import com.mi.info.intl.retail.so.app.mq.dto.RmsSyncSoDataReqDto;
import com.mi.info.intl.retail.so.domain.datasync.RmsSyncSoDataManage;
import com.mi.info.intl.retail.so.domain.datasync.dto.StockDataSyncReqDto;
import com.mi.info.intl.retail.so.domain.datasync.entity.IntlDatasyncLog;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncDataTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.DataSyncOperateTypeTypeEnum;
import com.mi.info.intl.retail.so.domain.datasync.enums.SyncStatusEnum;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.IntlDatasyncLogMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.datasync.RmsStockDataSyncMapper;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.redis.RedisKeyEnum;
import com.xiaomi.cnzone.commons.utils.JacksonUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.rocketmq.common.message.MessageExt;
import org.apache.rocketmq.spring.annotation.RocketMQMessageListener;
import org.apache.rocketmq.spring.core.RocketMQListener;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import javax.annotation.Resource;

/**
 * 多线程处理RMS同步数据消息的消费者
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@ConditionalOnProperty(name = "intl-retail.rocketmq.so-sync.enabled", havingValue = "true", matchIfMissing = true)
@RocketMQMessageListener(topic = "${intl-retail.rocketmq.so-sync.topic}",
        consumerGroup = "${intl-retail.rocketmq.so-sync.group}", enableMsgTrace = true)

public class RmsSyncSoDataConsumer implements RocketMQListener<MessageExt> {

    private static final String RMS_ID_KEY = "rmsId";
    private static final String RETAIL_ID_KEY = "retailId";
    private static final String IS_STOCK_KEY = "isStockData";
    private static final String ID_NEW_KEY = "idNew";

    @Resource
    private RmsSyncSoDataManage rmsSyncData;

    @Resource
    private IntlDatasyncLogMapper intlDatasyncLogMapper;

    @Resource
    private RmsStockDataSyncMapper rmsStockDataSyncMapper;

    @Resource
    private DistributionLockService distributionLockService;

    @Override
    public void onMessage(MessageExt messageExt) {
        try {
            // 1. 获取消息元信息
            String msgId = messageExt.getMsgId();
            int reconsumeTimes = messageExt.getReconsumeTimes();
            log.info("RmsSyncSoDataConsumer收到消息: msgId={}, 重试次数={}",
                    msgId, reconsumeTimes);

            // 2. 获取消息体字节数组
            byte[] body = messageExt.getBody();
            String bodyJson = null;
            try {
                bodyJson = new String(body, "UTF-8");
            } catch (UnsupportedEncodingException e) {
                throw new RuntimeException(e);
            }

            List<RmsSyncSoDataReqDto> dataList = JacksonUtil.parseArray(bodyJson, RmsSyncSoDataReqDto.class);
            try (DistributionLock ignored =
                         distributionLockService.tryLockNoArgs(
                                 RedisKeyEnum.LOCK_SYNC_DATA_CONSUMER.get(msgId).getKeyStr())) {
                if (dataList.size() > 1) {
                    handleBatchStockSync(dataList);
                } else {
                    RmsSyncSoDataReqDto data = dataList.get(0);
                    handleSingleStockSync(data);
                }
            }

        } catch (Exception e) {
            log.error("RmsSyncSoDataConsumer 处理消息失败: msgId={}, error={}", messageExt.getMsgId(), e.getMessage(),
                    e);
            throw new RuntimeException(" RmsSyncSoDataConsumer 消息处理失败", e);
        }
    }

    private void handleSingleStockSync(RmsSyncSoDataReqDto rmsSyncSoDataReqDto) {
        try {
            rmsSyncData.saveDb(rmsSyncSoDataReqDto);
        } catch (Exception e) {
            log.error("Error processing message: {}", rmsSyncSoDataReqDto, e);
            // 直接同步记录错误日志
            recordSyncLog(rmsSyncSoDataReqDto, e.getMessage());
            throw new RuntimeException("RmsSyncImeiAndQtyConsumer handleStockSync error", e);
        }
    }

    private void handleBatchStockSync(List<RmsSyncSoDataReqDto> rmsSyncSoDataReqDtos) {
        try {
            rmsSyncData.saveBatchDb(rmsSyncSoDataReqDtos);
        } catch (Exception e) {
            log.error("Error processing message: {}", rmsSyncSoDataReqDtos, e);
            // 直接同步记录错误日志
            rmsSyncSoDataReqDtos.forEach(rmsSyncSoDataReqDto -> {
                recordSyncLog(rmsSyncSoDataReqDto, e.getMessage());
            });
            throw new RuntimeException("RmsSyncImeiAndQtyConsumer handleBatchStockSync error", e);
        }
    }

    /**
     * 同步失败时记录错误日志
     *
     * @param reqDto 请求体
     * @param errorMessage 异常信息
     */
    private void recordSyncLog(RmsSyncSoDataReqDto reqDto, String errorMessage) {
        try {
            if (reqDto == null) {
                log.warn("RmsSyncSoDataReqDto is null, skip log recording.");
                return;
            }

            JSONObject data = reqDto.getData();
            if (data == null) {
                log.warn("Data is null in message for log recording: {}", reqDto);
                return;
            }

            String rmsId = data.getString(RMS_ID_KEY);
            String retailIdStr = data.getString(RETAIL_ID_KEY);
            Long retailId = parseRetailId(retailIdStr);

            Integer isStock = data.getInteger(IS_STOCK_KEY);
            String type = reqDto.getType();
            String operateType = reqDto.getOperateType();

            DataSyncDataTypeEnum dataTypeEnum = DataSyncDataTypeEnum.getEnumByMessage(type);
            DataSyncOperateTypeTypeEnum operateTypeTypeEnum = DataSyncOperateTypeTypeEnum.getEnumByMsg(operateType);

            IntlDatasyncLog intlDatasyncLog = new IntlDatasyncLog()
                    .setType(dataTypeEnum == null ? CommonConstant.DEFAULT_INTEGER : dataTypeEnum.getCode())
                    .setRmsId(rmsId)
                    .setRetailId(retailId)
                    .setMessage(JsonUtil.bean2json(reqDto))
                    .setStatus(SyncStatusEnum.FAILED.getCode())
                    .setErrorMessage(errorMessage)
                    .setOperateType(resolveOperateType(operateTypeTypeEnum));

            intlDatasyncLogMapper.insert(intlDatasyncLog);

            if (ObjectUtil.isNotNull(isStock) && isStock == 1) {
                handleStockSyncFailure(data, dataTypeEnum);
            }
        } catch (Exception e) {
            log.error("Failed to record sync log for message: {}", reqDto, e);
        }
    }

    private Long parseRetailId(String retailIdStr) {
        if (StringUtils.isEmpty(retailIdStr)) {
            return 0L;
        }
        try {
            return Long.parseLong(retailIdStr);
        } catch (NumberFormatException e) {
            log.warn("Invalid retailId format: {}", retailIdStr);
            return 0L;
        }
    }

    /**
     * 是存量数据同步时，修改同步状态为失败
     *
     * @param operateTypeTypeEnum 操作类型枚举
     * @return
     */
    private int resolveOperateType(DataSyncOperateTypeTypeEnum operateTypeTypeEnum) {
        if (operateTypeTypeEnum == null) {
            return CommonConstant.DEFAULT_INTEGER;
        }
        return !operateTypeTypeEnum.equals(DataSyncOperateTypeTypeEnum.CREATE)
                ? DataSyncOperateTypeTypeEnum.UPDATE.getCode()
                : operateTypeTypeEnum.getCode();
    }

    private void handleStockSyncFailure(JSONObject data, DataSyncDataTypeEnum dataTypeEnum) {
        if (dataTypeEnum == null) {
            log.warn("dataTypeEnum is null, skip stock sync failure handling.");
            return;
        }

        Long idNew = data.getLong(ID_NEW_KEY);
        if (idNew == null) {
            log.warn("idNew is missing in data for stock sync failure handling.");
            return;
        }

        StockDataSyncReqDto stockDataSyncReqDto =
                new StockDataSyncReqDto()
                        .setSyncStatus(SyncStatusEnum.FAILED.getCode())
                        .setSyncEndTime(LocalDateTime.now())
                        .setIdList(Collections.singletonList(idNew));

        try {
            if (dataTypeEnum.equals(DataSyncDataTypeEnum.IMEI)) {
                rmsStockDataSyncMapper.updateImeiSyncStatus(stockDataSyncReqDto);
            } else {
                rmsStockDataSyncMapper.updateQtySyncStatus(stockDataSyncReqDto);
            }
        } catch (Exception e) {
            log.error("Failed to update stock sync status for idNew: {}", idNew, e);
        }
    }

}
