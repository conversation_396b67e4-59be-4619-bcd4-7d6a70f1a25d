package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * SKU 可用性信息表
 *
 * @TableName intl_sku_available
 */
@TableName(value = "intl_sku_available")
@Data
public class IntlSkuAvailable {
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    private String countryCode;

    /**
     * 产品ID
     */
    @TableField(value = "product_code")
    private Long productCode;

    /**
     * 妥投时间
     */
    @TableField(value = "deliverytime")
    private Long deliverytime;

    /**
     * 销售时间
     */
    @TableField(value = "salestime")
    private Long salestime;
    private Integer status;
    private Long createdBy;
    private Long createdOn;
    private Long modifiedBy;
    private Long modifiedOn;
}