package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.country.dto.CountryDTO;
import com.mi.info.intl.retail.core.org.service.OrganizePlatformService;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextImeiUserDTO;
import com.mi.info.intl.retail.intlretail.service.api.masterdata.dto.PlainTextUserRequest;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.DictSysDTO;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.request.DictSysRequest;
import com.mi.info.intl.retail.so.app.dto.FileAddrRequest;
import com.mi.info.intl.retail.so.app.rpc.FileApiService;
import com.mi.info.intl.retail.so.app.rpc.dto.FileAddressDTO;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoPlaintextImeiUser;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoPlaintextImeiUserService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoPlaintextImeiUserMapper;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import com.xiaomi.nr.eiam.api.dto.userinfo.GetUserInfoResp;
import com.xiaomi.nr.eiam.api.dto.userinfo.Position;
import com.xiaomi.nr.eiam.common.enums.SceneEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_plaintext_imei_user(明文IMEI用户列表)】的数据库操作Service实现
 * @createDate 2025-07-31 17:15:13
 */
@Service
public class IntlSoPlaintextImeiUserServiceImpl
        extends ServiceImpl<IntlSoPlaintextImeiUserMapper, IntlSoPlaintextImeiUser>
        implements IntlSoPlaintextImeiUserService {

    @Resource
    private FileApiService fileApiService;

    @Resource
    private OrganizePlatformService organizePlatformService;
    @Resource
    private IntlSysDictService dictService;


    @Override
    public IPage<PlainTextImeiUserDTO> pageList(PlainTextUserRequest request) {
        Page<PlainTextImeiUserDTO> page = new Page<>(request.getPageNum(), request.getPageSize());
        request.setOffset((request.getPageNum() - 1) * request.getPageSize());
        int count = this.baseMapper.countPageList(request);
        List<PlainTextImeiUserDTO> plainTextImeiUserDTO = this.baseMapper.selectPageList(request);

        //为空直接返回
        if (CollectionUtils.isEmpty(plainTextImeiUserDTO)) {
            page.setTotal(count);
            return page;
        }

        // 提取照片ID列表
        Set<Long> photoIdList = plainTextImeiUserDTO.stream()
                .map(PlainTextImeiUserDTO::getPhotoIds)
                .filter(Objects::nonNull)
                .filter(s -> !s.isEmpty())
                .flatMap(s -> Arrays.stream(s.split(",")))
                .map(String::trim)
                .map(Long::parseLong)
                .collect(Collectors.toSet());

        // 提取用户账户列表
        Set<Long> userAccountList = plainTextImeiUserDTO.stream()
                .map(PlainTextImeiUserDTO::getUserAccount)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());

        // 批量获取用户信息
        List<GetUserInfoResp> userInfoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(userAccountList)) {
            userInfoList = organizePlatformService
                    .getUserInfoList(new ArrayList<>(userAccountList), SceneEnum.NEW_RETAIL.getScene());
        }

        Map<Long, GetUserInfoResp> userInfoRespMap = userInfoList.stream()
                .collect(Collectors.toMap(GetUserInfoResp::getMiId, Function.identity(), (v1, v2) -> v1));

        // 获取文件地址映射
        Map<Long, String> addrMap = Collections.emptyMap();
        if (CollectionUtils.isNotEmpty(photoIdList)) {
            FileAddrRequest fileAddrQryReq = new FileAddrRequest();
            fileAddrQryReq.setIds(new ArrayList<>(photoIdList));
            List<FileAddressDTO> fileAddressDTOS = fileApiService.getDownLoadAddr(fileAddrQryReq);
            addrMap = fileAddressDTOS.stream()
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(FileAddressDTO::getId, FileAddressDTO::getUrl, (v1, v2) -> v1));
        }

        // 获取职位字典映射
        DictSysRequest dictSysRequest = new DictSysRequest();
        DictSysDTO dictSysDTO = new DictSysDTO();
        dictSysDTO.setDictCode("NewJobtitle");
        dictSysDTO.setDictType("NewJobtitle");
        dictSysRequest.setDictCodeList(Collections.singletonList(dictSysDTO));

        Map<String, String> jobTitleMap = Collections.emptyMap();
        Map<String, List<LabelValueDTO>> labelValueListByDictCode = dictService.getLabelValueListByDictCode(dictSysRequest);
        if (labelValueListByDictCode != null && !labelValueListByDictCode.isEmpty()) {
            jobTitleMap = labelValueListByDictCode.values().stream()
                    .filter(Objects::nonNull)
                    .flatMap(List::stream)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle, (v1, v2) -> v1));
        }

        // 处理每个用户数据
        for (PlainTextImeiUserDTO textImeiUserDTO : plainTextImeiUserDTO) {
            // 处理创建时间
            textImeiUserDTO.setCreatedOn(
                    IntlTimeUtil.parseTimestampToAreaTime(request.getLocale(),
                            Long.valueOf(textImeiUserDTO.getCreatedOn())));

            // 处理创建人名称
            String createdByName = String.format("%s(%s)",
                    textImeiUserDTO.getCreatedByName(), textImeiUserDTO.getCreatedBy());
            textImeiUserDTO.setCreatedByName(createdByName);

            // 处理用户信息
            GetUserInfoResp userInfoResp = userInfoRespMap.get(textImeiUserDTO.getUserAccount());
            if (userInfoResp != null) {

                // 处理用户职位
                Set<Integer> positionIds = userInfoResp.getPositionList().stream()
                        .filter(Objects::nonNull)
                        .map(Position::getPositionId)
                        .collect(Collectors.toSet());

                String jobTitles = positionIds.stream()
                        .map(String::valueOf)
                        .map(jobTitleMap::get)
                        .filter(Objects::nonNull)
                        .collect(Collectors.joining(", "));

                textImeiUserDTO.setUserTitle(jobTitles);
            }

            // 构建照片URL列表
            buildPhotoUrls(textImeiUserDTO, addrMap);
        }

        page.setTotal(count);
        page.setRecords(plainTextImeiUserDTO);
        return page;
    }


    /**
     * 构建图片链接
     *
     * @param textImeiUserDTO
     * @param addrMap
     */
    private void buildPhotoUrls(PlainTextImeiUserDTO textImeiUserDTO, Map<Long, String> addrMap) {
        // 解析photoIds字符串并从map中获取对应的图片链接
        String photoIds = textImeiUserDTO.getPhotoIds();
        if (StringUtils.isNotEmpty(photoIds)) {
            List<String> photoUrls = Arrays.stream(photoIds.split(","))
                    .map(String::trim)
                    .map(id -> addrMap.getOrDefault(Long.valueOf(id), ""))
                    .filter(StringUtils::isNotEmpty)
                    .collect(Collectors.toList());
            textImeiUserDTO.setPhotoUrlList(photoUrls);
        }
    }
}




