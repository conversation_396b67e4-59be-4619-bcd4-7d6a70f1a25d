package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.SkuAvailableDetailList;
import com.mi.info.intl.retail.intlretail.service.api.so.sys.dto.SkuAvailableSearch;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSkuAvailable;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSkuAvailableService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSkuAvailableMapper;
import com.mi.info.intl.retail.so.infra.dto.IntlSkuAvailableDetail;
import com.mi.info.intl.retail.utils.UserInfoUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @description 针对表【intl_sku_available(SKU 可用性信息表)】的数据库操作Service实现
 * @createDate 2025-07-24 20:05:34
 */
@Slf4j
@Service
public class IntlSkuAvailableServiceImpl extends ServiceImpl<IntlSkuAvailableMapper, IntlSkuAvailable>
        implements IntlSkuAvailableService {

    @Resource
    private IntlSkuAvailableMapper intlSkuAvailableMapper;

    @Override
    public List<SkuAvailableDetailList> getDataBySearch(SkuAvailableSearch search) {

        int pageNum = search.getPageNum() < 1 ? 1 : search.getPageNum();
        int startNum = (pageNum - 1) * search.getPageSize();
        List<IntlSkuAvailableDetail> data =
                intlSkuAvailableMapper.getDataBySearch(startNum, search.getPageSize(), search);
        return this.convertToDataDto(data);
    }

    @Override
    public int getCountBySearch(SkuAvailableSearch search) {

        return intlSkuAvailableMapper.getCountBySearch(search);
    }

    private List<SkuAvailableDetailList> convertToDataDto(List<IntlSkuAvailableDetail> data) {

        String areaId = UserInfoUtil.getUserContext().getAreaId();
        log.info("UserInfoUtil_AreaId: {}", UserInfoUtil.getUserContext());

        return data.stream().map(item -> {
            SkuAvailableDetailList dto = new SkuAvailableDetailList();
            dto.setId(item.getId());
            dto.setProductId(item.getProductId());
            dto.setCountryCode(item.getCountryCode());
            dto.setCountryName(item.getCountryName());
            dto.setSalestime(IntlTimeUtil.parseTimestampToAreaTime(areaId, item.getSalestime()));
            dto.setDeliverytime(IntlTimeUtil.parseTimestampToAreaTime(areaId, item.getDeliverytime()));
            dto.setSkuId(item.getSkuId());
            dto.setSkuName(item.getSkuName());
            dto.setShortname(item.getShortname());
            dto.setModelLevel(item.getModelLevel());
            dto.setCode69(item.getCode69());
            dto.setIsSn(item.getIsSn());
            dto.setIsSnDesc(item.getIsSn() == 1 ? "Yes" : "No");
            dto.setIs69(item.getIs69());
            dto.setIs69Desc(item.getIs69() == 1 ? "Yes" : "No");
            dto.setSpuId(item.getSpuId());
            dto.setSpuEn(item.getSpuEn());
            dto.setCategoryEn(item.getCategoryEn());
            dto.setProductLine(item.getProductLine());
            dto.setProductLineEn(item.getProductLineEn());
            dto.setStatusDesc(System.currentTimeMillis() > item.getSalestime() ? "Saleable" : "Unsaleable");
            return dto;
        }).collect(Collectors.toList());
    }

}




