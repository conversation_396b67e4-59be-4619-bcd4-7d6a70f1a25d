package com.mi.info.intl.retail.so.domain.upload.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description 针对表【intl_so_org_info(销量阵地信息)】的数据库操作Service实现
 * @createDate 2025-07-25 16:40:50
 */
@Service
public class IntlSoOrgInfoServiceImpl extends ServiceImpl<IntlSoOrgInfoMapper, IntlSoOrgInfo>
        implements IntlSoOrgInfoService {

    @Autowired
    private IntlSoOrgInfoMapper intlSoOrgInfoMapper;

    @Override
    public List<IntlSoOrgInfo> batchGetByIds(List<Long> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return Collections.emptyList();
        }
        return baseMapper.selectBatchIds(idList);
    }

    @Override
    public Map<Long, IntlSoOrgInfo> batchGetByIdsMap(List<Long> idList) {
        List<IntlSoOrgInfo> list = baseMapper.selectBatchIds(idList);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.emptyMap();
        }
        return list.stream().collect(Collectors.toMap(IntlSoOrgInfo::getId, item -> item));
    }

    @Override
    public Long createOrgInfo(PositionStoreInfoDTO positionStoreInfo, String countryCode) {
        // 先查询是否存在完全相同的数据
        QueryWrapper<IntlSoOrgInfo> queryWrapper = buildQueryWrapper(positionStoreInfo, countryCode);
        IntlSoOrgInfo existingOrgInfo = getOne(queryWrapper);

        if (existingOrgInfo != null) {
            return existingOrgInfo.getId();
        }

        // 创建新的门店信息
        IntlSoOrgInfo orgInfo = getIntlSoOrgInfo(positionStoreInfo, countryCode);
        save(orgInfo);
        return orgInfo.getId();
    }
    /**
     * 构建查询条件
     */
    private QueryWrapper<IntlSoOrgInfo> buildQueryWrapper(PositionStoreInfoDTO dto, String countryCode) {
        QueryWrapper<IntlSoOrgInfo> queryWrapper = new QueryWrapper<>();

        queryWrapper.eq("store_changelog_id", 0L)
                .eq("store_id", getValueOrDefault(dto.getStoreId(), 0L))
                .eq("store_rms_code", getValueOrDefault(dto.getStoreCode(), ""))
                .eq("store_grade", getValueOrDefault(dto.getStoreGrade(), 0))
                .eq("store_type", getValueOrDefault(dto.getStoreType(), 0))
                .eq("store_channel_type", getValueOrDefault(dto.getStoreChannelType(), 0))
                .eq("store_hasSR", getValueOrDefault(dto.getStoreHasSR(), 0))
                .eq("store_hasPC", getValueOrDefault(dto.getStoreHasPC(), 0))
                .eq("country_code", getValueOrDefault(countryCode, ""))
                .eq("position_changelog_id", 0L)
                .eq("position_id", getValueOrDefault(dto.getPositionId(), 0L))
                .eq("position_rms_code", getValueOrDefault(dto.getPositionCode(), ""))
                .eq("position_type", getValueOrDefault(dto.getPositionType(), 0))
                .eq("retailer_id", getValueOrDefault(dto.getRetailerId(), 0L))
                .eq("retailer_code", getValueOrDefault(dto.getRetailerCode(), ""))
                .eq("store_class", getValueOrDefault(dto.getStoreClass(), 0))
                .eq("store_code", getValueOrDefault(dto.getCrssCode(), ""))
                .eq("position_code", getValueOrDefault(dto.getCrpsCode(), ""))
                .last("limit 1");

        return queryWrapper;
    }

    @NotNull
    private IntlSoOrgInfo getIntlSoOrgInfo(PositionStoreInfoDTO dto, String countryCode) {
        IntlSoOrgInfo orgInfo = new IntlSoOrgInfo();

        orgInfo.setStoreChangelogId(0L);
        orgInfo.setStoreId(getValueOrDefault(dto.getStoreId(), 0));
        orgInfo.setStoreCode(getValueOrDefault(dto.getCrssCode(), ""));
        orgInfo.setStoreGrade(getValueOrDefault(dto.getStoreGrade(), 0));
        orgInfo.setStoreType(getValueOrDefault(dto.getStoreType(), 0));
        orgInfo.setStoreChannelType(getValueOrDefault(dto.getStoreChannelType(), 0));
        orgInfo.setStoreHasSR(getValueOrDefault(dto.getStoreHasSR(), 0));
        orgInfo.setStoreHasPC(getValueOrDefault(dto.getStoreHasPC(), 0));
        orgInfo.setCountryCode(getValueOrDefault(countryCode, ""));
        orgInfo.setPositionChangelogId(0L);
        orgInfo.setPositionId(getValueOrDefault(dto.getPositionId(), 0));
        orgInfo.setPositionCode(getValueOrDefault(dto.getCrpsCode(), ""));
        orgInfo.setPositionType(getValueOrDefault(dto.getPositionType(), 0));
        orgInfo.setRetailerId(getValueOrDefault(dto.getRetailerId(), 0L));
        orgInfo.setRetailerCode(getValueOrDefault(dto.getRetailerCode(), ""));
        orgInfo.setPositionRmsCode(getValueOrDefault(dto.getPositionCode(), ""));
        orgInfo.setStoreRmsCode(getValueOrDefault(dto.getStoreCode(), ""));
        orgInfo.setStoreClass(getValueOrDefault(dto.getStoreClass(), 0));

        return orgInfo;
    }

    /**
     * 处理null值的通用方法
     */
    private static <T> T getValueOrDefault(T value, T defaultValue) {
        return value != null ? value : defaultValue;
    }
}




