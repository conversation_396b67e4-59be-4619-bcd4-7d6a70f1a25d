package com.mi.info.intl.retail.so.app;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.mi.info.intl.retail.api.country.CountryTimeZoneApiService;
import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
import com.mi.info.intl.retail.api.fieldforce.user.dto.UserInfoDTO;
import com.mi.info.intl.retail.api.file.FileUploadApiService;
import com.mi.info.intl.retail.api.file.dto.PhotoDataInfoDTO;
import com.mi.info.intl.retail.api.front.position.IntlPositionApiService;
import com.mi.info.intl.retail.api.front.position.dto.PositionStoreInfoDTO;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.dto.LabelValueDTO;
import com.mi.info.intl.retail.intlretail.service.api.retailer.dto.CommonResponse;
import com.mi.info.intl.retail.intlretail.service.api.so.QtyService;
import com.mi.info.intl.retail.intlretail.service.api.so.dto.*;
import com.mi.info.intl.retail.so.app.mq.dto.RetailSyncToRmsInfo;
import com.mi.info.intl.retail.so.app.provider.enums.DataFromEnum;
import com.mi.info.intl.retail.so.domain.sales.enums.SalesDictEnum;
import com.mi.info.intl.retail.so.domain.sales.handler.SoSalesHandler;
import com.mi.info.intl.retail.so.domain.sys.service.IntlSysDictService;
import com.mi.info.intl.retail.so.domain.upload.entity.*;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoUserInfoService;
import com.mi.info.intl.retail.so.infra.database.dataobject.rule.IntlSoRuleDetail;
import com.mi.info.intl.retail.so.infra.database.mapper.rule.IntlSoRuleDetailMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlRmsRrpMapper;
import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoQtyMapper;
import com.mi.info.intl.retail.so.util.SalesTimeValidUtil;
import com.mi.info.intl.retail.utils.JsonUtil;
import com.mi.info.intl.retail.utils.intl.IntlTimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.spring.core.RocketMQTemplate;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.mi.info.intl.retail.so.domain.upload.service.IntlRmsRrpService;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * QTY服务实现类
 *
 * <AUTHOR>
 * @date 2025/7/28
 */
@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = QtyService.class)
public class IntlQtyServiceImpl implements QtyService {

    @Resource
    private IntlSoQtyMapper intlSoQtyMapper;

    @Resource
    private IntlSoRuleDetailMapper intlSoRuleDetailMapper;

    @Resource
    private IntlRmsRrpMapper intlRmsRrpMapper;

    @Resource
    private IntlSoOrgInfoService intlSoOrgInfoService;

    @Resource
    private IntlSoUserInfoService intlSoUserInfoService;

    @Resource
    private IntlSoQtyService intlSoQtyService;

    @Resource
    private IntlSysDictService intlSysDictService;

    @Resource
    private ProductApiService productApiService;

    @Resource
    private FileUploadApiService fileUploadApiService;

    @Resource
    private IntlPositionApiService intlPositionApiService;

    @Resource
    private UserApiService userApiService;

    @Resource
    private SoSalesHandler soSalesHandler;
    @Autowired
    private RocketMQTemplate rocketMQTemplate;
    @Resource
    private IntlRmsRrpService intlRmsRrpService;


    // 定义图片上传模块名称
    private static final String MODULE_NAME = "qty_upload";
    // 定义mq消息常量
    private static final String OPERATION_TYPE = "create";
    private static final String DATA_TYPE = "qty";

    @Value("${intl-retail.rocketmq.to-rms.topic}")
    private String topic;
    // 促销员职位列表
    private static final List<Long> PROMOTER_TITLES = Arrays.asList(500900001L, 100000027L, 100000026L);

    @Override
    public CommonResponse<GetSkuListResponse> getSkuList(GetSkuListRequest request) {
        log.info("getSkuList request: {}", request);

        try {
            // 参数校验
            if (request == null) {
                return new CommonResponse<>(400, "请求参数不能为空", null);
            }
            if (StringUtils.isBlank(request.getCountryCode())) {
                return new CommonResponse<>(400, "国家代码不能为空", null);
            }
            if (StringUtils.isBlank(request.getUserId())) {
                return new CommonResponse<>(400, "用户ID不能为空", null);
            }
            if (request.getMiId() == null) {
                return new CommonResponse<>(400, "用户MID不能为空", null);
            }
            if (request.getUserTitle() == null) {
                return new CommonResponse<>(400, "职位代码不能为空", null);
            }

            // 查询产品列表
            List<GetSkuListResponse.ProductInfo> productList = intlSoQtyMapper.getSkuList(
                    request.getCountryCode(),
                    request.getProductLineCode(), // 使用产品线参数
                    request.getShortName(),
                    request.getProductName(),
                    request.getSearch() // 添加模糊查询参数
            );

            // 从产品列表中提取产品线列表（去重）
            List<GetSkuListResponse.ProductLineInfo> productLineList = productList.stream()
                    .filter(product -> product.getProductLine() != null && !product.getProductLine().isEmpty())
                    .map(product -> {
                        GetSkuListResponse.ProductLineInfo productLineInfo = new GetSkuListResponse.ProductLineInfo();
                        productLineInfo.setName(product.getProductLine());
                        productLineInfo.setCode(product.getProductLineCode());
                        return productLineInfo;
                    })
                    .distinct()
                    .collect(Collectors.toList());
            
            // 按照指定优先级排序产品线列表
            productLineList = sortProductLineListByPriority(productLineList);

            // 从产品列表中提取产品简称列表（去重）
            List<GetSkuListResponse.ShortNameInfo> shortNameList = productList.stream()
                    .filter(product -> product.getShortName() != null && !product.getShortName().isEmpty())
                    .map(product -> {
                        GetSkuListResponse.ShortNameInfo shortNameInfo = new GetSkuListResponse.ShortNameInfo();
                        shortNameInfo.setName(product.getShortName());
                        return shortNameInfo;
                    })
                    .distinct()
                    .collect(Collectors.toList());

            // 构建响应
            GetSkuListResponse response = new GetSkuListResponse();
            response.setProductList(productList);
            response.setProductLineList(productLineList);
            response.setShortNameList(shortNameList);

            log.info("getSkuList response: productList size={}, productLineList size={}, shortNameList size={}",
                    productList.size(), productLineList.size(), shortNameList.size());

            return new CommonResponse<>(response);
        } catch (Exception e) {
            log.error("getSkuList error", e);
            return new CommonResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonResponse<GetFilterListResponse> getFilterList() {
        log.info("getFilterList start");

        try {
            GetFilterListResponse response = new GetFilterListResponse();

            // 查询产品线列表
            List<GetFilterListResponse.FilterItem> productLineList = queryFilterListByDictName("ProductLineEN");
            productLineList = sortFilterItemProductLineListByPriority(productLineList);
            response.setProductLineList(productLineList);

            // 查询门店类型列表
            response.setStoreTypeList(queryFilterListByDictName("StoreType"));

            // 查询渠道类型列表
            response.setChannelTypeList(queryFilterListByDictName("ChannelType"));

            log.info("getFilterList success: productLineList size={}, storeTypeList size={}, channelTypeList size={}",
                    response.getProductLineList().size(), response.getStoreTypeList().size(), response.getChannelTypeList().size());

            return new CommonResponse<>(response);
        } catch (Exception e) {
            log.error("getFilterList error", e);
            return new CommonResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonResponse<QueryQtyListResponse> queryQtyList(QueryQtyListRequest request) {
        log.info("queryQtyList start, request: {}", request);

        try {
            // 1. 必填字段校验
            CommonResponse<Object> validateResult = validateQueryQtyListRequest(request);
            if (validateResult != null) {
                return new CommonResponse<>(validateResult.getCode(), validateResult.getMessage(), null);
            }

            // 2. 判断用户职位，获取门店列表
            List<Long> userStoreList = getUserStoreList(request.getMiId(), request.getUserTitle());

            // 3. 查询QTY明细数据（分页）
            QueryQtyListResponse response = queryQtyDataListWithPage(request, userStoreList);

            log.info("queryQtyList success: detailList size={}, dateGroupList size={}",
                    response.getDetailList().size(), response.getDateGroupList().size());

            return new CommonResponse<>(response);
        } catch (Exception e) {
            log.error("queryQtyList error", e);
            return new CommonResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonResponse<QueryQtyStatisticsResponse> queryQtyStatistics(QueryQtyStatisticsRequest request) {
        log.info("queryQtyStatistics start, request: {}", request);

        try {
            // 1. 必填字段校验
            CommonResponse<Object> validateResult = validateQueryQtyStatisticsRequest(request);
            if (validateResult != null) {
                return new CommonResponse<>(validateResult.getCode(), validateResult.getMessage(), null);
            }

            // 2. 判断userTitle，执行相应查询逻辑
            List<Long> userStoreList = new ArrayList<>();
            if (!PROMOTER_TITLES.contains(request.getUserTitle())) {
                // 督导及其他角色，查询该用户关联的所有门店
                userStoreList = getUserStoreList(request.getMiId(), request.getUserTitle());
                if (userStoreList.isEmpty()) {
                    log.warn("用户没有关联的门店, miId: {}", request.getMiId());
                    return new CommonResponse<>(new QueryQtyStatisticsResponse());
                }
            }

            // 3. 查询QTY统计数据
            QueryQtyStatisticsResponse response = queryQtyStatisticsData(request, userStoreList);

            log.info("queryQtyStatistics success: totalCount={}, pcCount={}, appCount={}",
                    response.getTotalCount(), response.getPcCount(), response.getAppCount());

            return new CommonResponse<>(response);
        } catch (Exception e) {
            log.error("queryQtyStatistics error", e);
            return new CommonResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonResponse<GetQtyBoardDataResponse> getQtyBoardData(GetQtyBoardDataRequest request) {
        log.info("getQtyBoardData start, request: {}", request);

        try {
            // 1. 必填字段校验
            CommonResponse<Object> validateResult = validateGetQtyBoardDataRequest(request);
            if (validateResult != null) {
                return new CommonResponse<>(validateResult.getCode(), validateResult.getMessage(), null);
            }

            // 2. 计算时间范围（使用 countryCode 换算时间，参考查询接口逻辑）
            long startTime = getMonthStartTimeByCountryCode(request.getCountryCode());
            long endTime = System.currentTimeMillis();

            // 3. 获取IMEI和QTY销量数据（按日期分组）
            Map<Integer, Map<Integer, Integer>> imeiSalesData = getImeiSalesData(
                    request.getCountryCode(), request.getMiId(), startTime, endTime);
            Map<Integer, Map<Integer, Integer>> qtySalesData = getQtySalesData(
                    request.getCountryCode(), request.getMiId(), startTime, endTime);

            // 4. 合并IMEI和QTY数据
            Map<Integer, Map<Integer, Integer>> mergedSalesData = mergeSalesData(imeiSalesData, qtySalesData);

            // 5. 生成看板数据
            List<GetQtyBoardDataResponse.BoardData> boardDataList = generateBoardData(mergedSalesData, request.getCountryCode());

            GetQtyBoardDataResponse response = new GetQtyBoardDataResponse();
            response.setData(boardDataList);

            log.info("getQtyBoardData success: boardDataList size={}", boardDataList.size());

            return new CommonResponse<>(response);
        } catch (Exception e) {
            log.error("getQtyBoardData error", e);
            return new CommonResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    @Override
    public CommonResponse<QueryQtyDetailResponse> queryQtyDetail(QueryQtyDetailRequest request) {
        log.info("queryQtyDetail start, request: {}", request);

        try {
            // 1. 入参校验 - 参照IMEI实现
            if (StringUtils.isBlank(request.getQtyId()) ||
                    StringUtils.isBlank(request.getUserId()) ||
                    request.getMiId() == null ||
                    request.getUserTitle() == null) {
                return new CommonResponse<>(400, "必填参数不能为空", null);
            }

            // 2. 查询QTY明细数据 - 参照IMEI实现
            QueryQtyDetailResponse response = queryQtyDetailById(request.getQtyId());
            if (response == null) {
                return new CommonResponse<>(404, "QTY明细不存在", null);
            }

            // 3. 时区转换处理 - 参照IMEI实现
            if (StringUtils.isNotBlank(request.getCountryCode())) {
                try {
                    // 查询销售时间戳
                    Long salesTimeTimestamp = intlSoQtyMapper.queryQtySalesTime(request.getQtyId(), request.getMiId());
                    
                    if (salesTimeTimestamp != null) {
                        String salesTimeStr = IntlTimeUtil.parseTimestampToAreaTime(request.getCountryCode(), salesTimeTimestamp);
                        if (StringUtils.isNotBlank(salesTimeStr)) {
                            // 提取到分钟部分，格式：yyyy-MM-dd HH:mm
                            if (salesTimeStr.length() >= 16) {
                                response.setDate(salesTimeStr.substring(0, 16));
                            } else if (salesTimeStr.length() >= 10) {
                                response.setDate(salesTimeStr.substring(0, 10));
                            }
                        }
                    }
                    
                    // 处理创建时间 - 参照IMEI实现
                    if (response.getCreatedOn() != null) {
                        String createdOnStr = IntlTimeUtil.parseTimestampToFormatAreaTime("yyyy-MM-dd HH:mm:ss",
                                request.getCountryCode(),
                                response.getCreatedOn());
                        if (StringUtils.isNotBlank(createdOnStr)) {
                            response.setCreatedOn(Long.valueOf(createdOnStr));
                        }
                    }
                } catch (Exception e) {
                    log.error("时区转换失败", e);
                }
            }

                    // 5. 设置枚举标签 - 使用字典系统转换
        Integer reportType = response.getReportType();
        if (reportType != null) {
            try {
                Map<String, List<LabelValueDTO>> salesDictMap = soSalesHandler.getLabelValueList4Sales();
                List<LabelValueDTO> reportingTypeList = salesDictMap.get(SalesDictEnum.REPORTING_TYPE.getCode());
                if (reportingTypeList != null) {
                    Map<String, String> reportingTypeMap = reportingTypeList.stream()
                            .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
                    response.setReportTypeLabel(reportingTypeMap.get(String.valueOf(reportType)));
                }
            } catch (Exception e) {
                log.warn("获取字典转换失败，reportType: {}", reportType, e);
                response.setReportTypeLabel("");
            }
        }

            log.info("queryQtyDetail success: qtyId={}", request.getQtyId());

            return new CommonResponse<>(response);
        } catch (Exception e) {
            log.error("queryQtyDetail error", e);
            return new CommonResponse<>(500, "系统异常：" + e.getMessage(), null);
        }
    }

    /**
     * 根据ID查询QTY明细 - 参照IMEI实现
     */
    private QueryQtyDetailResponse queryQtyDetailById(String qtyId) {
        try {
            // 查询QTY明细数据
            QueryQtyDetailResponse response = intlSoQtyMapper.queryQtyDetail(
                    qtyId
            );

            if (response == null) {
                return null;
            }

            // 处理图片列表
            List<String> photoUrls = new ArrayList<>();

                List<QueryQtyDetailResponse.PhotoInfo> photoList = intlSoQtyMapper.queryQtyPhotoList(response.getDetailId());
                
                for (QueryQtyDetailResponse.PhotoInfo photo : photoList) {
                    if (StringUtils.isNotBlank(photo.getUrl())) {
                        photoUrls.add(photo.getUrl());
                    }
                }

            response.setPhotoUrls(photoUrls);

            return response;
        } catch (Exception e) {
            log.error("查询QTY明细失败, qtyId: {}", qtyId, e);
            return null;
        }
    }

    @Override
    public CommonResponse<GetStoreListResponse> getStoreList(GetStoreListRequest request) {
        log.info("getStoreList start, request: {}", request);

        try {
            // 1. 必填字段校验
            if (request == null) {
                return new CommonResponse<>(400, "请求参数不能为空", null);
            }
            if (StringUtils.isBlank(request.getCountryCode())) {
                return new CommonResponse<>(400, "国家代码不能为空", null);
            }
            if (StringUtils.isBlank(request.getUserId())) {
                return new CommonResponse<>(400, "用户GUID不能为空", null);
            }
            if (request.getMiId() == null) {
                return new CommonResponse<>(400, "用户miId不能为空", null);
            }
            if (request.getUserTitle() == null) {
                return new CommonResponse<>(400, "职位代码不能为空", null);
            }

            // 2. 查询门店列表
            List<GetStoreListResponse.StoreInfo> storeList = intlSoQtyMapper.getStoreList(
                    request.getMiId(),
                    request.getSearch()
            );

            // 3. 构建响应
            GetStoreListResponse response = new GetStoreListResponse();
            response.setStoreList(storeList);

            log.info("getStoreList success: storeList size={}", storeList.size());

            return new CommonResponse<>(response);
        } catch (Exception e) {
            log.error("getStoreList error", e);
            return new CommonResponse<>(500, "查询失败: " + e.getMessage(), null);
        }
    }

    private long getMonthStartTimeByCountryCode(String countryCode) {
        try {
            // 获取当前时间
            Calendar now = Calendar.getInstance();
            int year = now.get(Calendar.YEAR);
            int month = now.get(Calendar.MONTH) + 1; // 当前月份

            // 使用 IntlTimeUtil 获取月份开始时间戳
            Long[] monthTimestamps = IntlTimeUtil.getMonthStartAndEndTimestamp(countryCode, year, month);
            if (monthTimestamps != null && monthTimestamps.length >= 1) {
                return monthTimestamps[0];
            }
        } catch (Exception e) {
            log.warn("使用 IntlTimeUtil 计算本月开始时间失败", e);
        }

        // 备选方案
        return getMonthStartTime(System.currentTimeMillis());
    }

    /**
     * 获取本月开始时间戳（保持原有方法兼容性）
     */
    private long getMonthStartTime(long currentTime) {
        Calendar cal = Calendar.getInstance();
        cal.setTimeInMillis(currentTime);
        cal.set(Calendar.DAY_OF_MONTH, 1);
        cal.set(Calendar.HOUR_OF_DAY, 0);
        cal.set(Calendar.MINUTE, 0);
        cal.set(Calendar.SECOND, 0);
        cal.set(Calendar.MILLISECOND, 0);
        return cal.getTimeInMillis();
    }

    /**
     * 获取IMEI销量数据（参考 GetSalesSummary 逻辑）
     */
    private Map<Integer, Map<Integer, Integer>> getImeiSalesData(String countryCode, Long miId, long startTime, long endTime) {
        try {
            List<Map<String, Object>> results = intlSoQtyMapper.getImeiBoardDataByDate(countryCode, miId, startTime, endTime);
            
            // 初始化结果结构
            Map<Integer, Map<Integer, Integer>> salesData = new HashMap<>();
            Map<String, Integer> productLineMap = getProductLineMap();
            
            for (Integer productLine : productLineMap.values()) {
                salesData.put(productLine, new HashMap<>());
            }

            // 处理查询结果
            for (Map<String, Object> entity : results) {
                String productLineStr = getStringValue(entity.get("productLine"));
                Integer dayOfMonth = getIntegerValue(entity.get("dayOfMonth"));
                Integer count = getIntegerValue(entity.get("imeiCount"));

                if (productLineStr != null && dayOfMonth != null && count != null) {
                    Integer productLineValue = getProductLineValue(productLineStr);
                    if (productLineValue != null && salesData.containsKey(productLineValue)) {
                        salesData.get(productLineValue).put(dayOfMonth, count);
                    }
                }
            }

            return salesData;
        } catch (Exception e) {
            log.warn("获取IMEI销量数据失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 获取QTY销量数据（参考 GetSalesSummary 逻辑）
     */
    private Map<Integer, Map<Integer, Integer>> getQtySalesData(String countryCode, Long miId, long startTime, long endTime) {
        try {
            List<Map<String, Object>> results = intlSoQtyMapper.getQtyBoardDataByDate(countryCode, miId, startTime, endTime);
            
            // 初始化结果结构
            Map<Integer, Map<Integer, Integer>> salesData = new HashMap<>();
            Map<String, Integer> productLineMap = getProductLineMap();
            
            for (Integer productLine : productLineMap.values()) {
                salesData.put(productLine, new HashMap<>());
            }

            // 处理查询结果
            for (Map<String, Object> entity : results) {
                String productLineStr = getStringValue(entity.get("productLine"));
                Integer dayOfMonth = getIntegerValue(entity.get("dayOfMonth"));
                Integer qtySum = getIntegerValue(entity.get("qtySum"));

                if (productLineStr != null && dayOfMonth != null && qtySum != null) {
                    Integer productLineValue = getProductLineValue(productLineStr);
                    if (productLineValue != null && salesData.containsKey(productLineValue)) {
                        salesData.get(productLineValue).put(dayOfMonth, qtySum);
                    }
                }
            }

            return salesData;
        } catch (Exception e) {
            log.warn("获取QTY销量数据失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 合并IMEI和QTY销量数据（参考 GetSalesSummary 逻辑）
     */
    private Map<Integer, Map<Integer, Integer>> mergeSalesData(
            Map<Integer, Map<Integer, Integer>> imeiData,
            Map<Integer, Map<Integer, Integer>> qtyData) {
        
        Map<Integer, Map<Integer, Integer>> merged = new HashMap<>();

        // 获取所有产品线，将IMEI和QTY合并
        Set<Integer> allProductLines = new HashSet<>(imeiData.keySet());
        allProductLines.addAll(qtyData.keySet());

        // 处理每个产品线
        for (Integer productLine : allProductLines) {
            merged.put(productLine, new HashMap<>());

            // 获取IMEI产品线数据
            Map<Integer, Integer> imeiDates = imeiData.getOrDefault(productLine, new HashMap<>());
            // QTY产品线数据
            Map<Integer, Integer> qtyDates = qtyData.getOrDefault(productLine, new HashMap<>());

            // 汇总IMEI和QTY的日期
            Set<Integer> allDays = new HashSet<>(imeiDates.keySet());
            allDays.addAll(qtyDates.keySet());

            for (Integer day : allDays) {
                int imeiCount = imeiDates.getOrDefault(day, 0);
                int qtyCount = qtyDates.getOrDefault(day, 0);
                merged.get(productLine).put(day, imeiCount + qtyCount);
            }
        }
        
        return merged;
    }

    /**
     * 生成看板数据（参考 GetSalesSummary 逻辑）
     */
    private List<GetQtyBoardDataResponse.BoardData> generateBoardData(Map<Integer, Map<Integer, Integer>> mergedSalesData, String countryCode) {
        List<GetQtyBoardDataResponse.BoardData> boardDataList = new ArrayList<>();
        Map<String, Integer> productLineMap = getProductLineMap();
        
        // 使用 countryCode 获取当前时间信息
        Calendar now = getCurrentTimeByCountryCode(countryCode);
        int currentDay = now.get(Calendar.DAY_OF_MONTH);
        
        // 计算本周开始（周一）
        int dayOfWeek = now.get(Calendar.DAY_OF_WEEK);
        dayOfWeek = dayOfWeek == Calendar.SUNDAY ? 7 : dayOfWeek - 1; // 周日为7，其他减1
        int weekStartDay = currentDay - dayOfWeek + 1;
        if (weekStartDay < 1) weekStartDay = 1;

        // 为每个产品线计算销量
        for (Map.Entry<String, Integer> entry : productLineMap.entrySet()) {
            String productLineName = entry.getKey();
            Integer productLineCode = entry.getValue();
            
            Map<Integer, Integer> productData = mergedSalesData.getOrDefault(productLineCode, new HashMap<>());

            // 计算日销量
            int daySales = productData.getOrDefault(currentDay, 0);

            // 计算本周销量
            int weekSales = 0;
            for (int day = weekStartDay; day <= currentDay; day++) {
                weekSales += productData.getOrDefault(day, 0);
            }

            // 计算月销量
            int monthSales = productData.values().stream().mapToInt(Integer::intValue).sum();

            // 添加到结果
            GetQtyBoardDataResponse.BoardData boardData = new GetQtyBoardDataResponse.BoardData();
            boardData.setProductLine(productLineName);
            boardData.setSalesQtyDay(daySales);
            boardData.setSalesQtyWeek(weekSales);
            boardData.setSalesQtyMonth(monthSales);
            
            boardDataList.add(boardData);
        }

        return boardDataList;
    }

    /**
     * 获取产品线映射
     */
    private Map<String, Integer> getProductLineMap() {
        Map<String, Integer> productLineMap = new HashMap<>();
        productLineMap.put("Smartphones", 1);
        productLineMap.put("Pad", 21);
        productLineMap.put("Wearables", 12);
        return productLineMap;
    }

    /**
     * 根据产品线名称获取产品线代码
     */
    private Integer getProductLineValue(String productLineName) {
        Map<String, Integer> productLineMap = getProductLineMap();
        return productLineMap.get(productLineName);
    }

    /**
     * 使用 countryCode 获取当前时间（参考查询接口逻辑）
     */
    private Calendar getCurrentTimeByCountryCode(String countryCode) {
        try {
            // 获取当前UTC时间
            long currentUtcTime = System.currentTimeMillis();
            
            // 使用 countryCode 转换为当地时间的字符串
            String localTimeStr = IntlTimeUtil.parseTimestampToAreaTime(countryCode, currentUtcTime);
            
            if (StringUtils.isNotBlank(localTimeStr)) {
                // 解析当地时间字符串，格式：yyyy-MM-dd HH:mm:ss
                String[] parts = localTimeStr.split(" ")[0].split("-"); // 提取日期部分
                if (parts.length == 3) {
                    int year = Integer.parseInt(parts[0]);
                    int month = Integer.parseInt(parts[1]) - 1; // Calendar月份从0开始
                    int day = Integer.parseInt(parts[2]);
                    
                    // 创建当地时间的Calendar实例
                    Calendar localCal = Calendar.getInstance();
                    localCal.set(year, month, day, 0, 0, 0);
                    localCal.set(Calendar.MILLISECOND, 0);
                    
                    return localCal;
                }
            }
        } catch (Exception e) {
            log.warn("使用 countryCode 获取当前时间失败，使用系统时间作为备选", e);
        }
        
        // 备选方案：使用系统时间
        return Calendar.getInstance();
    }

    /**
     * 校验QTY看板数据查询请求参数
     */
    private CommonResponse<Object> validateGetQtyBoardDataRequest(GetQtyBoardDataRequest request) {
        if (request == null) {
            return new CommonResponse<>(400, "请求参数不能为空", null);
        }
        if (request.getCountryCode() == null || request.getCountryCode().trim().isEmpty()) {
            return new CommonResponse<>(400, "国家代码不能为空", null);
        }
        if (request.getUserId() == null || request.getUserId().trim().isEmpty()) {
            return new CommonResponse<>(400, "用户GUID不能为空", null);
        }
        if (request.getMiId() == null) {
            return new CommonResponse<>(400, "用户miId不能为空", null);
        }
        return null;
    }

    /**
     * 校验QTY明细页查询请求参数
     */
    private CommonResponse<Object> validateQueryQtyDetailRequest(QueryQtyDetailRequest request) {
        if (request == null) {
            return new CommonResponse<>(400, "请求参数不能为空", null);
        }
        if (request.getQtyId() == null || request.getQtyId().trim().isEmpty()) {
            return new CommonResponse<>(400, "QTY记录ID不能为空", null);
        }
        if (request.getUserId() == null || request.getUserId().trim().isEmpty()) {
            return new CommonResponse<>(400, "用户GUID不能为空", null);
        }
        if (request.getMiId() == null) {
            return new CommonResponse<>(400, "用户miId不能为空", null);
        }
        if (request.getUserTitle() == null) {
            return new CommonResponse<>(400, "职位code不能为空", null);
        }
        if (StringUtils.isBlank(request.getCountryCode())) {
            return new CommonResponse<>(400, "国家代码不能为空", null);
        }
        return null;
    }

    /**
     * 校验QTY统计查询请求参数
     */
    private CommonResponse<Object> validateQueryQtyStatisticsRequest(QueryQtyStatisticsRequest request) {
        if (request == null) {
            return new CommonResponse<>(400, "请求参数不能为空", null);
        }

        if (StringUtils.isBlank(request.getCountryCode())) {
            return new CommonResponse<>(400, "国家代码不能为空", null);
        }

        if (StringUtils.isBlank(request.getUserId())) {
            return new CommonResponse<>(400, "用户GUID不能为空", null);
        }

        if (request.getMiId() == null) {
            return new CommonResponse<>(400, "用户miId不能为空", null);
        }

        if (request.getUserTitle() == null) {
            return new CommonResponse<>(400, "职位代码不能为空", null);
        }

        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResponse<Object> submitQty(SubmitQtyReq request) {
        log.info("submitQty start, request: {}", request);

        try {
            // 1. 必填字段校验
            CommonResponse<Object> validateResult = validateQtyRequest(request);
            if (validateResult != null) {
                return validateResult;
            }


            // 2. 查询阵地和门店信息
            PositionStoreInfoDTO positionStoreInfo = queryPositionStoreInfo(request.getPositionCode());
            if (positionStoreInfo == null) {
                return new CommonResponse<>(400, "阵地或门店信息不存在", null);
            }

            // 3. 查询用户信息
            UserInfoDTO userInfo = queryUserInfo(request.getMiId());
            if (userInfo == null) {
                return new CommonResponse<>(400, "用户信息不存在", null);
            }

            // 4. 查询产品信息
            Map<String, ProductInfoDTO> productMap = queryProductInfo(request.getDetailList());
            if (productMap.isEmpty()) {
                return new CommonResponse<>(400, "产品信息不存在", null);
            }

            // 6. 查询产品价格信息
            Map<String, IntlRmsRrp> rrpMap = intlRmsRrpService.queryRrpInfo(productMap.values(), request.getCountryCode());
            //7. 创建相关数据
            List<IntlSoQty>  soQtylist = createQtyData(request, positionStoreInfo, userInfo, productMap, rrpMap);
            sendMqMessage(soQtylist);
            log.info("submitQty success");
            return new CommonResponse<>(null);

        } catch (Exception e) {
            log.error("submitQty error", e);
            return new CommonResponse<>(500, "系统异常：" + e.getMessage(), null);
        }
    }

    private void sendMqMessage(List<IntlSoQty> qtyList) {
        if (CollectionUtils.isEmpty(qtyList)) {
            return;
        }
        RetailSyncToRmsInfo retailSyncToRmsInfo = new RetailSyncToRmsInfo();
        retailSyncToRmsInfo.setOperateType(OPERATION_TYPE);
        retailSyncToRmsInfo.setDataType(DATA_TYPE);
        // 提取列表中的id，逗号分隔
        retailSyncToRmsInfo.setDataId(qtyList.stream().map(IntlSoQty::getId).map(String::valueOf)
                .collect(Collectors.joining(",")));
        final String reqStr = JsonUtil.bean2json(retailSyncToRmsInfo);

        // 2.发送mq
        try {
            final SendResult sendResult = rocketMQTemplate.syncSend(topic, reqStr);
            log.info("submitImei sendResult msgId:{}", sendResult.getMsgId());
        } catch (Exception e) {
            log.info("submitImei send failed reqStr:{}", reqStr);
        }
    }

    /**
     * 校验QTY请求参数
     */
    private CommonResponse<Object> validateQtyRequest(SubmitQtyReq request) {
        if (request == null) {
            return new CommonResponse<>(400, "请求参数不能为空", null);
        }


        if (StringUtils.isBlank(request.getCountryCode())) {
            return new CommonResponse<>(400, "国家编码不能为空", null);
        }

        if (StringUtils.isBlank(request.getPositionCode())) {
            return new CommonResponse<>(400, "阵地编码不能为空", null);
        }

        if (StringUtils.isBlank(request.getUserId())) {
            return new CommonResponse<>(400, "用户GUID不能为空", null);
        }

        if (request.getMiId() == null) {
            return new CommonResponse<>(400, "用户miId不能为空", null);
        }

        if (CollectionUtils.isEmpty(request.getDetailList())) {
            return new CommonResponse<>(400, "QTY明细不能为空", null);
        }

        // 校验明细字段
        for (SubmitQtyReq.QtyDetailDto detail : request.getDetailList()) {

            if (detail.getProductId() == null) {
                return new CommonResponse<>(400, "产品ID不能为空", null);
            }
            if (StringUtils.isBlank(detail.getQuantity())) {
                return new CommonResponse<>(400, "销售数量不能为空", null);
            }
            if (detail.getSalesTime() == null) {
                return new CommonResponse<>(400, "销售时间不能为空", null);
            }
            if (detail.getSalesTime() < 1000000000000L) {
                return new CommonResponse<>(400, "销售时间格式不正确", null);
            }
        }

        return null;
    }

    /**
     * 校验QTY列表请求参数
     */
    private CommonResponse<Object> validateQueryQtyListRequest(QueryQtyListRequest request) {
        if (request == null) {
            return new CommonResponse<>(400, "请求参数不能为空", null);
        }

        if (StringUtils.isBlank(request.getCountryCode())) {
            return new CommonResponse<>(400, "国家代码不能为空", null);
        }

        if (StringUtils.isBlank(request.getUserId())) {
            return new CommonResponse<>(400, "用户GUID不能为空", null);
        }

        if (request.getMiId() == null) {
            return new CommonResponse<>(400, "用户miId不能为空", null);
        }

        if (request.getUserTitle() == null) {
            return new CommonResponse<>(400, "职位代码不能为空", null);
        }

        return null;
    }

    /**
     * 查询SO上报规则
     */
    private IntlSoRuleDetail queryRuleDetail(Integer ruleId) {
        try {
            return intlSoRuleDetailMapper.selectById(ruleId);
        } catch (Exception e) {
            log.error("查询SO上报规则失败, ruleId: {}", ruleId, e);
            return null;
        }
    }

    /**
     * 查询阵地和门店信息
     */
    private PositionStoreInfoDTO queryPositionStoreInfo(String positionCode) {
        try {
            Optional<PositionStoreInfoDTO> result = intlPositionApiService.queryPositionWithStoreByCode(positionCode);
            return result.orElse(null);
        } catch (Exception e) {
            log.error("查询阵地门店信息失败, positionCode: {}", positionCode, e);
            return null;
        }
    }

    /**
     * 查询用户信息
     */
    private UserInfoDTO queryUserInfo(Long miId) {
        try {
            Optional<UserInfoDTO> result = userApiService.queryUserByMiId(miId);
            return result.orElse(null);
        } catch (Exception e) {
            log.error("查询用户信息失败, miId: {}", miId, e);
            return null;
        }
    }

    /**
     * 查询产品信息
     */
    private Map<String, ProductInfoDTO> queryProductInfo(List<SubmitQtyReq.QtyDetailDto> detailList) {
        try {
            List<String> productIds = detailList.stream()
                    .map(SubmitQtyReq.QtyDetailDto::getProductId)
                    .distinct()
                    .collect(Collectors.toList());

            Map<Long, ProductInfoDTO> productMap = productApiService.queryProductsByGoodIds(productIds);
            
            // 创建从goodsId到ProductInfoDTO的映射
            Map<String, ProductInfoDTO> goodsIdToProductMap = new HashMap<>();
            for (ProductInfoDTO product : productMap.values()) {
                goodsIdToProductMap.put(product.getGoodsId(), product);
            }
            
            return goodsIdToProductMap;
        } catch (Exception e) {
            log.error("查询产品信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 查询产品价格信息
     */
    private Map<String, IntlRmsRrp> queryRrpInfo(Collection<ProductInfoDTO> products, String countryCode) {
        try {
            List<Long> productCodes = products.stream()
                    .map(ProductInfoDTO::getGoodsId)
                    .map(Long::valueOf)
                    .distinct()
                    .collect(Collectors.toList());

            if (CollectionUtils.isEmpty(productCodes)) {
                return new HashMap<>();
            }

            Long currentTime = SalesTimeValidUtil.getCurrentTimestamp();
            List<IntlRmsRrp> rrpList = intlRmsRrpMapper.selectValidRrpByProductCodes(productCodes, currentTime, countryCode);

            return rrpList.stream()
                    .collect(Collectors.toMap(
                            IntlRmsRrp::getProductCode,
                            rrp -> rrp,
                            (existing, replacement) -> existing
                    ));
        } catch (Exception e) {
            log.error("查询产品价格信息失败", e);
            return new HashMap<>();
        }
    }

    /**
     * 创建QTY相关数据
     */
    private List<IntlSoQty> createQtyData(SubmitQtyReq request,
                                          PositionStoreInfoDTO positionStoreInfo, UserInfoDTO userInfo,
                                          Map<String, ProductInfoDTO> productMap, Map<String, IntlRmsRrp> rrpMap) {

        // 1. 创建门店信息数据
        Long orgInfoId = intlSoOrgInfoService.createOrgInfo(positionStoreInfo, request.getCountryCode());

        // 2. 创建用户信息数据
        Long userInfoId = intlSoUserInfoService.createUserInfo(userInfo, request.getMiId());

        // 3. 创建QTY明细数据
        List<IntlSoQty> qtyList = createQtyDetails(request, orgInfoId, userInfoId,
                                                   productMap, rrpMap, positionStoreInfo);

        // 4. 批量插入QTY数据
        if (CollectionUtils.isNotEmpty(qtyList)) {
            intlSoQtyService.saveBatch(qtyList);
        }

        // 5. 设置图片存在标志
        setQtyPhotoExistFlag(request, qtyList);

        // 6. 更新QTY数据（更新图片存在标志）
        if (CollectionUtils.isNotEmpty(qtyList)) {
            intlSoQtyService.updateBatchById(qtyList);
        }

        // 7. 创建图片数据
        createQtyPhotoData(request, qtyList, userInfo);
        return   qtyList;
    }

    /**
     * 创建QTY明细数据
     */
    private List<IntlSoQty> createQtyDetails(SubmitQtyReq request,
                                             Long orgInfoId, Long userInfoId,
                                             Map<String, ProductInfoDTO> productMap,
                                             Map<String, IntlRmsRrp> rrpMap,
                                             PositionStoreInfoDTO positionStoreInfo) {

        List<IntlSoQty> qtyList = new ArrayList<>();
        Long currentTime = SalesTimeValidUtil.getCurrentTimestamp();

        for (SubmitQtyReq.QtyDetailDto detail : request.getDetailList()) {
            ProductInfoDTO product = productMap.get(detail.getProductId());
            if (product == null) {
                log.warn("产品信息不存在, productId: {}", detail.getProductId());
                continue;
            }

            IntlSoQty qty = new IntlSoQty();

            // 基本信息
            qty.setRmsId(null); // 后续数据同步到RMS系统后赋值
            qty.setProductCode(product.getGoodsId());
            qty.setSpuName(StringUtils.isNotBlank(product.getProductLineEn()) ? product.getProductLineEn() : "");

            // 数量信息
            qty.setQuantity(StringUtils.isNotBlank(detail.getQuantity()) ? Integer.valueOf(detail.getQuantity()) : 0);

            // 关联信息
            qty.setOrgInfoId(orgInfoId);
            qty.setUserInfoId(userInfoId);
            qty.setSalesmanMid(request.getMiId());

            // 门店和阵地信息，参考IMEI提交逻辑
            qty.setStoreRmsCode(positionStoreInfo.getStoreCode() != null ? positionStoreInfo.getStoreCode() : "");
            qty.setPositionRmsCode(positionStoreInfo.getPositionCode() != null ? positionStoreInfo.getPositionCode() : "");
            qty.setRetailerCode(positionStoreInfo.getRetailerCode() != null ? positionStoreInfo.getRetailerCode() : "");
            
            // 设置detail_id，用于和图片关联，参考IMEI提交逻辑
            // 检查detailId长度，如果超过36个字符则截断，避免数据库字段长度溢出
            String detailId = detail.getDetailId();
            if (StringUtils.isNotBlank(detailId) && detailId.length() > 36) {
                log.warn("detailId长度超过36个字符，进行截断处理。原始值: {}, 截断后: {}",
                        detailId, detailId.substring(0, 36));
                detailId = detailId.substring(0, 36);
            }
            qty.setDetailId(detailId);

            // 创建和修改信息
            qty.setCreatedby(request.getMiId());
            qty.setCreatedon(currentTime);
            qty.setModifiedby(request.getMiId());
            qty.setModifiedon(currentTime);


            qty.setSalesTime(detail.getSalesTime() != null ? detail.getSalesTime() : currentTime);

            // 价格信息
            IntlRmsRrp rrp = rrpMap.get(Long.valueOf(product.getGoodsId()));
            if (rrp != null) {
                qty.setRrpCode(String.valueOf(rrp.getId()));
                qty.setRrp(rrp.getRrp() != null ? rrp.getRrp() : BigDecimal.ZERO);
                qty.setCurrency(rrp.getCurrency() != null ? rrp.getCurrency() : "");
            } else {
                qty.setRrpCode("");
                qty.setRrp(BigDecimal.ZERO);
                qty.setCurrency("");
            }

            // 其他字段
            // 设置batchId为importLogId，用于标识导入批次
            if (request.getImportLogId() != null) {
                qty.setBatchId(request.getImportLogId().intValue());
                // 有importLogId，说明是通过PC端导入，设置为PC端
                qty.setReportingType(100000000); // PC端
                log.info("检测到importLogId: {}, 设置为PC端导入", request.getImportLogId());
            } else {
                // 没有importLogId，说明是通过App端导入，设置为App端
                qty.setReportingType(100000001); // App端
                log.info("未检测到importLogId，设置为App端导入");
            }
            qty.setNote(detail.getNote() != null ? detail.getNote() : "");
            qty.setStatus(0); // 默认可用
            qty.setDataFrom(DataFromEnum.MIRETAIL.getValue()); // 默认赋值mi retail，值为 2
            qty.setIsPhotoExist(0); // 默认不存在，后续由setQtyPhotoExistFlag方法设置
            qty.setDetailId(detail.getDetailId()); // 设置detail_id，用于和图片关联

            qtyList.add(qty);
        }

        return qtyList;
    }

    /**
     * 设置QTY图片存在标志
     */
    private void setQtyPhotoExistFlag(SubmitQtyReq request, List<IntlSoQty> qtyList) {
        if (CollectionUtils.isEmpty(request.getPhotoList()) || CollectionUtils.isEmpty(qtyList)) {
            return;
        }

        // 建立detailId到QTY对象的映射
        Map<String, IntlSoQty> detailIdToQtyMap = new HashMap<>();
        for (int i = 0; i < request.getDetailList().size() && i < qtyList.size(); i++) {
            String detailId = request.getDetailList().get(i).getDetailId();
            IntlSoQty qty = qtyList.get(i);
            detailIdToQtyMap.put(detailId, qty);
        }

        // 根据图片列表设置isPhotoExist字段（需要检查URL不为空）
        for (SubmitQtyReq.QtyPhotoDto photo : request.getPhotoList()) {
            // 只有当图片的URL不为空时才设置为1
            if (StringUtils.isNotBlank(photo.getUrl())) {
                IntlSoQty qty = detailIdToQtyMap.get(photo.getDetailId());
                if (qty != null) {
                    qty.setIsPhotoExist(1); // 有图片且URL不为空则设置为1
                }
            }
        }
    }

    /**
     * 创建QTY图片数据
     */
    private void createQtyPhotoData(SubmitQtyReq request, List<IntlSoQty> qtyList, UserInfoDTO userInfo) {
        if (CollectionUtils.isEmpty(request.getPhotoList()) || CollectionUtils.isEmpty(qtyList)) {
            return;
        }

        // 建立detailId到qtyId的映射
        Map<String, Long> detailIdToQtyIdMap = new HashMap<>();
        for (int i = 0; i < request.getDetailList().size() && i < qtyList.size(); i++) {
            String detailId = request.getDetailList().get(i).getDetailId();
            Long qtyId = qtyList.get(i).getId();
            if (qtyId != null) {
                detailIdToQtyIdMap.put(detailId, qtyId);
            }
        }

        List<PhotoDataInfoDTO> photoDataList = new ArrayList<>();
        Long currentTime = SalesTimeValidUtil.getCurrentTimestamp();

        for (SubmitQtyReq.QtyPhotoDto photo : request.getPhotoList()) {
            Long relatedId = detailIdToQtyIdMap.get(photo.getDetailId());
            if (relatedId == null) {
                log.warn("找不到对应的QTY记录, detailId: {}", photo.getDetailId());
                continue;
            }

            PhotoDataInfoDTO photoData = new PhotoDataInfoDTO();
            photoData.setRelatedId(relatedId);
            photoData.setIsOfflineUpload(0); // 默认为非离线
            photoData.setIsUploadedToBlob(1); // 默认已上传到Blob，参考IMEI实现
            photoData.setModuleName(MODULE_NAME); // 模块名称 so_qty_upload
            photoData.setGuid(photo.getDetailId()); // 设置guid，用于图片关联

            // 获取用户信息设置上传者名称
            // 这里简化处理，实际可能需要根据miId查询用户英文名
            photoData.setUploaderName("user_" + request.getMiId());

            // 上传时间转换
            Long uploadTime = IntlTimeUtil.parseAreaTimeToTimestamp(request.getCountryCode(), photo.getUploadTime());
            photoData.setUploaderTime(uploadTime != null ? uploadTime : currentTime);

            photoData.setFdsUrl(photo.getUrl());
            photoData.setCreateTime(currentTime);
            photoData.setUpdateTime(currentTime);

            // 从URL中提取文件后缀
            String suffix = extractSuffixFromUrl(photo.getUrl());
            photoData.setSuffix(suffix);

            photoDataList.add(photoData);
        }

        // 批量插入图片数据
        if (CollectionUtils.isNotEmpty(photoDataList)) {
            fileUploadApiService.createPhotoData(photoDataList);
        }
    }

    /**
     * 从URL中提取文件后缀
     */
    private String extractSuffixFromUrl(String url) {
        if (StringUtils.isBlank(url)) {
            return ".jpg"; // 默认后缀
        }

        int lastDotIndex = url.lastIndexOf('.');
        if (lastDotIndex > 0 && lastDotIndex < url.length() - 1) {
            return url.substring(lastDotIndex);
        }

        return ".jpg"; // 默认后缀
    }

    /**
     * 按照指定优先级排序产品线列表
     * 优先级：Smartphones > Pad > TV > Wearables > Laptop > 其他产品线（按首字母排序）
     *
     * @param productLineList 产品线列表
     * @return 排序后的产品线列表
     */
    private List<GetSkuListResponse.ProductLineInfo> sortProductLineListByPriority(
            List<GetSkuListResponse.ProductLineInfo> productLineList) {
        
        if (CollectionUtils.isEmpty(productLineList)) {
            return productLineList;
        }
        
        // 定义优先级映射，数字越小优先级越高
        Map<String, Integer> priorityMap = new HashMap<>();
        priorityMap.put("Smartphones", 1);
        priorityMap.put("Pad", 2);
        priorityMap.put("TV", 3);
        priorityMap.put("Wearables", 4);
        priorityMap.put("Laptop", 5);
        
        return productLineList.stream()
                .sorted((a, b) -> {
                    String nameA = a.getName();
                    String nameB = b.getName();
                    
                    // 获取优先级，如果不在优先级列表中则设为最大值（最低优先级）
                    Integer priorityA = priorityMap.getOrDefault(nameA, Integer.MAX_VALUE);
                    Integer priorityB = priorityMap.getOrDefault(nameB, Integer.MAX_VALUE);
                    
                    // 如果优先级不同，按优先级排序
                    if (!priorityA.equals(priorityB)) {
                        return priorityA.compareTo(priorityB);
                    }
                    
                    // 如果优先级相同（都是其他产品线），按首字母排序
                    return nameA.compareToIgnoreCase(nameB);
                })
                .collect(Collectors.toList());
    }

    /**
     * 按照指定优先级排序FilterItem产品线列表
     * 优先级：Smartphones > Pad > TV > Wearables > Laptop > 其他产品线（按首字母排序）
     *
     * @param productLineList 产品线列表
     * @return 排序后的产品线列表
     */
    private List<GetFilterListResponse.FilterItem> sortFilterItemProductLineListByPriority(
            List<GetFilterListResponse.FilterItem> productLineList) {
        
        if (CollectionUtils.isEmpty(productLineList)) {
            return productLineList;
        }
        
        // 定义优先级映射，数字越小优先级越高
        Map<String, Integer> priorityMap = new HashMap<>();
        priorityMap.put("Smartphones", 1);
        priorityMap.put("Pad", 2);
        priorityMap.put("TV", 3);
        priorityMap.put("Wearables", 4);
        priorityMap.put("Laptop", 5);
        
        return productLineList.stream()
                .sorted((a, b) -> {
                    String nameA = a.getName();
                    String nameB = b.getName();
                    
                    // 获取优先级，如果不在优先级列表中则设为最大值（最低优先级）
                    Integer priorityA = priorityMap.getOrDefault(nameA, Integer.MAX_VALUE);
                    Integer priorityB = priorityMap.getOrDefault(nameB, Integer.MAX_VALUE);
                    
                    // 如果优先级不同，按优先级排序
                    if (!priorityA.equals(priorityB)) {
                        return priorityA.compareTo(priorityB);
                    }
                    
                    // 如果优先级相同（都是其他产品线），按首字母排序
                    return nameA.compareToIgnoreCase(nameB);
                })
                .collect(Collectors.toList());
    }

    /**
     * 根据字典名称查询筛选列表
     *
     * @param dictName 字典名称
     * @return 筛选列表
     */
    private List<GetFilterListResponse.FilterItem> queryFilterListByDictName(String dictName) {
        try {
            log.info("查询字典数据, dictName: {}", dictName);
            
            // 直接查询指定字典名称的启用状态数据
            com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<com.mi.info.intl.retail.so.infra.database.dataobject.sys.IntlSysDict> queryWrapper = 
                new com.baomidou.mybatisplus.core.conditions.query.QueryWrapper<>();
            queryWrapper.eq("dict_code", dictName)
                       .eq("status", 0) // 只查询启用状态的数据
                       .orderByAsc("sort"); // 按排序号升序

            List<com.mi.info.intl.retail.so.infra.database.dataobject.sys.IntlSysDict> dictList = 
                intlSysDictService.list(queryWrapper);

            log.info("查询到字典数据数量: {}", dictList.size());

            return dictList.stream()
                    .map(dict -> {
                        GetFilterListResponse.FilterItem item = new GetFilterListResponse.FilterItem();
                        item.setName(dict.getDictLabel());
                        item.setCode(Integer.valueOf(dict.getDictValue()));
                        return item;
                    })
                    .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("查询筛选列表失败, dictName: {}", dictName, e);
            return new ArrayList<>();
        }
    }

    /**
     * 获取用户门店列表
     */
    private List<Long> getUserStoreList(Long miId, Long userTitle) {
        // 促销员职位列表
        List<Long> promoterTitles = Arrays.asList(500900001L, 100000027L, 100000026L);

        // 如果是促销员，返回空列表（只查询自己的数据）
        if (promoterTitles.contains(userTitle)) {
            return new ArrayList<>();
        }

        // 其他职位，查询用户关联的所有门店
        try {
            return intlSoQtyMapper.getUserStoreList(miId);
        } catch (Exception e) {
            log.error("获取用户门店列表失败, miId: {}, userTitle: {}", miId, userTitle, e);
            return new ArrayList<>();
        }
    }

    /**
     * 查询QTY统计数据
     */
    private QueryQtyStatisticsResponse queryQtyStatisticsData(QueryQtyStatisticsRequest request, List<Long> userStoreList) {
        // 时间筛选参数
        Long startTime = null;
        Long endTime = null;
        if (StringUtils.isNotBlank(request.getDateFilterType())) {
            if ("1".equals(request.getDateFilterType()) &&
                    StringUtils.isNotBlank(request.getYear()) &&
                    StringUtils.isNotBlank(request.getMonth())) {
                // 年月筛选
                startTime = convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(),
                        true);
                endTime = convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(),
                        false);
            } else if ("2".equals(request.getDateFilterType()) &&
                    StringUtils.isNotBlank(request.getStartTime()) &&
                    StringUtils.isNotBlank(request.getEndTime())) {
                // 自定义时间段筛选
                startTime = convertToCountryTimestamp(request.getStartTime(), request.getCountryCode());
                endTime = convertToCountryTimestamp(request.getEndTime(), request.getCountryCode());
            }
        }

        QueryQtyStatisticsResponse response = new QueryQtyStatisticsResponse();

        try {
            // 使用分组查询，一次查询获取所有统计数据
            Map<String, Object> statisticsResult = intlSoQtyMapper.queryQtyStatisticsGroup(
                    request.getCountryCode(),
                    PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                    PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                    request.getSearch(),
                    request.getReportType(),
                    request.getStoreCode(),
                    request.getProductLine(),
                    request.getStoreType(),
                    request.getChannelType(),
                    request.getDateFilterType(),
                    startTime != null ? String.valueOf(startTime) : null,
                    endTime != null ? String.valueOf(endTime) : null
            );

            // 从查询结果中提取统计数据
            Integer totalCount = statisticsResult != null ? getIntegerValue(statisticsResult.get("totalCount")) : 0;
            Integer pcCount = statisticsResult != null ? getIntegerValue(statisticsResult.get("pcCount")) : 0;
            Integer appCount = statisticsResult != null ? getIntegerValue(statisticsResult.get("appCount")) : 0;

            response.setTotalCount(totalCount != null ? totalCount : 0);
            response.setPcCount(pcCount != null ? pcCount : 0);
            response.setAppCount(appCount != null ? appCount : 0);

        } catch (Exception e) {
            log.error("查询QTY统计数据失败", e);
            response.setTotalCount(0);
            response.setPcCount(0);
            response.setAppCount(0);
        }

        return response;
    }

    /**
     * 查询QTY数据列表（分页）
     */
    private QueryQtyListResponse queryQtyDataListWithPage(QueryQtyListRequest request, List<Long> userStoreList) {
        QueryQtyListResponse response = new QueryQtyListResponse();

        try {
            // 计算分页参数
            Integer pageIndex = request.getPageIndex() != null ? request.getPageIndex() : 1;
            Integer pageSize = request.getPageSize() != null ? request.getPageSize() : 10;
            Integer offset = (pageIndex - 1) * pageSize;

            // 时间筛选参数处理
            Long startTime = null;
            Long endTime = null;
            if (StringUtils.isNotBlank(request.getDateFilterType())) {
                if ("1".equals(request.getDateFilterType()) &&
                        StringUtils.isNotBlank(request.getYear()) &&
                        StringUtils.isNotBlank(request.getMonth())) {
                    // 年月筛选
                    startTime = convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(), true);
                    endTime = convertYearMonthToTimestamp(request.getYear(), request.getMonth(), request.getCountryCode(), false);
                } else if ("2".equals(request.getDateFilterType()) &&
                        StringUtils.isNotBlank(request.getStartTime()) &&
                        StringUtils.isNotBlank(request.getEndTime())) {
                    // 自定义时间段筛选
                    startTime = convertToCountryTimestamp(request.getStartTime(), request.getCountryCode());
                    endTime = convertToCountryTimestamp(request.getEndTime(), request.getCountryCode());
                }
            }

            // 查询QTY明细数据（分页）- 多查询一条用于判断是否有更多数据
            List<Map<String, Object>> detailData = intlSoQtyMapper.queryQtyListWithPage(
                    request.getCountryCode(),
                    PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                    PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                    request.getSearch(),
                    request.getReportType(),
                    request.getStoreCode(),
                    request.getProductLine(),
                    request.getStoreType(),
                    request.getChannelType(),
                    request.getDateFilterType(),
                    startTime != null ? String.valueOf(startTime) : null,
                    endTime != null ? String.valueOf(endTime) : null,
                    offset,
                    pageSize + 1
            );

            // 判断是否有更多数据
            boolean hasMore = detailData.size() > pageSize;

            // 构建响应
            response.setMoreRecords(hasMore);

            // 处理明细数据 - 使用时区转换
            List<QueryQtyListResponse.QtyDetail> actualDetailList = new ArrayList<>();
            int actualSize = hasMore ? pageSize : detailData.size();
            
            for (int i = 0; i < actualSize; i++) {
                Map<String, Object> item = detailData.get(i);
                QueryQtyListResponse.QtyDetail dto = new QueryQtyListResponse.QtyDetail();
                
                try {
                    // 设置基本信息 - 安全类型转换
                    dto.setId(getIntegerValue(item.get("id")));
                    dto.setProductName(getStringValue(item.get("productName")));
                    dto.setProductCode(getStringValue(item.get("productCode")));
                    dto.setProductLine(getStringValue(item.get("productLine")));
                    dto.setShortName(getStringValue(item.get("shortName")));
                    dto.setQuantity(getIntegerValue(item.get("quantity")));
                    dto.setNote(getStringValue(item.get("note")));
                    dto.setStoreName(getStringValue(item.get("storeName")));
                    dto.setStoreCode(getStringValue(item.get("storeCode")));
                    dto.setStoreType(getIntegerValue(item.get("storeType")));
                    dto.setChannelType(getIntegerValue(item.get("channelType")));
                    dto.setReportType(getIntegerValue(item.get("reportType")));
                    dto.setPhotoExistFlag(getBooleanValue(item.get("photoExistFlag")));
                    dto.setUserName(getStringValue(item.get("userName")));

                    // 处理时间字段 - 使用时区转换
                    Long salesTimeTimestamp = getLongValue(item.get("sales_time"));
                    String countryCode = getStringValue(item.get("country_code"));
                    
                    if (salesTimeTimestamp != null && StringUtils.isNotBlank(countryCode)) {
                        try {
                            String salesTimeStr = IntlTimeUtil.parseTimestampToAreaTime(countryCode, salesTimeTimestamp);
                            if (StringUtils.isNotBlank(salesTimeStr)) {
                                dto.setSalesTime(salesTimeStr);
                                // 提取到分钟部分，格式：yyyy-MM-dd HH:mm
                                if (salesTimeStr.length() >= 16) {
                                    dto.setSalesDate(salesTimeStr.substring(0, 16));
                                } else if (salesTimeStr.length() >= 10) {
                                    dto.setSalesDate(salesTimeStr.substring(0, 10));
                                }
                            }
                        } catch (NumberFormatException e) {
                            log.warn("时间戳格式转换失败，salesTime: {}, countryCode: {}", salesTimeTimestamp, countryCode, e);
                        } catch (Exception e) {
                            log.warn("时间转换失败，salesTime: {}, countryCode: {}", salesTimeTimestamp, countryCode, e);
                        }
                    }

                    // 设置枚举标签 - 使用字典系统转换
                    try {
                        Map<String, List<LabelValueDTO>> salesDictMap = soSalesHandler.getLabelValueList4Sales();
                        
                        // 转换 storeType
                        Integer storeType = getIntegerValue(item.get("storeType"));
                        log.debug("QTY storeType: {}, raw value: {}", storeType, item.get("storeType"));
                        if (storeType != null) {
                            List<LabelValueDTO> storeTypeList = salesDictMap.get(SalesDictEnum.STORE_TYPE.getCode());
                            if (storeTypeList != null) {
                                Map<String, String> storeTypeMap = storeTypeList.stream()
                                        .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
                                dto.setStoreTypeLabel(storeTypeMap.get(String.valueOf(storeType)));
                            }
                        }
                        
                        // 转换 channelType
                        Integer channelType = getIntegerValue(item.get("channelType"));
                        log.debug("QTY channelType: {}, raw value: {}", channelType, item.get("channelType"));
                        if (channelType != null) {
                            List<LabelValueDTO> channelTypeList = salesDictMap.get(SalesDictEnum.CHANNEL_TYPE.getCode());
                            if (channelTypeList != null) {
                                Map<String, String> channelTypeMap = channelTypeList.stream()
                                        .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
                                dto.setChannelTypeLabel(channelTypeMap.get(String.valueOf(channelType)));
                            }
                        }
                        
                        // 转换 reportType
                        Integer reportType = getIntegerValue(item.get("reportType"));
                        if (reportType != null) {
                            List<LabelValueDTO> reportingTypeList = salesDictMap.get(SalesDictEnum.REPORTING_TYPE.getCode());
                            if (reportingTypeList != null) {
                                Map<String, String> reportingTypeMap = reportingTypeList.stream()
                                        .collect(Collectors.toMap(LabelValueDTO::getId, LabelValueDTO::getTitle));
                                dto.setReportTypeLabel(reportingTypeMap.get(String.valueOf(reportType)));
                            }
                        }
                    } catch (Exception e) {
                        log.warn("获取字典转换失败", e);
                        dto.setStoreTypeLabel("");
                        dto.setChannelTypeLabel("");
                        dto.setReportTypeLabel("");
                    }

                    actualDetailList.add(dto);
                } catch (Exception e) {
                    log.error("处理QTY明细数据失败，item: {}", item, e);
                    // 继续处理下一条数据，不中断整个流程
                }
            }
            
            response.setDetailList(actualDetailList);

            // 构建日期分组数据 - 基于当前页的实际数据
            List<Map<String, Object>> dateGroupData = new ArrayList<>();
            if (!actualDetailList.isEmpty()) {
                try {
                    // 从当前页的数据中提取日期并统计
                    Map<String, Integer> dateCountMap = new HashMap<>();
                    for (int i = 0; i < actualSize; i++) {
                        QueryQtyListResponse.QtyDetail item = actualDetailList.get(i);
                        String salesDate = item.getSalesDate();
                        if (StringUtils.isNotBlank(salesDate)) {
                            dateCountMap.put(salesDate, dateCountMap.getOrDefault(salesDate, 0) + 1);
                        }
                    }
                    
                    // 转换为返回格式并按日期倒序排列
                    dateGroupData = dateCountMap.entrySet().stream()
                            .map(entry -> {
                                Map<String, Object> dateGroup = new HashMap<>();
                                dateGroup.put("date", entry.getKey());
                                dateGroup.put("count", entry.getValue());
                                return dateGroup;
                            })
                            .sorted((a, b) -> ((String) b.get("date")).compareTo((String) a.get("date")))
                            .collect(Collectors.toList());

                    // 获取排序后末尾一天的日期，单独查询这一天的QTY数量
                    if (!dateGroupData.isEmpty()) {
                        String lastDate = (String) dateGroupData.get(dateGroupData.size() - 1).get("date");
                        Long lastDateTimestamp = IntlTimeUtil.parseAreaTimeToTimestamp(request.getCountryCode(),
                                lastDate + " 00:00:00");

                        if (lastDateTimestamp != null) {
                            // 查询这一天的QTY数量 - 按照 IMEI 的逻辑，使用精确的时间范围
                            int countForLastDate = intlSoQtyMapper.queryQtyAppCount(
                                    request.getCountryCode(),
                                    PROMOTER_TITLES.contains(request.getUserTitle()) ? request.getMiId() : null,
                                    PROMOTER_TITLES.contains(request.getUserTitle()) ? null : userStoreList,
                                    request.getSearch(),
                                    request.getReportType(),
                                    request.getStoreCode(),
                                    request.getProductLine(),
                                    request.getStoreType(),
                                    request.getChannelType(),
                                    request.getDateFilterType(),
                                    String.valueOf(lastDateTimestamp),
                                    String.valueOf(lastDateTimestamp + 24 * 60 * 60 * 1000) // 加一天的时间戳，与 IMEI 保持一致
                            );

                            // 更新最后一天的数量
                            dateGroupData.get(dateGroupData.size() - 1).put("count", countForLastDate);
                        }
                    }
                } catch (Exception e) {
                    log.error("构建日期分组数据失败", e);
                }
            }

            // 处理日期分组数据
            List<QueryQtyListResponse.DateGroup> dateGroupList = new ArrayList<>();
            try {
                for (Map<String, Object> item : dateGroupData) {
                    try {
                        QueryQtyListResponse.DateGroup dto = new QueryQtyListResponse.DateGroup();
                        dto.setDate((String) item.get("date"));
                        dto.setCount(((Number) item.get("count")).intValue());
                        dateGroupList.add(dto);
                    } catch (Exception e) {
                        log.warn("处理日期分组数据项失败，item: {}", item, e);
                        // 继续处理下一条数据，不中断整个流程
                    }
                }
            } catch (Exception e) {
                log.error("处理日期分组数据失败", e);
                dateGroupList = new ArrayList<>();
            }
            
            response.setDateGroupList(dateGroupList);

        } catch (Exception e) {
            log.error("查询QTY数据列表失败", e);
            response.setDetailList(new ArrayList<>());
            response.setDateGroupList(new ArrayList<>());
            response.setMoreRecords(false);
        }

        return response;
    }

    /**
     * 安全获取字符串值
     */
    private String getStringValue(Object value) {
        return value != null ? String.valueOf(value) : null;
    }

    /**
     * 安全获取整数值
     */
    private Integer getIntegerValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).intValue();
        }
        try {
            return Integer.valueOf(String.valueOf(value));
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取长整数值
     */
    private Long getLongValue(Object value) {
        if (value == null) {
            return null;
        }
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        try {
            return Long.valueOf(String.valueOf(value));
        } catch (NumberFormatException e) {
            return null;
        }
    }

    /**
     * 安全获取布尔值
     */
    private Boolean getBooleanValue(Object value) {
        if (value == null) {
            return false; // 默认返回 false 而不是 null
        }
        if (value instanceof Boolean) {
            return (Boolean) value;
        }
        String stringValue = String.valueOf(value);
        if ("true".equalsIgnoreCase(stringValue) || "1".equals(stringValue) || "yes".equalsIgnoreCase(stringValue)) {
            return true;
        }
        if ("false".equalsIgnoreCase(stringValue) || "0".equals(stringValue) || "no".equalsIgnoreCase(stringValue)) {
            return false;
        }
        // 对于其他无法识别的值，默认返回 false
        return false;
    }

    /**
     * 将时间字符串转换为国家本地时间戳
     */
    private Long convertToCountryTimestamp(String timeStr, String countryCode) {
        if (StringUtils.isBlank(timeStr) || StringUtils.isBlank(countryCode)) {
            log.warn("时间转换参数为空: timeStr={}, countryCode={}", timeStr, countryCode);
            return null;
        }
        
        try {
            // 如果timeStr是时间戳格式，直接转换
            if (timeStr.matches("\\d+")) {
                return Long.parseLong(timeStr);
            }

            // 使用IntlTimeUtil进行时区转换
            return IntlTimeUtil.parseAreaTimeToTimestamp(countryCode, timeStr);
        } catch (NumberFormatException e) {
            log.error("时间戳格式转换失败: timeStr={}, countryCode={}", timeStr, countryCode, e);
            return null;
        } catch (Exception e) {
            log.error("时间转换失败: timeStr={}, countryCode={}", timeStr, countryCode, e);
            return null;
        }
    }

    /**
     * 将年月转换为时间戳
     */
    private Long convertYearMonthToTimestamp(String year, String month, String countryCode, boolean isStart) {
        if (StringUtils.isBlank(year) || StringUtils.isBlank(month) || StringUtils.isBlank(countryCode)) {
            log.warn("年月转换参数为空: year={}, month={}, countryCode={}", year, month, countryCode);
            return null;
        }
        
        try {
            int yearInt = Integer.parseInt(year);
            int monthInt = Integer.parseInt(month);

            // 验证年月范围
            if (yearInt < 1900 || yearInt > 2100 || monthInt < 1 || monthInt > 12) {
                log.warn("年月参数超出有效范围: year={}, month={}", yearInt, monthInt);
                return null;
            }

            // 使用IntlTimeUtil获取月份的开始和结束时间戳
            Long[] timestamps = IntlTimeUtil.getMonthStartAndEndTimestamp(countryCode, yearInt, monthInt);
            if (timestamps != null && timestamps.length == 2) {
                return isStart ? timestamps[0] : timestamps[1];
            }

            return null;
        } catch (NumberFormatException e) {
            log.error("年月数字格式转换失败: year={}, month={}, countryCode={}", year, month, countryCode, e);
            return null;
        } catch (Exception e) {
            log.error("年月时间转换失败: year={}, month={}, countryCode={}", year, month, countryCode, e);
            return null;
        }
    }

}