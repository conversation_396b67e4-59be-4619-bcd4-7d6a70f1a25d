package com.mi.info.intl.retail.so.app.provider.imei;

import com.mi.info.intl.retail.intlretail.service.api.so.imei.provider.ImeiVerifyDubboProvider;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.imei.service.IntlSoImeiVerifyService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@Service
@DubboService(timeout = 20000, group = "${retail.dubbo.group:}", retries = 3, interfaceClass = ImeiVerifyDubboProvider.class)
public class ImeiVerifyDubboProviderImpl implements ImeiVerifyDubboProvider {

    @Resource
    private IntlSoImeiVerifyService intlSoImeiVerifyService;

    @Override
    public CommonApiResponse<Void> imeiActivationVerify() {

        //1.激活校验，获取成功+失败的id
        List<Long> longs = intlSoImeiVerifyService.verifyActivationStatusOfImei();
        // 2.发送mq
        return CommonApiResponse.success(null);

    }
}
