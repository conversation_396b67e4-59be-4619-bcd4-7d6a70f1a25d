package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;

/**
 * 明文IMEI用户列表
 * @TableName intl_so_plaintext_imei_user
 */
@TableName(value = "intl_so_plaintext_imei_user")
@Data
public class IntlSoPlaintextImeiUser implements Serializable {
    
    private static final long serialVersionUID = -6195970478498697598L;
    
    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 关联国家名称
     */
    @TableField(value = "country_name")
    private String countryName;

    /**
     * 关联国家短代码
     */
    @TableField(value = "country_code")
    private String countryCode;

    /**
     * 联系人图片url
     */
    @TableField(value = "photo_ids")
    private String photoIds;

    /**
     * 用户mid
     */
    @TableField(value = "user_mid")
    private Long userMid;

    /**
     * 用户名称
     */
    @TableField(value = "user_name")
    private String userName;

    /**
     * 职位id
     */
    @TableField(value = "job_title_id")
    private String jobTitleId;

    /**
     * 创建人mid
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 创建时间戳
     */
    @TableField(value = "created_on")
    private Long createdOn;

    /**
     * 修改人mid
     */
    @TableField(value = "modified_by")
    private Long modifiedBy;

    /**
     * 修改时间戳
     */
    @TableField(value = "modified_on")
    private Long modifiedOn;

    /**
     * 状态 0:正常 1:禁用
     */
    @TableField(value = "status")
    private Integer status;



}