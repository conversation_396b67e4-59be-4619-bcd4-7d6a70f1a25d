package com.mi.info.intl.retail.so.domain.upload.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 销量imei
 *
 * @TableName intl_so_imei
 */
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@TableName(value = "intl_so_imei")
@Data
public class IntlSoImei implements Serializable {

    private static final long serialVersionUID = -5038096055551271964L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 序列号
     */
    @TableField(value = "sn")
    private String sn;

    /**
     * IMEI码1
     */
    @TableField(value = "imei1")
    private String imei1;

    /**
     * IMEI码2
     */
    @TableField(value = "imei2")
    private String imei2;

    /**
     * 序列号掩码
     */
    @TableField(value = "sn_mask")
    private String snMask;

    /**
     * IMEI码1掩码
     */
    @TableField(value = "imei1_mask")
    private String imei1Mask;

    /**
     * IMEI码2掩码
     */
    @TableField(value = "imei2_mask")
    private String imei2Mask;

    /**
     * 序列号哈希值
     */
    @TableField(value = "sn_hash")
    private String snHash;

    /**
     * IMEI是否来自枢纽（0：否；1：是）
     */
    @TableField(value = "imei_from_hub")
    private String imeiFromHub;

    /**
     * 产品 code
     */
    @TableField(value = "product_code")
    private String productCode;

    /**
     * 阵地组织信息 ID
     */
    @TableField(value = "org_info_id")
    private Long orgInfoId;

    /**
     * 用户信息 ID
     */
    @TableField(value = "user_info_id")
    private Long userInfoId;

    /**
     * 是否是重点
     */
    @TableField(value = "focus_model")
    private String focusModel;

    /**
     * IMEI规则id
     */
    @TableField(value = "imei_rule_id")
    private String imeiRuleId;

    /**
     * IMEI规则执行前数据
     */
    @TableField(value = "imei_rule_before")
    private Integer imeiRuleBefore;

    /**
     * IMEI规则执行后数据
     */
    @TableField(value = "imei_rule_after")
    private Integer imeiRuleAfter;

    @TableField(value = "imei_rule_is_activing_check")
    private Boolean imeiRuleIsActivingCheck;

    /**
     * 激活验证时间戳
     */
    @TableField(value = "activation_verification_time")
    private Long activationVerificationTime;

    /**
     * 激活时间戳
     */
    @TableField(value = "activation_time")
    private Long activationTime;

    /**
     * 激活频率
     */
    @TableField(value = "activation_frequency")
    private Integer activationFrequency;

    /**
     * 激活站点
     */
    @TableField(value = "activation_site")
    private String activationSite;

    @TableField(value = "repeat_user")
    private String repeatUser;

    @TableField(value = "last_md")
    private String lastMd;

    /**
     * 货币类型（如 USD、CNY 等）
     */
    @TableField(value = "currency")
    private String currency;

    /**
     * 验证状态（Normal，）
     */
    @TableField(value = "verifying_state")
    private Integer verifyingState;

    /**
     * 验证结果（0：验证中；1：验证通过；2：验证失败）
     */
    @TableField(value = "verification_result")
    private Integer verificationResult;

    /**
     * 验证结果详情
     */
    @TableField(value = "verify_result_detail")
    private String verifyResultDetail;

    /**
     * 验证失败原因
     */
    @TableField(value = "failed_reason")
    private Integer failedReason;

    /**
     * ST失败原因详情
     */
    @TableField(value = "failed_reason_detail")
    private Integer failedReasonDetail;

    /**
     * SI校验结果
     */
    @TableField(value = "si_verify_result")
    private Integer siVerifyResult;

    /**
     * 上报
     */
    @TableField(value = "reporting_type")
    private Integer reportingType;

    /**
     * 一级账户编码
     */
    @TableField(value = "first_level_account_code")
    private String firstLevelAccountCode;

    /**
     * 最终销售国
     */
    @TableField(value = "final_sales_country")
    private String finalSalesCountry;

    /**
     * 允许销售国
     */
    @TableField(value = "allow_sales_country")
    private String allowSalesCountry;

    /**
     * 备注信息
     */
    @TableField(value = "note")
    private String note;

    /**
     * 价格表code
     */
    @TableField(value = "rrp_code")
    private String rrpCode;

    /**
     * 销售价格
     */
    @TableField(value = "rrp")
    private BigDecimal rrp;

    /**
     * 批次ID
     */
    @TableField(value = "batch_id")
    private Long batchId;

    /**
     * 销售时间戳
     */
    @TableField(value = "sales_time")
    private Long salesTime;

    /**
     * 创建人mid
     */
    @TableField(value = "created_by")
    private Long createdBy;

    /**
     * 创建时间戳
     */
    @TableField(value = "created_on")
    private Long createdOn;

    /**
     * 修改人mid
     */
    @TableField(value = "modified_by")
    private Long modifiedBy;

    /**
     * 修改时间戳-直接保存RMS的修改时间
     */
    @TableField(value = "modified_on")
    private Long modifiedOn;

    /**
     * 是否启用（0：可用；1 停用）
     */
    @TableField(value = "status")
    private Integer status;

    /**
     *
     */
    @TableField(value = "store_rms_code")
    private String storeRmsCode;

    @TableField(value = "supplier_code")
    private String supplierCode;
    /**
     *
     */
    @TableField(value = "position_rms_code")
    private String positionRmsCode;

    /**
     * RMSID
     */
    @TableField(value = "rms_id")
    private String rmsId;

    /**
     * 0:不存在，1：存在
     */
    @TableField(value = "is_photo_exist")
    private Integer isPhotoExist;

    /**
     * 数据来源 1:RMS 2:Retail
     */
    @TableField(value = "data_from")
    private Integer dataFrom;

    /**
     * 销售人员米id
     */
    @TableField(value = "salesman_mid")
    private Long salesmanMid;

    /**
     * 唯一标识Id, 用于和图片关联
     */
    @TableField(value = "detail_id")
    private String detailId;

    /**
     * 销售渠道
     */
    @TableField(value = "sales_channel")
    private Long salesChannel;
    /**
     * 批次Id(uuid)
     */
    @TableField(value = "batch_id_str")
    private String batchIdStr;

    /**
     * 零售系统的新增时间
     */
    @TableField(value = "created_on_new")
    private Date createdOnNew;

    /**
     * 零售系统的更新时间
     */
    @TableField(value = "update_on_new")
    private Date updateOnNew;

    /**
     * 存量数据id
     */
    @TableField(value = "stock_id")
    private Long stockId;

    /**
     * 产品名称
     */
    @TableField(value = "product_name")
    private String productName;

    /**
     * 产品线编码
     */
    @TableField(value = "product_line_code")
    private Long productLineCode;

    /**
     * 产品线英文名称
     */
    @TableField(value = "product_line_en")
    private String productLineEn;

}