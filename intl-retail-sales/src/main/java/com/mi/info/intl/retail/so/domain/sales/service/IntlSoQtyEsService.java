package com.mi.info.intl.retail.so.domain.sales.service;

import com.alibaba.cola.dto.PageResponse;
import com.alibaba.fastjson.JSON;
import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
import com.mi.info.intl.retail.api.front.store.RmsStoreService;
import com.mi.info.intl.retail.api.product.ProductApiService;
import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
import com.mi.info.intl.retail.core.es.EsQueryConverter;
import com.mi.info.intl.retail.core.metrics.MetricsTimer;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
import com.mi.info.intl.retail.so.domain.sales.model.SoQtyIndex;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoUserInfo;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoOrgInfoService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoQtyService;
import com.mi.info.intl.retail.so.domain.upload.service.IntlSoUserInfoService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.ElasticsearchStatusException;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.rest.RestStatus;
import org.elasticsearch.search.aggregations.AggregationBuilders;
import org.elasticsearch.search.aggregations.Aggregations;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.data.elasticsearch.core.AggregationsContainer;
import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
import org.springframework.data.elasticsearch.core.SearchHit;
import org.springframework.data.elasticsearch.core.SearchHits;
import org.springframework.data.elasticsearch.core.mapping.IndexCoordinates;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class IntlSoQtyEsService {
    
    private static final int MAX_PAGE_SIZE = 500;
    
    @Value("${es.index.qtyIndex}")
    private String indexName;
    
    @Autowired
    private ElasticsearchRestTemplate template;
    
    @Autowired
    private ProductApiService productApiService;
    
    @Resource
    private RmsStoreService rmsStoreService;
    
    @Resource
    private IntlRetailerApiService retailerApiService;
    
    @Resource
    private IntlSoUserInfoService intlSoUserInfoService;
    
    @Resource
    private IntlSoOrgInfoService intlSoOrgInfoService;
    
    @Resource
    private IntlSoQtyService intlSoQtyService;
    
    
    public SoQtyIndex getById(Long id) {
        if (id == null) {
            throw new IllegalArgumentException("id is null");
        }
        try {
            return template.get(String.valueOf(id), SoQtyIndex.class, IndexCoordinates.of(indexName));
        } catch (ElasticsearchStatusException e) {
            if (e.status() == RestStatus.NOT_FOUND) {
                // 处理文档不存在逻辑
                return null;
            }
            log.error("get form es index:{} id:{} error", indexName, id, e);
            throw new BizException(e.getMessage());
        }
    }
    
    
    public void batchExecuteSyncMsg(List<Long> ids, boolean useLatestInfo) {
        // 添加输入参数验证
        if (CollectionUtils.isEmpty(ids)) {
            log.warn("Empty ids list, skipping processing");
            return;
        }
        
        List<IntlSoQty> qtyList = intlSoQtyService.getByIds(ids);
        if (qtyList == null || qtyList.isEmpty() || qtyList.size() != ids.size()) {
            log.error("batch execute sync qty error: qtyList size={}, expected size={}",
                    qtyList != null ? qtyList.size() : 0, ids.size());
            // 数据不完整时，将所有ID标记为错误并重新发送
            throw new BizException("Get qtyList from DB error, resend to MQ");
        }
        Set<String> storeCodes = new HashSet<>();
        Set<String> positionCodes = new HashSet<>();
        Set<Long> userIds = new HashSet<>();
        Set<Long> orgIds = new HashSet<>();
        Set<String> goodsIds = new HashSet<>();
        
        qtyList.forEach(qty -> {
            // 添加空值检查，避免空指针异常
            if (qty.getStoreRmsCode() != null) {
                storeCodes.add(qty.getStoreRmsCode());
            }
            if (qty.getPositionRmsCode() != null) {
                positionCodes.add(qty.getPositionRmsCode());
            }
            if (qty.getUserInfoId() != null) {
                userIds.add(qty.getUserInfoId());
            }
            if (qty.getOrgInfoId() != null) {
                orgIds.add(qty.getOrgInfoId());
            }
            if (qty.getProductCode() != null) {
                goodsIds.add(qty.getProductCode());
            }
        });
        Map<String, RmsStoreInfoDto> storeMap = rmsStoreService.batchGetStoreInfoByStoreCode(new LinkedList<>(storeCodes));
        Map<String, RmsPositionInfoRes> positionMap = rmsStoreService.batchGetPositionIfoByPositionCode(new LinkedList<>(positionCodes));
        Map<Long, IntlSoUserInfo> userInfoMap = intlSoUserInfoService.batchGetByIds(new LinkedList<>(userIds));
        Map<Long, IntlSoOrgInfo> orgInfoMap = intlSoOrgInfoService.batchGetByIdsMap(new LinkedList<>(orgIds));
        Set<String> retailerIds = new HashSet<>();
        storeMap.forEach((storeId, storeInfo) -> {
            if (storeInfo != null && storeInfo.getRetailerId() != null) {
                retailerIds.add(storeInfo.getRetailerId());
            }
        });
        Map<String, IntlRetailerDTO> retailerMap = retailerApiService.batchGetRetailerByRetailerIds(new LinkedList<>(retailerIds));
        Map<String, ProductInfoDTO> goodsMap = productApiService.batchGetByGoodIds(new LinkedList<>(goodsIds));
        batchSyncToEs(useLatestInfo, qtyList, storeMap, positionMap, userInfoMap, orgInfoMap, retailerMap, goodsMap);
    }
    
    private void batchSyncToEs(boolean useLatestInfo, List<IntlSoQty> qtyList, Map<String, RmsStoreInfoDto> storeMap, Map<String,
                    RmsPositionInfoRes> positionMap, Map<Long, IntlSoUserInfo> userInfoMap,
            Map<Long, IntlSoOrgInfo> orgInfoMap, Map<String, IntlRetailerDTO> retailerMap,
            Map<String, ProductInfoDTO> goodsMap) {
        List<SoQtyIndex> indexList = new LinkedList<>();
        qtyList.forEach(intlSoQty -> {
            // 基础字段
            SoQtyIndex.SoQtyIndexBuilder builder = SoQtyIndex.of(intlSoQty);
            // 用户信息
            SoQtyIndex.fillWithUserInfo(builder, userInfoMap.get(intlSoQty.getUserInfoId()));
            // org字段
            SoQtyIndex.fillWithOrgInfo(builder, orgInfoMap.get(intlSoQty.getOrgInfoId()));
            RmsStoreInfoDto storeInfo = storeMap.get(intlSoQty.getStoreRmsCode());
            if (storeInfo != null) {
                // 填充store其他扩展信息
                SoQtyIndex.fillWithStoreInfo(builder, storeInfo, useLatestInfo);
                // Retailer信息
                SoQtyIndex.fillWithRetailerInfo(builder, retailerMap.get(storeInfo.getRetailerId()));
            }
            // position
            SoQtyIndex.fillWithPositionInfo(builder, positionMap.get(intlSoQty.getPositionRmsCode()), useLatestInfo);
            // Product字段
            SoQtyIndex.fillWithProductInfo(builder, goodsMap.get(String.valueOf(intlSoQty.getProductCode())));
            indexList.add(builder.build());
        });
        if (indexList.size() <= 0) {
            throw new BizException("No valid data to sync to ES");
        }
        try {
            template.save(indexList, IndexCoordinates.of(indexName));
        } catch (Exception e) {
            log.error("Failed to save data to ES, error: {}", e.getMessage(), e);
            throw new BizException("Failed to save data to ES: " + e.getMessage());
        }
    }
    
    @MetricsTimer
    public void batchInsert(List<SoQtyIndex> soQtyIndexes) {
        template.save(soQtyIndexes, IndexCoordinates.of(indexName));
    }
    
    @MetricsTimer
    public void delete(List<Long> list) {
        BulkRequest bulkRequest = new BulkRequest();
        list.forEach(id ->
                bulkRequest.add(new DeleteRequest(indexName, String.valueOf(id)))
        );
        template.execute(client -> client.bulk(bulkRequest, RequestOptions.DEFAULT));
    }
    
    
    @MetricsTimer
    public PageResponse<SoQtyIndex> search(SalesQtyReqDto reqDto) {
        int esPageIndex = reqDto.getPageIndex() - 1;
        int pageSize = Math.min(reqDto.getPageSize(), MAX_PAGE_SIZE);
        
        NativeSearchQueryBuilder builder = new NativeSearchQueryBuilder()
                .withQuery(EsQueryConverter.convert(reqDto))
                .withPageable(PageRequest.of(esPageIndex, pageSize))
                // 强制计算精确总数
                .withTrackTotalHits(true)
                // 浅翻页
                .withSearchAfter(reqDto.getSearchAfter());
        if (!StringUtils.isEmpty(reqDto.getOrderBy())) {
            // 优先
            Sort sort;
            if (Sort.Direction.ASC.name().equalsIgnoreCase(reqDto.getOrderDirection())) {
                sort = Sort.by(reqDto.getOrderBy()).ascending();
            } else {
                sort = Sort.by(reqDto.getOrderBy()).descending();
            }
            builder.withSort(sort);
        }
        // 默认按ID倒序
        builder.withSort(Sort.by("id").descending());
        SearchHits<SoQtyIndex> hits = template.search(builder.build(), SoQtyIndex.class, IndexCoordinates.of(indexName));
        List<SoQtyIndex> list = hits.stream()
                .map(SearchHit::getContent)
                .collect(Collectors.toList());
        return PageResponse.of(list, (int) hits.getTotalHits(), pageSize, reqDto.getPageIndex());
    }
    
    /**
     * 查询总数
     *
     * @param reqDto
     * @return
     */
    public long count(SalesQtyReqDto reqDto) {
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(EsQueryConverter.convert(reqDto))
                .build();
        return template.count(query, SoQtyIndex.class, IndexCoordinates.of(indexName));
    }
    
    /**
     * 分组查询
     *
     * @param reqDto
     * @return
     */
    public Map<String, Long> groupByAgg(SalesQtyReqDto reqDto) {
        // 分组字段
        String groupBy = reqDto.getGroupBy();
        if (StringUtils.isBlank(groupBy)) {
            throw new IllegalArgumentException("GroupBy field is required");
        }
        
        NativeSearchQuery query = new NativeSearchQueryBuilder()
                .withQuery(EsQueryConverter.convert(reqDto))
                .withAggregations(AggregationBuilders.terms("groupByAgg").field(groupBy).size(reqDto.getPageSize()))
                .build();
        AggregationsContainer<?> aggregationsContainer = template.search(query, SoQtyIndex.class, IndexCoordinates.of(indexName)).getAggregations();
        if (aggregationsContainer == null) {
            return new HashMap<>();
        }
        Aggregations aggregations = (Aggregations) aggregationsContainer.aggregations();
        Terms terms = aggregations.get("groupByAgg");
        Map<String, Long> map = terms.getBuckets().stream()
                .collect(Collectors.toMap(Terms.Bucket::getKeyAsString, Terms.Bucket::getDocCount));
        log.info("reqDto={}, groupByAgg={}", JSON.toJSONString(reqDto), map);
        return map;
    }
    
}