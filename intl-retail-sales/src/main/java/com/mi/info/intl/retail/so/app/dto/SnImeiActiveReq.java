package com.mi.info.intl.retail.so.app.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * SN IMEI激活请求类
 * 用于查询设备激活信息的请求参数
 */
@Getter
@Setter
public class SnImeiActiveReq implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 设备序列号
     */
    private String sn;

    /**
     * SN哈希值，与sn二选其一
     */
    private String snHexIdx;

    /**
     * 是否跨集群
     */
    private Boolean cross;

    /**
     * 排除国内集群列表
     */
    private List<String> crossExcludeClusters;

}