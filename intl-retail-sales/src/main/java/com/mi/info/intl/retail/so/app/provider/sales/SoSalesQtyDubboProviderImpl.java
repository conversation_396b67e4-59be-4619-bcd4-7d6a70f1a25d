package com.mi.info.intl.retail.so.app.provider.sales;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.mi.info.intl.retail.exception.ErrorCodes;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingRoleRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.ReportingTypeRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.dto.SalesQtyRespDto;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.provider.SoSalesQtyDubboProvider;
import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
import com.mi.info.intl.retail.intlretail.util.AppProviderUtil;
import com.mi.info.intl.retail.model.CommonApiResponse;
import com.mi.info.intl.retail.so.domain.sales.service.IntlSoQtyDomainService;
import com.xiaomi.mone.dubbo.docs.annotations.ApiDoc;
import com.xiaomi.mone.dubbo.docs.annotations.ApiModule;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;


/**
 * @Project: intl-retail
 * @Description: 销量dubbo服务实现类
 * @Author: 周楚强
 * @Date: 2025-08-01
 **/

@Slf4j
@Service
@DubboService(group = "${center.dubbo.group:}", interfaceClass = SoSalesQtyDubboProvider.class)
@ApiModule(value = "国际新零售平台", apiInterface = SoSalesQtyDubboProvider.class)
public class SoSalesQtyDubboProviderImpl implements SoSalesQtyDubboProvider {
    
    @Resource
    private IntlSoQtyDomainService intlSoQtyDomainService;

    @ApiDoc(value = "/api/so/v1/salesQty/getSalesQtyForPage", description = "Qty数据分页查询")
    @Override
    public CommonApiResponse<IPage<SalesQtyRespDto>> getSalesQtyForPage(SalesQtyReqDto salesQtyReqDto) {
        if (null == salesQtyReqDto) {
            log.error("[method]getSalesQtyForPage:salesQtyReqDto is null......");
            return CommonApiResponse.failure(ErrorCodes.PARAM_IS_EMPTY.getCode(), "The parameter 'salesQtyReqDto' is empty");
        }
        return AppProviderUtil.wrap(log, "SoSalesQtyDubboProvider::getSalesQtyForPage", salesQtyReqDto,
                intlSoQtyDomainService::getSalesQtyForPage, false);
    }

    @ApiDoc(value = "/api/so/v1/salesQty/getQtyReportingRoleData", description = "Qty获取上报角色数据")
    @Override
    public CommonApiResponse<ReportingRoleRespDto> getQtyReportingRoleData(SalesQtyReqDto salesQtyReqDto) {
        return AppProviderUtil.wrap(log, "SoSalesQtyDubboProvider::getQtyReportingRoleData", salesQtyReqDto,
                intlSoQtyDomainService::getQtyReportingRoleData, false);
    }

    @ApiDoc(value = "/api/so/v1/salesQty/getQtyReportingTypeData", description = "Qty获取上报类型数据")
    @Override
    public CommonApiResponse<ReportingTypeRespDto> getQtyReportingTypeData(SalesQtyReqDto salesQtyReqDto) {
        return AppProviderUtil.wrap(log, "SoSalesQtyDubboProvider::getQtyReportingTypeData", salesQtyReqDto,
                intlSoQtyDomainService::getQtyReportingTypeData, false);
    }

    @ApiDoc(value = "/api/so/v1/salesQty/export", description = "QTY数据下载")
    @Override
    public CommonApiResponse<String> export(SalesQtyReqDto salesQtyReqDto) {
        return AppProviderUtil.wrap(log, "SoSalesQtyDubboProvider::export", salesQtyReqDto,
                intlSoQtyDomainService::export, false);
    }
}
