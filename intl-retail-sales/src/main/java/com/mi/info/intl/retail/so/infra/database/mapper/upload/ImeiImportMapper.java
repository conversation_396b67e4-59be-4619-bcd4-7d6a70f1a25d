
package com.mi.info.intl.retail.so.infra.database.mapper.upload;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.mi.info.intl.retail.so.domain.upload.entity.IntlImportLog;
import lombok.Data;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * IMEI导入相关数据库操作Mapper
 *
 * <AUTHOR>
 * @date 2025/8/8
 */
@Mapper
public interface ImeiImportMapper extends BaseMapper<IntlImportLog> {

    /**
     * 根据Store Code查询门店信息
     *
     * @param storeCode 门店编码
     * @return 门店信息
     */
    StoreInfoDto getStoreByCode(@Param("storeCode") String storeCode);


    /**
     * 查询门店下所有阵地
     *
     * @param storeCode 门店编码
     * @return 阵地列表
     */
    List<PositionInfoDto> getPositionsByStoreCode(@Param("storeCode") String storeCode);

    /**
     * 根据Product ID校验产品是否存在
     *
     * @param productId 产品ID
     * @return 是否存在
     */
    boolean validateProductByProductId(@Param("productId") String productId);

    /**
     * 根据SKU校验产品是否存在
     *
     * @param sku SKU
     * @return 是否存在
     */
    String validateProductBySku(@Param("sku") String sku);

    /**
     * 根据69 Code校验产品是否存在
     *
     * @param code69 69 Code
     * @return 是否存在
     */
    String validateProductByCode69(@Param("code69") String code69);

    /**
     * 门店信息DTO
     */
    @Data
    class StoreInfoDto {
        private String storeId;
        private String code;
        private String crssCode;
        private String name;
        private String countryShortcode;
        private String createdOn;
        private Integer stateCode;

    }

    /**
     * 阵地信息DTO
     */
    @Data
    class PositionInfoDto {
        private String positionId;
        private String code;
        private String name;
        private String typeName;
        private String createdOn;
        private Integer stateCode;
    }
}

    