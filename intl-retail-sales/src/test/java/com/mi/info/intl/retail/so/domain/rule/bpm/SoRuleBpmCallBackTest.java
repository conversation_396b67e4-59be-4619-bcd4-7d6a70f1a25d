package com.mi.info.intl.retail.so.domain.rule.bpm;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

import com.mi.info.intl.retail.exception.ErrorCodes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import com.mi.info.intl.retail.component.ComponentLocator;
import com.mi.info.intl.retail.component.convertor.ConverterFacade;
import com.mi.info.intl.retail.exception.BizException;
import com.mi.info.intl.retail.intlretail.service.api.bpm.dto.BpmCallBackParamDto;
import com.mi.info.intl.retail.intlretail.service.api.bpm.enums.BpmApproveBusinessCodeEnum;
import com.mi.info.intl.retail.intlretail.service.api.so.rule.dto.SoRuleApproveCallbackDTO;
import com.mi.info.intl.retail.so.domain.rule.aggregate.SoRuleAggregateService;
import com.mi.info.intl.retail.so.domain.rule.enums.SoRuleDetailApproveStatus;

/**
 * SoRuleBpmCallBack单元测试
 *
 * <AUTHOR>
 * @date 2025/08/25
 */
@ExtendWith(MockitoExtension.class)
class SoRuleBpmCallBackTest {

    @Mock
    private SoRuleAggregateService soRuleAggregateService;

    @Mock
    private ConverterFacade converterFacade;

    @InjectMocks
    private SoRuleBpmCallBack soRuleBpmCallBack;

    private BpmCallBackParamDto bpmCallBackParamDto;
    private SoRuleApproveCallbackDTO soRuleApproveCallbackDTO;

    @BeforeEach
    void setUp() {
        // 初始化测试数据
        bpmCallBackParamDto = new BpmCallBackParamDto();
        bpmCallBackParamDto.setBusinessKey("test-business-key");
        bpmCallBackParamDto.setComment("test comment");
        bpmCallBackParamDto.setStatus("agree");

        soRuleApproveCallbackDTO = new SoRuleApproveCallbackDTO();
        soRuleApproveCallbackDTO.setBusinessKey("test-business-key");
        soRuleApproveCallbackDTO.setComment("test comment");
    }

    @Test
    @DisplayName("获取BPM审批类型枚举 - 成功场景")
    void testGetBpmApproveTypeEnum() {
        BpmApproveBusinessCodeEnum result = soRuleBpmCallBack.getBpmApproveTypeEnum();
        assertEquals(BpmApproveBusinessCodeEnum.SO_RULE, result);
    }

    @Test
    @DisplayName("一级审批节点通过回调 - 成功场景")
    void testDoCallback_ApproveNode1Agree_Success() {
        // Given
        bpmCallBackParamDto.setTaskDefinitionKey("Activity_1ewz0u3");
        bpmCallBackParamDto.setStatus("agree");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);
            doNothing().when(soRuleAggregateService).approveRuleAgree(any(SoRuleApproveCallbackDTO.class));

            // When
            soRuleBpmCallBack.doCallback(bpmCallBackParamDto);

            // Then
            verify(converterFacade).convert(bpmCallBackParamDto, SoRuleApproveCallbackDTO.class);
            verify(soRuleAggregateService).approveRuleAgree(any(SoRuleApproveCallbackDTO.class));
            verify(soRuleAggregateService, never()).approveRuleCompleted(any());
            verify(soRuleAggregateService, never()).approveRuleRejected(any());
        }
    }

    @Test
    @DisplayName("二级审批节点通过回调 - 成功场景")
    void testDoCallback_ApproveNode2Agree_Success() {
        // Given
        bpmCallBackParamDto.setTaskDefinitionKey("Activity_0hn92dr");
        bpmCallBackParamDto.setStatus("agree");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);
            doNothing().when(soRuleAggregateService).approveRuleCompleted(any(SoRuleApproveCallbackDTO.class));

            // When
            soRuleBpmCallBack.doCallback(bpmCallBackParamDto);

            // Then
            verify(converterFacade).convert(bpmCallBackParamDto, SoRuleApproveCallbackDTO.class);
            verify(soRuleAggregateService).approveRuleCompleted(any(SoRuleApproveCallbackDTO.class));
            verify(soRuleAggregateService, never()).approveRuleAgree(any());
            verify(soRuleAggregateService, never()).approveRuleRejected(any());
        }
    }

    @Test
    @DisplayName("审批拒绝回调 - 成功场景")
    void testDoCallback_Reject_Success() {
        // Given
        bpmCallBackParamDto.setStatus("reject");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);
            doNothing().when(soRuleAggregateService).approveRuleRejected(any(SoRuleApproveCallbackDTO.class));

            // When
            soRuleBpmCallBack.doCallback(bpmCallBackParamDto);

            // Then
            verify(converterFacade).convert(bpmCallBackParamDto, SoRuleApproveCallbackDTO.class);
            verify(soRuleAggregateService).approveRuleRejected(any(SoRuleApproveCallbackDTO.class));
            verify(soRuleAggregateService, never()).approveRuleAgree(any());
            verify(soRuleAggregateService, never()).approveRuleCompleted(any());
        }
    }

    @Test
    @DisplayName("未知审批节点通过回调 - 抛出异常")
    void testDoCallback_UnknownTaskDefinitionKey_ThrowsException() {
        // Given
        bpmCallBackParamDto.setTaskDefinitionKey("unknown-node");
        bpmCallBackParamDto.setStatus("agree");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);

            // When & Then
            BizException exception = assertThrows(BizException.class, () -> soRuleBpmCallBack.doCallback(bpmCallBackParamDto));
            assertEquals(ErrorCodes.SYS_ERROR.getCode(), exception.getErrorCode().getCode());
            assertTrue(exception.getMessage().contains("Not support taskDefinitionKey : unknown-node"));
        }
    }

    @Test
    @DisplayName("未知审批状态回调 - 抛出异常")
    void testDoCallback_UnknownStatus_ThrowsException() {
        // Given
        bpmCallBackParamDto.setStatus("unknown-status");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);

            // When & Then
            BizException exception = assertThrows(BizException.class, () -> soRuleBpmCallBack.doCallback(bpmCallBackParamDto));
            assertEquals(ErrorCodes.SYS_ERROR.getCode(), exception.getErrorCode().getCode());
            assertTrue(exception.getMessage().contains("Not support status : unknown-status"));
        }
    }

    @Test
    @DisplayName("审批拒绝回调 - 设置正确的审批状态")
    void testDoCallback_Reject_SetCorrectStatus() {
        // Given
        bpmCallBackParamDto.setStatus("reject");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);
            doNothing().when(soRuleAggregateService).approveRuleRejected(any(SoRuleApproveCallbackDTO.class));

            // When
            soRuleBpmCallBack.doCallback(bpmCallBackParamDto);

            // Then
            verify(soRuleAggregateService).approveRuleRejected(
                argThat(callback -> SoRuleDetailApproveStatus.REJECTED.getValue() == callback.getApproveStatus()));
        }
    }

    @Test
    @DisplayName("审批通过回调 - 设置正确的审批状态")
    void testDoCallback_Agree_SetCorrectStatus() {
        // Given
        bpmCallBackParamDto.setTaskDefinitionKey("Activity_1ewz0u3");
        bpmCallBackParamDto.setStatus("agree");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);
            doNothing().when(soRuleAggregateService).approveRuleAgree(any(SoRuleApproveCallbackDTO.class));

            // When
            soRuleBpmCallBack.doCallback(bpmCallBackParamDto);

            // Then
            verify(soRuleAggregateService).approveRuleAgree(
                argThat(callback -> SoRuleDetailApproveStatus.APPROVED.getValue() == callback.getApproveStatus()));
        }
    }

    @Test
    @DisplayName("一级审批节点通过回调 - 设置正确的审批节点")
    void testDoCallback_ApproveNode1_SetCorrectNode() {
        // Given
        bpmCallBackParamDto.setTaskDefinitionKey("Activity_1ewz0u3");
        bpmCallBackParamDto.setStatus("agree");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);
            doNothing().when(soRuleAggregateService).approveRuleAgree(any(SoRuleApproveCallbackDTO.class));

            // When
            soRuleBpmCallBack.doCallback(bpmCallBackParamDto);

            // Then
            verify(soRuleAggregateService)
                .approveRuleAgree(argThat(callback -> SoRuleBpmCallBack.NODE1 == callback.getApproveNode()));
        }
    }

    @Test
    @DisplayName("二级审批节点通过回调 - 设置正确的审批节点")
    void testDoCallback_ApproveNode2_SetCorrectNode() {
        // Given
        bpmCallBackParamDto.setTaskDefinitionKey("Activity_0hn92dr");
        bpmCallBackParamDto.setStatus("agree");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);
            doNothing().when(soRuleAggregateService).approveRuleCompleted(any(SoRuleApproveCallbackDTO.class));

            // When
            soRuleBpmCallBack.doCallback(bpmCallBackParamDto);

            // Then
            verify(soRuleAggregateService)
                .approveRuleCompleted(argThat(callback -> SoRuleBpmCallBack.NODE2 == callback.getApproveNode()));
        }
    }

    @Test
    @DisplayName("审批拒绝回调 - 设置正确的审批节点为NODE0")
    void testDoCallback_Reject_SetCorrectNode() {
        // Given
        bpmCallBackParamDto.setStatus("reject");

        try (MockedStatic<ComponentLocator> componentLocatorMock = mockStatic(ComponentLocator.class)) {
            componentLocatorMock.when(ComponentLocator::getConverter).thenReturn(converterFacade);
            when(converterFacade.convert(any(BpmCallBackParamDto.class), eq(SoRuleApproveCallbackDTO.class)))
                .thenReturn(soRuleApproveCallbackDTO);
            doNothing().when(soRuleAggregateService).approveRuleRejected(any(SoRuleApproveCallbackDTO.class));

            // When
            soRuleBpmCallBack.doCallback(bpmCallBackParamDto);

            // Then
            verify(soRuleAggregateService)
                .approveRuleRejected(argThat(callback -> SoRuleBpmCallBack.NODE0 == callback.getApproveNode()));
        }
    }
}