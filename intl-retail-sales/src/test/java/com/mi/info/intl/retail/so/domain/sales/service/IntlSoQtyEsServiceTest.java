//package com.mi.info.intl.retail.so.domain.sales.service;
//
//import com.alibaba.cola.dto.PageResponse;
//import com.google.common.collect.Lists;
//import com.mi.info.intl.retail.api.fieldforce.retailer.IntlRetailerApiService;
//import com.mi.info.intl.retail.api.fieldforce.retailer.dto.IntlRetailerDTO;
//import com.mi.info.intl.retail.api.fieldforce.user.UserApiService;
//import com.mi.info.intl.retail.api.fieldforce.user.dto.IntlRmsUserNewDto;
//import com.mi.info.intl.retail.api.front.dto.RmsPositionInfoRes;
//import com.mi.info.intl.retail.api.front.dto.RmsStoreInfoDto;
//import com.mi.info.intl.retail.api.front.store.RmsStoreService;
//import com.mi.info.intl.retail.api.product.ProductApiService;
//import com.mi.info.intl.retail.api.product.dto.ProductInfoDTO;
//import com.mi.info.intl.retail.core.config.SyncDataThreadPoolConfig;
//import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesImeiReqDto;
//import com.mi.info.intl.retail.intlretail.service.api.so.sales.request.SalesQtyReqDto;
//import com.mi.info.intl.retail.so.domain.sales.model.SoImeiIndex;
//import com.mi.info.intl.retail.so.domain.sales.model.SoQtyIndex;
//import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoImei;
//import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoOrgInfo;
//import com.mi.info.intl.retail.so.domain.upload.entity.IntlSoQty;
//import com.mi.info.intl.retail.so.infra.database.mapper.upload.IntlSoOrgInfoMapper;
//import com.mi.info.intl.retail.utils.JsonUtil;
//import org.apache.http.HttpHost;
//import org.elasticsearch.client.RestClient;
//import org.elasticsearch.client.RestHighLevelClient;
//import org.junit.jupiter.api.Assertions;
//import org.junit.jupiter.api.BeforeEach;
//import org.junit.jupiter.api.Test;
//import org.junit.jupiter.api.extension.ExtendWith;
//import org.mockito.InjectMocks;
//import org.mockito.Mock;
//import org.mockito.Mockito;
//import org.mockito.MockitoAnnotations;
//import org.mockito.junit.jupiter.MockitoExtension;
//import org.springframework.data.elasticsearch.core.ElasticsearchRestTemplate;
//import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
//import org.springframework.test.util.ReflectionTestUtils;
//
//import java.math.BigDecimal;
//import java.util.*;
//
//import static org.mockito.ArgumentMatchers.any;
//
//
//@ExtendWith(MockitoExtension.class)
//public class IntlSoQtyEsServiceTest {
//
//    @InjectMocks
//    private IntlSoQtyEsService service;
//
//    @Mock
//    private ProductApiService productApiService;
//
//    @Mock
//    private ElasticsearchRestTemplate template;
//
//    @Mock
//    private UserApiService userApiService;
//
//    @Mock
//    private RmsStoreService rmsStoreService;
//
//    @Mock
//    private IntlRetailerApiService retailerApiService;
//
//    @Mock
//    private IntlSoOrgInfoMapper intlSoOrgInfoMapper;
//
//
//    @BeforeEach
//    void setup() {
//        MockitoAnnotations.openMocks(this);
//        ElasticsearchRestTemplate template = new ElasticsearchRestTemplate(new RestHighLevelClient(
//                RestClient.builder(new HttpHost("c3test.api.es.srv", 80, "http"))
//                        .setRequestConfigCallback(requestConfigBuilder ->
//                                requestConfigBuilder.setSocketTimeout(10000)))
//        );
//        ReflectionTestUtils.setField(service, "template", template);
//        ReflectionTestUtils.setField(service, "indexName", "intl_retail_so_qty_dev@3-000001");
//    }
//
//    //@Test
//    public void testDelete() {
//        boolean hasMore = true;
//        do {
//            SalesQtyReqDto req = SalesQtyReqDto.builder()
//                    .build();
//            req.setPageSize(500);
//            req.setPageIndex(1);
//            req.setSearchAfter(Lists.newArrayList(Integer.MAX_VALUE));
//            PageResponse<SoQtyIndex> page = service.search(req);
//            hasMore = page.getData().size() >= 500;
//            List list = new LinkedList<Long>();
//            for (SoQtyIndex index : page.getData()) {
//                list.add(index.getId());
//            }
//            service.delete(list);
//        } while (hasMore);
//    }
//
//    //@Test
//    public void testSyncDataToEs() {
//        service.delete(Arrays.asList(123l));
//        IntlSoQty qty = IntlSoQty.builder()
//                .id(123l)
//                .rrp(new BigDecimal("100.0"))
//                .rrpCode("100")
//                .currency("USD")
//                .salesTime(new Date().getTime())
//                .salesmanMid(1l)
//                .storeRmsCode("202501")
//                .createdby(1L)
//                .positionRmsCode("202")
//                .productCode(22222)
//                .storeRmsCode("rms00001")
//                .positionRmsCode("202502")
//                .createdon(new Date().getTime())
//                .modifiedby(1L)
//                .modifiedon(new Date().getTime())
//                .reportingType(1)
//                .build();
//        Mockito.when(userApiService.getUserByMiId(any())).thenReturn(Optional.of(IntlRmsUserNewDto.builder()
//                .jobId(5000001)
//                .rmsUserid("rmsUserId")
//                .build()));
//        RmsStoreInfoDto storeInfo = RmsStoreInfoDto.builder()
//                .grade(1)
//                .type(2)
//                .channelType(3)
//                .hasPc(1)
//                .hasSR(1)
//                .countryShortcode("CN")
//                .cityCode("1000000")
//                .provinceCode("pCode")
//                .provinceName("pName")
//                .build();
//        Mockito.when(rmsStoreService.getStoreInfoByStoreCode(any())).thenReturn(Optional.of(storeInfo));
//        RmsPositionInfoRes positionInfo = RmsPositionInfoRes.builder()
//                .type(2)
//                .name("positionName")
//                .channelType(3)
//                .build();
//        Mockito.when(rmsStoreService.getPositionIfoByPositionCode(any())).thenReturn(Optional.of(positionInfo));
//        IntlRetailerDTO retailerDto = IntlRetailerDTO.builder()
//                .retailerCode("retailerCode")
//                .retailerName("retailerName")
//                .build();
//        Mockito.when(retailerApiService.getRetailerByRetailerId(any())).thenReturn(retailerDto);
//        ProductInfoDTO productDto = ProductInfoDTO.builder()
//                .productLineEn("pc")
//                .productLineCode(12412l)
//                .skuName("skuName X")
//                .spuNameEn("spuName en X")
//                .build();
//        Mockito.when(productApiService.queryProductById(any())).thenReturn(productDto);
//
//        IntlSoOrgInfo orgInfo = IntlSoOrgInfo.builder()
//                .storeCode("99999")
//                .storeType(9)
//                .storeGrade(9)
//                .storeChannelType(9)
//                .storeHasPC(1)
//                .storeHasSR(1)
//                .positionCode("33333")
//                .positionRmsCode("44444")
//                .positionType(9)
//                .retailerCode("4444444")
//                .build();
//        Mockito.when(intlSoOrgInfoMapper.selectOne(any())).thenReturn(orgInfo);
//        service.syncDataToEs(qty, false);
//
//        SoQtyIndex index = service.getById(123l);
//        Assertions.assertNotNull(index);
//        //基础属性
//        Assertions.assertEquals(index.getRrp(), qty.getRrp());
//        Assertions.assertEquals(index.getRrpCode(), qty.getRrpCode());
//        Assertions.assertEquals(index.getCurrency(), qty.getCurrency());
//        Assertions.assertEquals(index.getSalesTime(), qty.getSalesTime());
//        Assertions.assertEquals(index.getSalesmanMid(), qty.getSalesmanMid());
//        Assertions.assertEquals(index.getStoreRmsCode(), qty.getStoreRmsCode());
//        Assertions.assertEquals(index.getCreatedBy(), qty.getCreatedby());
//        Assertions.assertEquals(index.getPositionRmsCode(), qty.getPositionRmsCode());
//        Assertions.assertEquals(index.getCreatedOn(), qty.getCreatedon());
//        Assertions.assertEquals(index.getReportingType(), qty.getReportingType());
//        Assertions.assertEquals(index.getModifiedBy(), qty.getModifiedby());
//        Assertions.assertEquals(index.getModifiedOn(), qty.getModifiedon());
//
//        // 从Orginfo取值的属性
//        Assertions.assertEquals(index.getStoreCode(), orgInfo.getStoreCode());
//        Assertions.assertEquals(index.getStoreType(), orgInfo.getStoreType());
//        Assertions.assertEquals(index.getStoreGrade(), orgInfo.getStoreGrade());
//        Assertions.assertEquals(index.getStoreChannelType(), orgInfo.getStoreChannelType());
//        Assertions.assertEquals(index.getStoreHasPc(), orgInfo.getStoreHasPC());
//        Assertions.assertEquals(index.getStoreHasSr(), orgInfo.getStoreHasSR());
//        Assertions.assertEquals(index.getPositionCode(), orgInfo.getPositionCode());
//        Assertions.assertEquals(index.getPositionType(), orgInfo.getPositionType());
//        Assertions.assertEquals(index.getRetailerCode(), orgInfo.getRetailerCode());
//
//
//        // 从store取值的属性
//        Assertions.assertEquals(index.getCountryShortcode(), storeInfo.getCountryShortcode());
//        Assertions.assertEquals(index.getCountryName(), storeInfo.getCountryIdName());
//        Assertions.assertEquals(index.getProvinceCode(), storeInfo.getProvinceCode());
//        Assertions.assertEquals(index.getProvinceName(), storeInfo.getProvinceName());
//        Assertions.assertEquals(index.getCityCode(), storeInfo.getCityCode());
//        Assertions.assertEquals(index.getCityName(), storeInfo.getCityName());
//        Assertions.assertEquals(index.getDistrictOrgCode(), storeInfo.getDistrictOrgCode());
//        Assertions.assertEquals(index.getDivisionOrgCode(), storeInfo.getDivisionOrgCode());
//        Assertions.assertEquals(index.getAreaOrgCode(), storeInfo.getAreaOrgCode());
//        Assertions.assertEquals(index.getRegionOrgCode(), storeInfo.getRegionOrgCode());
//        Assertions.assertEquals(index.getCountryOrgCode(), storeInfo.getCountryOrgCode());
//
//        // 从Position取值的属性
//        Assertions.assertEquals(index.getPositionName(), positionInfo.getName());
//
//        Assertions.assertEquals(index.getRetailerName(), retailerDto.getRetailerName());
//
//        // 产品属性核对
//        Assertions.assertEquals(index.getSkuName(), productDto.getSkuName());
//        Assertions.assertEquals(index.getSpuNameEn(), productDto.getSpuNameEn());
//        Assertions.assertEquals(index.getProductLineEn(), productDto.getProductLineEn());
//        Assertions.assertEquals(index.getProductLineCode(), productDto.getProductLineCode());
//
//        qty.setStatus(99);
//        Long now = new Date().getTime();
//        qty.setModifiedon(now);
//        qty.setModifiedby(99999L);
//
//
//        // 只更新基础字段，不强制使用最新值
//        service.syncDataToEs(qty,false);
//        index = service.getById(123l);
//        Assertions.assertNotNull(index);
//        Assertions.assertEquals(index.getStatus(), qty.getStatus());
//        Assertions.assertEquals(index.getModifiedOn(), qty.getModifiedon());
//        Assertions.assertEquals(index.getModifiedBy(), qty.getModifiedby());
//        //此时应该还是旧值
//        Assertions.assertEquals(index.getStoreCode(), orgInfo.getStoreCode());
//        Assertions.assertEquals(index.getStoreType(), orgInfo.getStoreType());
//        Assertions.assertEquals(index.getStoreGrade(), orgInfo.getStoreGrade());
//        Assertions.assertEquals(index.getStoreChannelType(), orgInfo.getStoreChannelType());
//        Assertions.assertEquals(index.getStoreHasPc(), orgInfo.getStoreHasPC());
//        Assertions.assertEquals(index.getStoreHasSr(), orgInfo.getStoreHasSR());
//        Assertions.assertEquals(index.getPositionType(), orgInfo.getPositionType());
//        Assertions.assertEquals(index.getPositionCode(), orgInfo.getPositionCode());
//        Assertions.assertEquals(index.getRetailerCode(), orgInfo.getRetailerCode());
//
///*        Assertions.assertEquals(index.getCountryShortcode(), storeInfo.getCountryShortcode());
//        Assertions.assertEquals(index.getCountryName(), storeInfo.getCountryIdName());
//        Assertions.assertEquals(index.getProvinceCode(), storeInfo.getProvinceCode());
//        Assertions.assertEquals(index.getProvinceName(), storeInfo.getProvinceName());*/
//        storeInfo.setCode("codeNew");
//        storeInfo.setName("nameNew");
//        positionInfo.setCode("codeNew");
//        positionInfo.setName("nameNew");
//        retailerDto.setRetailerName("nameNew");
//        retailerDto.setRetailerCode("codeNew");
//        Mockito.when(rmsStoreService.getStoreInfoByStoreCode(any())).thenReturn(Optional.of(storeInfo));
//        Mockito.when(rmsStoreService.getPositionIfoByPositionCode(any())).thenReturn(Optional.of(positionInfo));
//        // 强制使用最新字段
//        service.syncDataToEs(qty,true);
//        index = service.getById(123l);
//        // 对比最新值
//        Assertions.assertEquals(index.getStoreCode(), storeInfo.getCode());
//        Assertions.assertEquals(index.getStoreName(), storeInfo.getName());
//        Assertions.assertEquals(index.getStoreType(), storeInfo.getType());
//        Assertions.assertEquals(index.getStoreGrade(), storeInfo.getGrade());
//        Assertions.assertEquals(index.getStoreChannelType(), storeInfo.getChannelType());
//        Assertions.assertEquals(index.getStoreHasPc(), storeInfo.getHasPc());
//        Assertions.assertEquals(index.getStoreHasSr(), storeInfo.getHasSR());
//        Assertions.assertEquals(index.getCountryShortcode(), storeInfo.getCountryShortcode());
//        Assertions.assertEquals(index.getCountryName(), storeInfo.getCountryIdName());
//        Assertions.assertEquals(index.getProvinceCode(), storeInfo.getProvinceCode());
//        Assertions.assertEquals(index.getProvinceName(), storeInfo.getProvinceName());
//        Assertions.assertEquals(index.getDistrictOrgCode(), storeInfo.getDistrictOrgCode());
//        Assertions.assertEquals(index.getDivisionOrgCode(), storeInfo.getDivisionOrgCode());
//        Assertions.assertEquals(index.getAreaOrgCode(), storeInfo.getAreaOrgCode());
//        Assertions.assertEquals(index.getRegionOrgCode(), storeInfo.getRegionOrgCode());
//        Assertions.assertEquals(index.getCountryOrgCode(), storeInfo.getCountryOrgCode());
//        service.delete(Arrays.asList(123l));
//    }
//
//
//    public void initData() {
//        List<SoQtyIndex> list = new ArrayList<SoQtyIndex>(100);
//        for (int i = 0; i < 100; i++) {
//            SoQtyIndex index = SoQtyIndex.builder()
//                    .id(1L + i)
//                    .rrp(new BigDecimal(100))
//                    .rrpCode("100")
//                    .currency("USD")
//                    // activation
//                    //store\position
//                    .salesTime(new Date().getTime())
//                    .salesmanMid(1l + i)
//                    .salesmanJobtitle(5000001)
//                    .storeCode("50001")
//                    .createdBy(1L)
//                    .retailerCode("60001")
//                    .positionCode("70001")
//                    .productCode(22222)
//                    .storeRmsCode("80001")
//                    .positionRmsCode("rms00001")
//                    .storeGrade(1)
//                    .storeType(1)
//                    .storeChannelType(1)
//                    .storeHasPc(1)
//                    .storeHasSr(1)
//                    .positionType(1)
//                    .countryShortcode("CN")
//                    .cityCode("1000000")
//                    .createdOn(new Date().getTime())
//                    .modifiedBy(1L)
//                    .modifiedOn(new Date().getTime())
//                    .reportingType(1)
//                    .regionOrgCode("001")
//                    .areaOrgCode("002")
//                    .countryOrgCode("003")
//                    .divisionOrgCode("004")
//                    .districtOrgCode("005")
//                    .build();
//            list.add(index);
//        }
//        service.batchInsert(list);
//        Assertions.assertTrue(true);
//    }
//
//    private void dropData(){
//        List<Long> ids = new ArrayList<>(100);
//        for (int i = 0; i < 100; i++) {
//            ids.add(1L + i);
//        }
//        service.delete(ids);
//    }
//
//
//    //@Test
//    public void TestQuery() {
//        SalesQtyReqDto req = SalesQtyReqDto.builder()
//                //.id(12334464l)
//                //.salesmanMid(80l)
//                .storeGradeList(Arrays.asList(1, 2, 3))
//                .salesmanJobtitleList(Arrays.asList(1))
//                //.productCode(21421421l)
//                //.storeTypeList(Arrays.asList(2,3))
//                .build();
//        req.setPageSize(30);
//        req.setPageIndex(0);
//        req.setSearchAfter(Lists.newArrayList(52338041));
//        PageResponse<SoQtyIndex> page = service.search(req);
//        for (SoQtyIndex index : page.getData()) {
//            System.out.println(JsonUtil.bean2json(index));
//        }
//        Assertions.assertTrue(true);
//    }
//
//    //@Test
//    public void TestGroupByAgg() {
//        SalesQtyReqDto req = SalesQtyReqDto.builder()
//                //.id(12334464l)
//                //.salesmanMid(80l)
//                //.salesmanJobtitleList(Arrays.asList(1))
//                .storeGradeList(Arrays.asList(1, 2, 3))
//                //.productCode(21421421l)
//                //.storeTypeList(Arrays.asList(2,3))
//                .build();
//        req.setGroupBy("salesmanMid");
//        Map<String, Long> map = service.groupByAgg(req);
//        System.out.println(map);
//        Assertions.assertTrue(true);
//    }
//
//    //@Test
//    public void TestUpdate() {
//        for (int i = 0; i < 100; i++) {
//            SoQtyIndex index = SoQtyIndex.builder()
//                    .id(52337945l)
//                    .rrp(new BigDecimal(100).add(new BigDecimal(i)))
//                    .rrpCode("100")
//                    .currency("USD")
//                    .salesTime(1690000000L)
//                    .salesmanMid(1l + i)
//                    .storeCode("1000000000000000000000000000000")
//                    .createdBy(1L)
//                    .retailerCode("1000000000000000000000")
//                    .positionCode("1000000000000000000000000000000")
//                    .productCode(8888)
//                    .storeRmsCode("1000000000000000000000000000000")
//                    .positionRmsCode("10000000000000000")
//                    .storeGrade(1)
//                    .storeType(1)
//                    .storeChannelType(1)
//                    .storeHasPc(1)
//                    .storeHasSr(1)
//                    .positionType(1)
//                    .countryShortcode("CN")
//                    .cityCode("1000000")
//                    .createdOn(1690000000L + i)
//                    .modifiedBy(1L)
//                    .modifiedOn(new Date().getTime())
//                    .reportingType(1)
//                    .build();
//            // service.update(index);
//        }
//        Assertions.assertTrue(true);
//
//    }
//
//}
